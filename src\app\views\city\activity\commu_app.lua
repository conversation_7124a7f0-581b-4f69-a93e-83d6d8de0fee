-- @date 2024-5-9
-- @desc 社区app

local ActivityCommuAppView = class("ActivityCommuAppView", Dialog)

ActivityCommuAppView.RESOURCE_FILENAME = "activity_commu_app.json"
ActivityCommuAppView.RESOURCE_BINDING = {
	["btnClose"] = {
		varname = "btnClose",
		binds = {
			event = "touch",
			methods = {ended = bindHelper.self("onClose")}
		},
	},
    ["btn"] = {
    	varname = "btn",
		binds = {
			event = "touch",
			clicksafe = true,
			methods = {ended = bindHelper.self("onBtnClick")},
		},
	},
    ["claimed"] = "claimed",
    ["list"] = "list",
}

function ActivityCommuAppView:onCreate(activityID)
	Dialog.onCreate(self, {blackType = 1})
	self.activityID = activityID
	self.yyhuodongs = gGameModel.role:getIdler("yyhuodongs")
	idlereasy.when(self.yyhuodongs, function(_, yyhuodongs)
		local yyData = yyhuodongs[self.activityID] or {}
		local yyCfg = csv.yunying.yyhuodong[self.activityID]
		self.yyCfg = yyCfg
		local huodongID = yyCfg.huodongID
		for k, v in csvPairs(csv.yunying.generaltask) do
			if v.huodongID == huodongID then
				self.csvID = k
				local stamps = yyData.stamps or {}
				uiEasy.createItemsToList(self, self.list, v.award, {margin = 20, scale = 1, padding = 35})
				self.isGain = stamps[k] ~= 1
				self.claimed:visible(self.isGain)
				break
			end
		end
	end)
	local dx = matchLanguage({"tw"}) and -60 or 0
	local dy = matchLanguage({"tw"}) and 30 or 0
	self.list:x(self.list:x() + 20 + dx)
	self.claimed:x(self.claimed:x() - 50  + dx)
	self.btn:x(self.btn:x() + dx)
	self.btn:y(self.btn:y() + dy)
end

function ActivityCommuAppView:onBtnClick()
	if self.yyCfg.clientParam.url then
		cc.Application:getInstance():openURL("https://dto.redfoxgames.vn/dl.html")
	end
	if self.isGain then
		return
	end
	gGameApp:requestServer("/game/yy/award/get",function (tb)
		gGameUI:showGainDisplay(tb)
	end, self.activityID, self.csvID)
end

return ActivityCommuAppView
