{"textures": [], "designHeight": 1440, "name": null, "designWidth": 2560, "texturesPng": [], "classname": null, "animation": {"classname": null, "actionlist": [], "name": "AnimationManager"}, "widgetTree": {"classname": "Panel", "options": {"vectorX": 0, "bgStartColorB": 255, "touchAble": false, "bgColorG": 200, "height": 1440, "classname": "Panel", "visible": true, "ignoreSize": false, "__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "adaptScreen": false, "bgColorR": 150, "useMergedTexture": false, "capInsetsX": 0, "colorB": 255, "colorG": 255, "flipY": false, "layoutType": 0, "scaleX": 1, "scaleY": 1, "bgEndColorR": 150, "width": 2560, "colorR": 255, "ZOrder": 0, "capInsetsY": 0, "layoutParameter": null, "bgEndColorG": 200, "capInsetsHeight": 1, "bgEndColorB": 255, "opacity": 255, "vectorY": -0.5, "customProperty": "", "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "flipX": false, "backGroundScale9Enable": false, "actiontag": -1, "bgColorB": 255, "backGroundImageData": null, "backGroundImage": null, "anchorPointY": 0.5, "anchorPointX": 0.5, "rotation": 0, "sizeType": 0, "colorType": 1, "bgColorOpacity": 0, "name": "Panel", "positionPercentX": 0, "positionPercentY": 0, "sizePercentX": 1, "sizePercentY": 1, "bgStartColorG": 255, "tag": 3, "positionType": 0, "clipAble": false, "y": 720, "x": 1280, "bgStartColorR": 255, "capInsetsWidth": 1}, "name": null, "children": [{"classname": "ImageView", "options": {"fileNameData": {"resourceType": 0, "path": "activity/commu_app/bg.png", "plistFile": ""}, "touchAble": true, "scale9Height": 720, "height": 720, "classname": "ImageView", "visible": true, "scale9Width": 1280, "ignoreSize": true, "__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "useMergedTexture": false, "colorB": 255, "colorG": 255, "flipY": false, "scaleX": 1.5, "scaleY": 1.5, "width": 1280, "colorR": 255, "ZOrder": 1, "capInsetsY": 0, "layoutParameter": null, "capInsetsX": 0, "capInsetsHeight": 1, "opacity": 255, "fileName": null, "customProperty": "", "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "flipX": false, "actiontag": 48915641, "scale9Enable": false, "anchorPointY": 0.5, "anchorPointX": 0.5, "rotation": 0, "sizeType": 0, "name": "bg", "positionPercentX": 0, "positionPercentY": 0, "sizePercentX": 0.46875, "sizePercentY": 0.4861111, "tag": 2864, "positionType": 0, "y": 0, "x": 0, "capInsetsWidth": 1}, "name": null, "children": []}, {"classname": "<PERSON><PERSON>", "options": {"textColorG": 255, "fontName": "font/youmi.ttf", "fontSize": 14, "touchAble": true, "text": "", "scale9Height": 61, "height": 61, "classname": "<PERSON><PERSON>", "visible": true, "scale9Width": 58, "ignoreSize": true, "__type": "ButtonSurrogate:#EditorCommon.JsonModel.Component.GUI", "useMergedTexture": false, "colorB": 255, "colorG": 255, "flipY": false, "scaleX": 1.3, "scaleY": 1.3, "width": 58, "colorR": 255, "ZOrder": 5, "capInsetsY": 0, "layoutParameter": null, "capInsetsX": 0, "capInsetsHeight": 1, "pressed": null, "opacity": 255, "normal": null, "customProperty": "", "classType": "CocoStudio.EngineAdapterWrap.CSButton", "flipX": false, "textColorR": 255, "actiontag": 66801186, "scale9Enable": false, "pressedData": {"resourceType": 0, "path": null, "plistFile": null}, "anchorPointY": 0.5, "anchorPointX": 0.5, "rotation": 0, "sizeType": 0, "disabledData": {"resourceType": 0, "path": null, "plistFile": null}, "textColorB": 255, "disabled": null, "normalData": {"resourceType": 0, "path": "3q/event_meo/btn_exit.png", "plistFile": ""}, "name": "btnClose", "positionPercentX": 0.35546875, "positionPercentY": 0.3361111, "sizePercentX": 0.02265625, "sizePercentY": 0.04236111, "fontType": 0, "tag": 3169, "positionType": 0, "y": 484, "x": 910, "capInsetsWidth": 1}, "name": null, "children": []}, {"classname": "ImageView", "options": {"fileNameData": {"resourceType": 0, "path": "activity/tiktok/box2.png", "plistFile": ""}, "touchAble": false, "scale9Height": 215, "height": 215, "classname": "ImageView", "visible": false, "scale9Width": 829, "ignoreSize": false, "__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "useMergedTexture": false, "colorB": 255, "colorG": 255, "flipY": false, "scaleX": 1, "scaleY": 1, "width": 829, "colorR": 255, "ZOrder": 3, "capInsetsY": 72, "layoutParameter": null, "capInsetsX": 72, "capInsetsHeight": 1, "opacity": 255, "fileName": null, "customProperty": "", "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "flipX": false, "actiontag": 17026711, "scale9Enable": true, "anchorPointY": 0.5, "anchorPointX": 0.5, "rotation": 0, "sizeType": 0, "name": "listBg", "positionPercentX": 0.095703125, "positionPercentY": -0.08958333, "sizePercentX": 0.323828131, "sizePercentY": 0.149305552, "tag": 727809594, "positionType": 0, "y": -129, "x": 245, "capInsetsWidth": 1}, "name": null, "children": []}, {"classname": "ListView", "options": {"vectorX": 0, "bgStartColorB": 255, "touchAble": false, "bgColorG": 150, "height": 200, "classname": "ListView", "visible": true, "ignoreSize": false, "__type": "ListViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "innerWidth": 0, "bgColorR": 150, "useMergedTexture": false, "capInsetsX": 0, "editorClipAble": false, "bounceEnable": false, "colorB": 255, "colorG": 255, "flipY": false, "gravity": 5, "scaleX": 1, "scaleY": 1, "bgEndColorR": 150, "width": 800, "innerHeight": 0, "colorR": 255, "ZOrder": 4, "capInsetsY": 0, "layoutParameter": null, "bgEndColorG": 150, "capInsetsHeight": 1, "bgEndColorB": 255, "opacity": 255, "vectorY": -0.5, "direction": 2, "customProperty": "", "classType": "CocoStudio.EngineAdapterWrap.CSListView", "flipX": false, "backGroundScale9Enable": false, "actiontag": 11566066, "bgColorB": 255, "backGroundImageData": null, "backGroundImage": null, "anchorPointY": 0, "anchorPointX": 0, "rotation": 0, "sizeType": 0, "colorType": 0, "bgColorOpacity": 100, "name": "list", "positionPercentX": -0.0597656257, "positionPercentY": -0.160416663, "sizePercentX": 0.3125, "sizePercentY": 0.1388889, "bgStartColorG": 255, "tag": 2905, "positionType": 0, "clipAble": true, "itemMargin": 20, "y": -231, "x": -153, "bgStartColorR": 255, "capInsetsWidth": 1}, "name": null, "children": []}, {"classname": "ImageView", "options": {"fileNameData": {"resourceType": 0, "path": "3q/achievement/logo_ylq.png", "plistFile": ""}, "touchAble": false, "scale9Height": 65, "height": 65, "classname": "ImageView", "visible": true, "scale9Width": 93, "ignoreSize": true, "__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "useMergedTexture": false, "colorB": 255, "colorG": 255, "flipY": false, "scaleX": 1.5, "scaleY": 1.5, "width": 93, "colorR": 255, "ZOrder": 5, "capInsetsY": 0, "layoutParameter": null, "capInsetsX": 0, "capInsetsHeight": 1, "opacity": 255, "fileName": null, "customProperty": "", "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "flipX": false, "actiontag": 355977, "scale9Enable": false, "anchorPointY": 0.5, "anchorPointX": 0.5, "rotation": 0, "sizeType": 0, "name": "claimed", "positionPercentX": 0.217968747, "positionPercentY": -0.0340277776, "sizePercentX": 0.0363281257, "sizePercentY": 0.0451388881, "tag": 727809590, "positionType": 0, "y": -49, "x": 558, "capInsetsWidth": 1}, "name": null, "children": []}, {"classname": "Panel", "options": {"vectorX": 0, "bgStartColorB": 255, "touchAble": true, "bgColorG": 200, "height": 150, "classname": "Panel", "visible": true, "ignoreSize": false, "__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "adaptScreen": false, "bgColorR": 150, "useMergedTexture": false, "capInsetsX": 0, "colorB": 255, "colorG": 255, "flipY": false, "layoutType": 0, "scaleX": 1, "scaleY": 1, "bgEndColorR": 150, "width": 500, "colorR": 255, "ZOrder": 10, "capInsetsY": 0, "layoutParameter": null, "bgEndColorG": 200, "capInsetsHeight": 1, "bgEndColorB": 255, "opacity": 255, "vectorY": -0.5, "customProperty": "", "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "flipX": false, "backGroundScale9Enable": false, "actiontag": 16052282, "bgColorB": 255, "backGroundImageData": null, "backGroundImage": null, "anchorPointY": 0.5, "anchorPointX": 0.5, "rotation": 0, "sizeType": 0, "colorType": 0, "bgColorOpacity": 100, "name": "btn", "positionPercentX": 0.07773437, "positionPercentY": -0.302083343, "sizePercentX": 0.1953125, "sizePercentY": 0.104166664, "bgStartColorG": 255, "tag": 727833983, "positionType": 0, "clipAble": false, "y": -435, "x": 199, "bgStartColorR": 255, "capInsetsWidth": 1}, "name": null, "children": [{"classname": "ImageView", "options": {"fileNameData": {"resourceType": 0, "path": "activity/commu_app/btn.png", "plistFile": ""}, "touchAble": false, "scale9Height": 70, "height": 70, "classname": "ImageView", "visible": true, "scale9Width": 295, "ignoreSize": true, "__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "useMergedTexture": false, "colorB": 255, "colorG": 255, "flipY": false, "scaleX": 1.5, "scaleY": 1.5, "width": 295, "colorR": 255, "ZOrder": 0, "capInsetsY": 61, "layoutParameter": null, "capInsetsX": 106, "capInsetsHeight": 1, "opacity": 255, "fileName": null, "customProperty": "", "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "flipX": false, "actiontag": 12946034, "scale9Enable": false, "anchorPointY": 0.5, "anchorPointX": 0.5, "rotation": 0, "sizeType": 0, "name": "icon", "positionPercentX": 0, "positionPercentY": 0, "sizePercentX": 0.8, "sizePercentY": 0.813333333, "tag": 727833984, "positionType": 0, "y": 0, "x": 0, "capInsetsWidth": 1}, "name": null, "children": []}, {"classname": "Label", "options": {"touchScaleEnable": false, "areaHeight": 0, "fontName": "font/youmi1.ttf", "fontSize": 50, "touchAble": false, "text": "前往填写>>", "height": 50, "classname": "Label", "visible": false, "ignoreSize": true, "__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "hAlignment": 0, "useMergedTexture": false, "fontFile": null, "colorB": 237, "colorG": 252, "flipY": false, "scaleX": 1, "scaleY": 1, "width": 250, "colorR": 255, "ZOrder": 0, "layoutParameter": null, "opacity": 255, "vAlignment": 0, "customProperty": "", "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "flipX": false, "actiontag": 38400506, "anchorPointY": 0.5, "anchorPointX": 0.5, "rotation": 0, "sizeType": 0, "name": "txt", "positionPercentX": 0, "positionPercentY": 0, "sizePercentX": 0.5, "sizePercentY": 0.333333343, "tag": 727833986, "positionType": 0, "areaWidth": 0, "y": 0, "x": 0}, "name": null, "children": []}]}]}, "version": "*******", "dataScale": 1}