csvpath = '../../OkamiTool/csv_dev/'
luapath = '../../src/config/'
tmppath = 'lua_run/'
csv_out = 'csv_out/'
error_log = 'error.txt'

copy_to_build = True

lualist = [
   "skill","fragments"
]
# unlock
# world_map
# new_guide
# draw_items_lib
# endless_tower_scene
# skill_process
# huodong
# pvpandpve
# items
# buff
# cards
# draw_card_up_group
# effect_event
# equips
# language
# mail
# monster_scenes
# role_figure
# role_logo
# scene_conf
# scene_monster_story
# skill
# title
# unit
# notice
# pokedex
# recharges
# signin
# talent
# talent_tree
# tasks
# test_battle
# vip_desc
# card_star_effect
# combat_manual
# fetter
# grow_guide
# note
# stage/emeng
# stage/lubu
# yunying/yyhuodong
# yunying/generaltask
# base_attribute\nature_matrix
# held_item\items
lualist_ignore = [
        "csv",
        "zawake/levels",
]

is_convert_all = False # True: convert all in luapath, False: convert only files in lualist

import re
import os
import chardet

def minify_lua(file_path):
    with open(file_path, 'r', encoding='utf-8') as file:
        lua_code = file.read()
    lua_code = re.sub(r', __size = \d+', '', lua_code) 
    lua_code = re.sub(r', \[\'__size\'\] = \d+', '', lua_code) 
    lua_code = re.sub(r'__size = \d+,', '', lua_code) 
    lua_code = re.sub(r'__size = \d+', '', lua_code) 
    lua_code = re.sub( r'return csv\..*$', '', lua_code) 
    lua_code = re.sub( r"csv\['[^']+'\](?:\['[^']+'\])*", 'local a', lua_code)
    lua_code = re.sub( r"csv\['[^']+'\](?:\['[^']+'\])*", 'local a', lua_code)
    lua_code = lua_code.replace("= {[1] =", "= {[1205962024] =")
    lua_code = lua_code.replace("\\n", "@nnn@")

    return lua_code
def detect_encoding(file_path):
    with open(file_path, 'rb') as f:
        raw_data = f.read()
        result = chardet.detect(raw_data)
        encoding = result['encoding']
        confidence = result['confidence']
        return encoding
def read_csv_headers(csv_file_path):
    print(csv_file_path)
    try:
        with open(csv_file_path, 'r', newline='', encoding=detect_encoding(csv_file_path)) as csvfile:
            csv_rows = []
            for row in csvfile:
                csv_row = row.strip().split(',')
                csv_rows.append(csv_row)
            csv_rows = csv_rows[:3]
            max_length = max(len(row) for row in csv_rows)
            csv_rows = [row + [''] * (max_length - len(row)) for row in csv_rows]

            csv_header = csv_rows[0]
            csv_header2 = csv_rows[1]
            csv_header3 = csv_rows[2]

            return [csv_header, csv_header2, csv_header3]
    except FileNotFoundError:
        print(f"Không tìm thấy tệp: {csv_file_path}")
        return []