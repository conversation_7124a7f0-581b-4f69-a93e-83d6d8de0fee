-- @Date:   2023-03-13
-- @Desc:   特惠物匣选择物品

local ActivityCustomizeGiftSelectDialog = require "app.views.city.activity.customize_gift_select"
local ActivityPreferentialSelectView = class("ContestBetBetView", ActivityCustomizeGiftSelectDialog)

local function bindIcon(list, node, data, touch)
	bind.extend(list, node , {
		class = "icon_key",
		props = {
			data = data,
			onNode = function(panel)
				panel:setTouchEnabled(touch)
			end
		},
	})
end

ActivityPreferentialSelectView.RESOURCE_FILENAME = "activity_preferential_goods_select.json"
ActivityPreferentialSelectView.RESOURCE_BINDING = clone(rawget(ActivityCustomizeGiftSelectDialog, "RESOURCE_BINDING"))
ActivityPreferentialSelectView.RESOURCE_BINDING["tip1"] = "tip1"
ActivityPreferentialSelectView.RESOURCE_BINDING["bg"] = "bg"
ActivityPreferentialSelectView.RESOURCE_BINDING["title"] = "title"

function ActivityPreferentialSelectView:onCreate(params)
	self.activityId = params.activityId
	local val = params.val
	self:initModel(params.data, params.slotNums, val.optionSlotNum, val.showAwards, params.choose)
	self:initUI()
	Dialog.onCreate(self)
end

function ActivityPreferentialSelectView:initUI()
	local cfg = csv.yunying.yyhuodong[self.activityId]
	if cfg and cfg.clientParam and cfg.clientParam.res then
		local res = cfg.clientParam.res
		self.bg:texture(string.format("%s/%s",res,"box_thwx.png"))
		self.title:texture(string.format("%s/%s",res,"txt_zxwp.png"))
	end
end

function ActivityPreferentialSelectView:btnState(isOnlyNowSelect)
	self.btn:setTouchEnabled(not isOnlyNowSelect)
	uiEasy.setBtnShader(self.btn, self.btn:get("label"), isOnlyNowSelect and 2 or 1)
end

return ActivityPreferentialSelectView