{"classname": null, "name": null, "animation": {"classname": null, "name": "AnimationManager", "actionlist": []}, "dataScale": 1, "designHeight": 1440, "designWidth": 2560, "textures": [], "texturesPng": ["3q/tanthu_7ngay/bg_frame.png", "activity/server_open/img_title1.png", "activity/server_open/img_lefttitle_2.png", "3q/sukien_30_4/image_gold.png", "activity/server_open/img_lefttitle_3.png", "activity/server_open/img_lefttitle_4.png", "3q/tanthu_7ngay/btn_dayactive.png", "3q/common/pic_suo_09.png", "3q/tanthu_7ngay/btn_daynormal.png", "activity/server_open/banner_jnh1.png", "activity/server_open/banner_jnh.png", "activity/server_open/txt_xyjl.png", "login/box_tagline.png", "3q/tanthu_7ngay/btn_renwu_xuanzhong.png", "3q/tanthu_7ngay/btn_renwu_weixuanzhong.png", "activity/server_open/img_db.png", "common/btn/btn_hd_close.png", "3q/tanthu_7ngay/btn_anniu.png", "3q/tanthu_7ngay/bg_02.png", "3q/tanthu_7ngay/bg_shadow_player.png", "3q/tanthu_7ngay/front.png", "3q/sukien_30_4/bg_main.png", "activity/server_open/double_years_day/img_sd_1.png", "activity/server_open/img_lefttitle_2.png", "3q/sukien_30_4/image_gold.png", "activity/server_open/img_lefttitle_3.png", "activity/server_open/img_lefttitle_4.png", "3q/sukien_30_4/btn_tap_01.png", "3q/common/pic_suo_yeqian.png", "3q/sukien_30_4/btn_tap_02.png", "activity/server_open/double_years_day/banner_sd.png", "login/box_tagline.png", "3q/sukien_30_4/btn_02_onl.png", "3q/sukien_30_4/btn_02_off.png", "3q/sukien_30_4/img_box.png", "3q/sukien_30_4/btn_03.png", "GUI/image.png", "common/btn/btn_hd_close.png", "3q/sukien_30_4/bg_event.png", "3q/sukien_30_4/image_gold.png", "3q/sukien_30_4/img_timeline.png", "3q/sukien_30_4/img_box.png", "3q/sukien_30_4/img_timeline_active.png", "3q/sukien_30_4/bg_item.png", "activity/img_panel1.png", "activity/img_panel2.png", "3q/tanthu_7ngay/bg_shadow.png", "3q/sukien_30_4/btn_01.png", "common/icon/logo_ylq.png", "3q/tanthu_7ngay/l24.png", "3q/sukien_30_4/image_gold.png", "3q/sukien_30_4/txt_sub.png"], "version": "1.6.0.0", "widgetTree": {"classname": "Panel", "name": null, "children": [{"classname": "Panel", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "img1", "ZOrder": 0, "actiontag": 57643034, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 116, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.204705879, "positionPercentY": 0.003937008, "positionType": 0, "rotation": 0, "scaleX": 2, "scaleY": 2, "sizePercentX": 0.289411753, "sizePercentY": 0.4566929, "sizeType": 0, "tag": 2840, "touchAble": false, "useMergedTexture": false, "visible": false, "width": 492, "x": -348, "y": 1, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "activity/img_panel1.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 116, "scale9Width": 492}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "img2", "ZOrder": 0, "actiontag": 49025264, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 116, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.274117649, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 2, "scaleY": 2, "sizePercentX": 0.134705886, "sizePercentY": 0.4566929, "sizeType": 0, "tag": 2841, "touchAble": false, "useMergedTexture": false, "visible": false, "width": 229, "x": 466, "y": 0, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "activity/img_panel2.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 116, "scale9Width": 229}}], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "bg", "ZOrder": 0, "actiontag": 43687483, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 254, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.0005763689, "positionPercentY": 0.00384615385, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.9798271, "sizePercentY": 0.976923048, "sizeType": 0, "tag": 2839, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 1700, "x": -1, "y": 1, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 80, "capInsetsY": 80, "fileName": null, "fileNameData": {"path": "3q/sukien_30_4/bg_item.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 254, "scale9Width": 1700}}, {"classname": "ListView", "name": null, "children": [], "options": {"__type": "ListViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ListView", "name": "list", "ZOrder": 4, "actiontag": 38574198, "anchorPointX": 0, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSListView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 200, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.472622484, "positionPercentY": -0.3923077, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.652449548, "sizePercentY": 0.7692308, "sizeType": 0, "tag": 2842, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 1132, "x": -820, "y": -102, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 150, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 150, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "bounceEnable": false, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": true, "colorType": 0, "direction": 2, "editorClipAble": false, "gravity": 5, "innerHeight": 0, "innerWidth": 0, "itemMargin": 15, "vectorX": 0, "vectorY": -0.5}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "img", "ZOrder": 2, "actiontag": 11623470, "anchorPointX": 1, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 45, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.458789617, "positionPercentY": 0.326923072, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.3573487, "sizePercentY": 0.173076928, "sizeType": 0, "tag": 2843, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 620, "x": 796, "y": 85, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 15, "capInsetsY": 15, "fileName": null, "fileNameData": {"path": "3q/tanthu_7ngay/bg_shadow.png", "plistFile": "", "resourceType": 0}, "scale9Enable": true, "scale9Height": 45, "scale9Width": 620}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "desc", "ZOrder": 2, "actiontag": 37502367, "anchorPointX": 0, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 91, "colorG": 84, "colorR": 91, "customProperty": "", "flipX": false, "flipY": false, "height": 36, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.1648415, "positionPercentY": 0.326923072, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.0380403474, "sizePercentY": 0.138461545, "sizeType": 0, "tag": 2844, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 66, "x": 286, "y": 85, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 32, "hAlignment": 0, "text": "desc", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "num", "ZOrder": 2, "actiontag": 1976988, "anchorPointX": 1, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 86, "colorG": 196, "colorR": 96, "customProperty": "", "flipX": false, "flipY": false, "height": 41, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.441498548, "positionPercentY": 0.326923072, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.0288184434, "sizePercentY": 0.157692313, "sizeType": 0, "tag": 2845, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 50, "x": 766, "y": 85, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 36, "hAlignment": 1, "text": "0/0", "touchScaleEnable": false, "vAlignment": 1}}, {"classname": "<PERSON><PERSON>", "name": null, "children": [{"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "label", "ZOrder": 0, "actiontag": 25918306, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 184, "colorG": 250, "colorR": 254, "customProperty": "", "flipX": false, "flipY": false, "height": 57, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0.06097561, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.22012578, "sizePercentY": 0.695121944, "sizeType": 0, "tag": 2847, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 70, "x": 0, "y": 5, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 50, "hAlignment": 0, "text": "领 取", "touchScaleEnable": false, "vAlignment": 0}}], "options": {"__type": "ButtonSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "<PERSON><PERSON>", "name": "receivebtn", "ZOrder": 3, "actiontag": 17792892, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSButton", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 82, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.335668385, "positionPercentY": -0.09082841, "positionType": 0, "rotation": 0, "scaleX": 1.5, "scaleY": 1.5, "sizePercentX": 0.1832853, "sizePercentY": 0.315384626, "sizeType": 0, "tag": 2846, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 318, "x": 582, "y": -23, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 106, "capInsetsY": 0, "disabled": null, "disabledData": {"path": null, "plistFile": null, "resourceType": 0}, "fontName": "font/youmi.ttf", "fontSize": 14, "fontType": 0, "normal": null, "normalData": {"path": "3q/sukien_30_4/btn_01.png", "plistFile": "", "resourceType": 0}, "pressed": null, "pressedData": {"path": null, "plistFile": null, "resourceType": 0}, "scale9Enable": false, "scale9Height": 82, "scale9Width": 318, "text": "", "textColorB": 255, "textColorG": 255, "textColorR": 255}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "received", "ZOrder": 2, "actiontag": 31938166, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 107, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.402881831, "positionPercentY": -0.0730769262, "positionType": 0, "rotation": 0, "scaleX": 1.5, "scaleY": 1.5, "sizePercentX": 0.06167147, "sizePercentY": 0.411538452, "sizeType": 0, "tag": 2848, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 107, "x": 699, "y": -19, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "common/icon/logo_ylq.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 107, "scale9Width": 107}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "item", "ZOrder": 0, "actiontag": 42136867, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 260, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 1.09492183, "positionPercentY": -0.141666666, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.6777344, "sizePercentY": 0.180555552, "sizeType": 0, "tag": 2838, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 1735, "x": 2803, "y": -204, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "ListView", "name": null, "children": [], "options": {"__type": "ListViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ListView", "name": "list", "ZOrder": 1, "actiontag": 45752642, "anchorPointX": 0, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSListView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 815, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.0484375, "positionPercentY": -0.289583325, "positionType": 0, "rotation": 0, "scaleX": 0.68, "scaleY": 0.68, "sizePercentX": 0.67578125, "sizePercentY": 0.5659722, "sizeType": 0, "tag": 349, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 1730, "x": -124, "y": -417, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 150, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 150, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "bounceEnable": false, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": true, "colorType": 0, "direction": 1, "editorClipAble": false, "gravity": 2, "innerHeight": 0, "innerWidth": 0, "itemMargin": 4, "vectorX": 0, "vectorY": -0.5}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "Image_12", "ZOrder": 2, "actiontag": 11406208, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 162, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.180859372, "positionPercentY": -0.233333334, "positionType": 0, "rotation": 0, "scaleX": 1.12, "scaleY": 1, "sizePercentX": 0.41015625, "sizePercentY": 0.1125, "sizeType": 0, "tag": 727808544, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 1050, "x": 463, "y": -336, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/tanthu_7ngay/l24.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 162, "scale9Width": 1050}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "Image_15", "ZOrder": 4, "actiontag": 29739479, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 191, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.4265625, "positionPercentY": -0.3090278, "positionType": 0, "rotation": 0, "scaleX": 1.5, "scaleY": 1.5, "sizePercentX": 0.0796875, "sizePercentY": 0.132638887, "sizeType": 0, "tag": 727808797, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 204, "x": 1092, "y": -445, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/sukien_30_4/image_gold.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 191, "scale9Width": 204}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "Image_16", "ZOrder": 3, "actiontag": 38074530, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 67, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.178906247, "positionPercentY": 0.13611111, "positionType": 0, "rotation": 0, "scaleX": 1.6, "scaleY": 1.6, "sizePercentX": 0.272656262, "sizePercentY": 0.0465277769, "sizeType": 0, "tag": 727808798, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 698, "x": 458, "y": 196, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/sukien_30_4/txt_sub.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 67, "scale9Width": 698}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "Panel", "ZOrder": 0, "actiontag": -1, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 1440, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 1, "sizePercentY": 1, "sizeType": 0, "tag": 3, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 2560, "x": 1280, "y": 720, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 0, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 1, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}}