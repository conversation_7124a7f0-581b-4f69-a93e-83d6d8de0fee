{"classname": null, "name": null, "animation": {"classname": null, "name": "AnimationManager", "actionlist": []}, "dataScale": 1, "designHeight": 1440, "designWidth": 2560, "textures": [], "texturesPng": ["3q/privilege/bg.png", "3q/progress_bg_01.png", "3q/progress_06.png", "3q/iconvip/icon_vip1.png", "common/icon/icon_diamond.png", "3q/iconvip/icon_vip2.png", "3q/iconvip/icon_vip18.png", "3q/privilege/btn_purchase.png", "3q/privilege/banner5.png", "3q/privilege/pic.png", "common/box/box_panel_1.png", "3q/recharge/btn_chongzhi.png", "3q/recharge/icon_cz_1@.png", "common/btn/btn_normal.png", "3q/privilege/napdau.png", "3q/recharge/title_mingcheng.png", "3q/recharge/bg_title.png", "3q/privilege/platform.png", "3q/privilege/plat2.png", "3q/privilege/bg_pri.png", "3q/privilege/underline.png", "3q/recharge/line_red.png", "3q/common/pic_xuanzhong_05.png", "3q/arrow_02.png", "3q/bg_ziyuanhuishou.jpg", "3q/privilege/btnnormal.png", "3q/privilege/btnactive.png", "3q/privilege/rope.png", "3q/privilege/monthcard.png", "3q/privilege/thethuong.png", "3q/privilege/thechiton.png", "3q/common/btn_info.png", "3q/recharge/bg_chongzhi01.png", "3q/recharge/img_star.png", "3q/stat/icon_life.png", "GUI/image.png", "3q/privilege/tab_chongzhi_off.png", "3q/privilege/tab_chongzhi_on.png", "common/icon/icon_sj.png", "3q/recharge/bg_slogan.png", "3q/shop/bg_shadow.png", "3q/boss/bg_reward.png", "3q/boss/bg_reward_list.png", "3q/boss/bg_reward_title.png", "3q/common/btn_guanbi.png"], "version": "*******", "widgetTree": {"classname": "Panel", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "bg", "ZOrder": 1, "actiontag": 16430528, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 720, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 2, "scaleY": 2, "sizePercentX": 0.608984351, "sizePercentY": 0.5, "sizeType": 0, "tag": 727795793, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 1559, "x": 0, "y": 0, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/privilege/bg.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 720, "scale9Width": 1559}}, {"classname": "Panel", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "barBg", "ZOrder": 1, "actiontag": 60336444, "anchorPointX": 0, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 0, "colorG": 0, "colorR": 0, "customProperty": "", "flipX": false, "flipY": false, "height": 20, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.164864868, "positionPercentY": 0.8809091, "positionType": 0, "rotation": 0, "scaleX": 2, "scaleY": 2, "sizePercentX": 0.337837845, "sizePercentY": 0.0181818176, "sizeType": 0, "tag": 727795796, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 250, "x": -122, "y": 969, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 2, "capInsetsY": 2, "fileName": null, "fileNameData": {"path": "3q/progress_bg_01.png", "plistFile": "", "resourceType": 0}, "scale9Enable": true, "scale9Height": 20, "scale9Width": 250}}, {"classname": "LoadingBar", "name": null, "children": [], "options": {"__type": "LoadingBarSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "LoadingBar", "name": "bar", "ZOrder": 2, "actiontag": 60264286, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLoadingBar", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 18, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.172972977, "positionPercentY": 0.8809091, "positionType": 0, "rotation": 0, "scaleX": 2, "scaleY": 2, "sizePercentX": 0.337837845, "sizePercentY": 0.0163636357, "sizeType": 0, "tag": 727795799, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 250, "x": 128, "y": 969, "capInsetsHeight": 2, "capInsetsWidth": 2, "capInsetsX": 11, "capInsetsY": 0, "direction": 0, "percent": 55, "scale9Enable": true, "texture": null, "textureData": {"path": "3q/progress_06.png", "plistFile": "", "resourceType": 0}}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "barNum", "ZOrder": 3, "actiontag": 63786899, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 237, "colorG": 252, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 45, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.136486486, "positionPercentY": 0.88, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.0743243247, "sizePercentY": 0.04090909, "sizeType": 0, "tag": 727795801, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 55, "x": 101, "y": 968, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 40, "hAlignment": 0, "text": "0/0", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "Panel", "name": null, "children": [{"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "label1", "ZOrder": 0, "actiontag": 46919822, "anchorPointX": 0, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 91, "colorG": 84, "colorR": 91, "customProperty": "", "flipX": false, "flipY": false, "height": 57, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.224324331, "positionPercentY": 4.31, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.227027029, "sizePercentY": 0.57, "sizeType": 0, "tag": 727795806, "touchAble": false, "useMergedTexture": false, "visible": false, "width": 168, "x": -166, "y": 431, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 50, "hAlignment": 0, "text": "Current", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "vipIcon1", "ZOrder": 0, "actiontag": 45907574, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 156, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.268918931, "positionPercentY": 4.31, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.21081081, "sizePercentY": 1.56, "sizeType": 0, "tag": 727795803, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 156, "x": -199, "y": 431, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/iconvip/icon_vip1.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 156, "scale9Width": 156}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "label2", "ZOrder": 0, "actiontag": 20635053, "anchorPointX": 0, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 15, "colorG": 19, "colorR": 107, "customProperty": "", "flipX": false, "flipY": false, "height": 57, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.12702702, "positionPercentY": 4.59, "positionType": 0, "rotation": 0, "scaleX": 0.8, "scaleY": 0.8, "sizePercentX": 0.2945946, "sizePercentY": 0.57, "sizeType": 0, "tag": 727812807, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 218, "x": -94, "y": 459, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 50, "hAlignment": 0, "text": "Recharge", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "diamondIcon", "ZOrder": 0, "actiontag": 62634438, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 84, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.0567567572, "positionPercentY": 4.62, "positionType": 0, "rotation": 0, "scaleX": 0.6, "scaleY": 0.6, "sizePercentX": 0.118918918, "sizePercentY": 0.84, "sizeType": 0, "tag": 727795808, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 88, "x": 42, "y": 462, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "common/icon/icon_diamond.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 84, "scale9Width": 88}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "num", "ZOrder": 0, "actiontag": 23220942, "anchorPointX": 0, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 207, "colorG": 113, "colorR": 21, "customProperty": "", "flipX": false, "flipY": false, "height": 57, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.09594595, "positionPercentY": 4.62, "positionType": 0, "rotation": 0, "scaleX": 0.8, "scaleY": 0.8, "sizePercentX": 0.113513514, "sizePercentY": 0.57, "sizeType": 0, "tag": 727795810, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 84, "x": 71, "y": 462, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 50, "hAlignment": 0, "text": "200", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "label3", "ZOrder": 0, "actiontag": 12870008, "anchorPointX": 1, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 15, "colorG": 19, "colorR": 107, "customProperty": "", "flipX": false, "flipY": false, "height": 57, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.329729736, "positionPercentY": 4.59, "positionType": 0, "rotation": 0, "scaleX": 0.8, "scaleY": 0.8, "sizePercentX": 0.325675666, "sizePercentY": 0.57, "sizeType": 0, "tag": 727795812, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 241, "x": 244, "y": 459, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 50, "hAlignment": 0, "text": "upgrade to", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "vipIcon2", "ZOrder": 0, "actiontag": 3958163, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 156, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.385135144, "positionPercentY": 4.56, "positionType": 0, "rotation": 0, "scaleX": 0.4, "scaleY": 0.4, "sizePercentX": 0.21081081, "sizePercentY": 1.56, "sizeType": 0, "tag": 727812762, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 156, "x": 285, "y": 456, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/iconvip/icon_vip2.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 156, "scale9Width": 156}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "normalPanel", "ZOrder": 3, "actiontag": 16920797, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 100, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.06081081, "positionPercentY": 0.5163636, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 1, "sizePercentY": 0.09090909, "sizeType": 0, "tag": 727795802, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 740, "x": -45, "y": 568, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 162, "bgColorG": 255, "bgColorOpacity": 100, "bgColorR": 0, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "Panel", "name": null, "children": [{"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "label", "ZOrder": 0, "actiontag": 20551303, "anchorPointX": 0, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 15, "colorG": 19, "colorR": 107, "customProperty": "", "flipX": false, "flipY": false, "height": 57, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.195945948, "positionPercentY": 0.23, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.64054054, "sizePercentY": 0.57, "sizeType": 0, "tag": 727812809, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 474, "x": -145, "y": 23, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 50, "hAlignment": 0, "text": "<PERSON><PERSON> đạt cấp VIP tối đa", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "vipIcon", "ZOrder": 0, "actiontag": 33340398, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 156, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.32027027, "positionPercentY": -0.02, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.21081081, "sizePercentY": 1.56, "sizeType": 0, "tag": 727806594, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 156, "x": -237, "y": -2, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/iconvip/icon_vip18.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 156, "scale9Width": 156}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "maxPanel", "ZOrder": 3, "actiontag": 60434506, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 100, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.02972973, "positionPercentY": 0.908181846, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 1, "sizePercentY": 0.09090909, "sizeType": 0, "tag": 727806593, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 740, "x": 22, "y": 999, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "<PERSON><PERSON>", "name": null, "children": [{"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "label", "ZOrder": 0, "actiontag": 63129413, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 149, "colorG": 229, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 57, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.00266666664, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.330666661, "sizePercentY": 0.5135135, "sizeType": 0, "tag": 727795818, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 124, "x": 1, "y": 0, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 50, "hAlignment": 0, "text": "name", "touchScaleEnable": false, "vAlignment": 0}}], "options": {"__type": "ButtonSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "<PERSON><PERSON>", "name": "btn", "ZOrder": 1, "actiontag": 21722049, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSButton", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 111, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -2.64324331, "positionPercentY": 0.5518182, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.5067568, "sizePercentY": 0.100909092, "sizeType": 0, "tag": 727795816, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 375, "x": -1956, "y": 607, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 80, "capInsetsY": 0, "disabled": null, "disabledData": {"path": null, "plistFile": null, "resourceType": 0}, "fontName": "font/youmi.ttf", "fontSize": 14, "fontType": 0, "normal": null, "normalData": {"path": "3q/privilege/btn_purchase.png", "plistFile": "", "resourceType": 0}, "pressed": null, "pressedData": {"path": null, "plistFile": null, "resourceType": 0}, "scale9Enable": false, "scale9Height": 111, "scale9Width": 375, "text": "", "textColorB": 255, "textColorG": 255, "textColorR": 255}}, {"classname": "Panel", "name": null, "children": [], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "btnVip", "ZOrder": 5, "actiontag": 31872695, "anchorPointX": 0, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 200, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.848648667, "positionPercentY": -0.44, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.270270258, "sizePercentY": 0.181818187, "sizeType": 0, "tag": 727828729, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 200, "x": -628, "y": -484, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "image", "ZOrder": 0, "actiontag": 37174709, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 537, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.0554054044, "positionPercentY": 0.50454545, "positionType": 0, "rotation": 0, "scaleX": 2, "scaleY": 2, "sizePercentX": 0.477027029, "sizePercentY": 0.488181829, "sizeType": 0, "tag": 727836762, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 353, "x": 41, "y": 555, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/privilege/banner5.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 537, "scale9Width": 353}}, {"classname": "Panel", "name": null, "children": [{"classname": "ListView", "name": null, "children": [], "options": {"__type": "ListViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ListView", "name": "x", "ZOrder": 0, "actiontag": 17220058, "anchorPointX": 0, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSListView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 1076, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0.001858736, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 1, "sizePercentY": 1, "sizeType": 0, "tag": 727841367, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 729, "x": 0, "y": 2, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 150, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 150, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "bounceEnable": false, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": true, "colorType": 0, "direction": 2, "editorClipAble": false, "gravity": 3, "innerHeight": 0, "innerWidth": 0, "itemMargin": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "Image_52", "ZOrder": 0, "actiontag": 28442181, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 99, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.0480109751, "positionPercentY": 0.03810409, "positionType": 0, "rotation": 0, "scaleX": 1.30000007, "scaleY": 1.30000007, "sizePercentX": 0.245541841, "sizePercentY": 0.0920074359, "sizeType": 0, "tag": 727841368, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 179, "x": 35, "y": 41, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/privilege/pic.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 99, "scale9Width": 179}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "FoxInfo", "ZOrder": 1, "actiontag": 34066844, "anchorPointX": 0, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 1076, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.420270264, "positionPercentY": 0.009090909, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.985135138, "sizePercentY": 0.978181839, "sizeType": 0, "tag": 727841364, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 729, "x": -311, "y": 10, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "topPanel", "ZOrder": 3, "actiontag": 8202362, "anchorPointX": 0.5, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 1100, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.312109381, "positionPercentY": -0.439583331, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.2890625, "sizePercentY": 0.7638889, "sizeType": 0, "tag": 727795794, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 740, "x": -799, "y": -633, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "Panel", "name": null, "children": [{"classname": "ListView", "name": null, "children": [], "options": {"__type": "ListViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ListView", "name": "list", "ZOrder": 1, "actiontag": 29394968, "anchorPointX": 0, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSListView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 1100, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.401449263, "positionPercentY": -0.4227273, "positionType": 0, "rotation": 0, "scaleX": 0.9, "scaleY": 0.9, "sizePercentX": 0.9130435, "sizePercentY": 1, "sizeType": 0, "tag": 727795826, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 1260, "x": -554, "y": -465, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 150, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 150, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "bounceEnable": false, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": true, "colorType": 0, "direction": 1, "editorClipAble": false, "gravity": 1, "innerHeight": 0, "innerWidth": 0, "itemMargin": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "Panel", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "Image_15", "ZOrder": 1, "actiontag": 1920201, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 217, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 2.30000019, "scaleY": 2.5, "sizePercentX": 0.438095242, "sizePercentY": 0.394545466, "sizeType": 0, "tag": 727836599, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 184, "x": 0, "y": 0, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/recharge/btn_chongzhi.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 217, "scale9Width": 184}}], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "bg", "ZOrder": 0, "actiontag": 17210093, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 550, "ignoreSize": false, "layoutParameter": null, "opacity": 0, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 1, "sizePercentY": 1, "sizeType": 0, "tag": 727795828, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 420, "x": 0, "y": 0, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 80, "capInsetsY": 80, "fileName": null, "fileNameData": {"path": "common/box/box_panel_1.png", "plistFile": "", "resourceType": 0}, "scale9Enable": true, "scale9Height": 550, "scale9Width": 420}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "icon", "ZOrder": 1, "actiontag": 26156926, "anchorPointX": 0.5, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 230, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.01904762, "positionPercentY": -0.229090914, "positionType": 0, "rotation": 0, "scaleX": 1.5, "scaleY": 1.5, "sizePercentX": 0.547619045, "sizePercentY": 0.4181818, "sizeType": 0, "tag": 727795829, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 230, "x": -8, "y": -126, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/recharge/icon_cz_1@.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 230, "scale9Width": 230}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "extraInfo", "ZOrder": 8, "actiontag": 28530571, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 80, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.0119047621, "positionPercentY": -0.181818187, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.857142866, "sizePercentY": 0.145454541, "sizeType": 0, "tag": 727795831, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 360, "x": 5, "y": -100, "areaHeight": 80, "areaWidth": 360, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 30, "hAlignment": 1, "text": "Earn an additional 60 diamonds on first charge", "touchScaleEnable": false, "vAlignment": 1}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "diamondIcon", "ZOrder": 3, "actiontag": 22272419, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 84, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.183333337, "positionPercentY": 0.4181818, "positionType": 0, "rotation": 0, "scaleX": 0.85, "scaleY": 0.85, "sizePercentX": 0.209523812, "sizePercentY": 0.152727276, "sizeType": 0, "tag": 727795833, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 88, "x": 77, "y": 230, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "common/icon/icon_diamond.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 84, "scale9Width": 88}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "gain", "ZOrder": 3, "actiontag": 35757189, "anchorPointX": 1, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 0, "colorG": 234, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 67, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.104761906, "positionPercentY": 0.4181818, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.157142863, "sizePercentY": 0.121818185, "sizeType": 0, "tag": 727795835, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 66, "x": 44, "y": 230, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 60, "hAlignment": 0, "text": "60", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "price", "ZOrder": 3, "actiontag": 3170141, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 21, "colorG": 65, "colorR": 106, "customProperty": "", "flipX": false, "flipY": false, "height": 67, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.01904762, "positionPercentY": -0.345454544, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.07857143, "sizePercentY": 0.121818185, "sizeType": 0, "tag": 727795837, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 33, "x": -8, "y": -190, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 60, "hAlignment": 0, "text": "6", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "priceBg", "ZOrder": 1, "actiontag": 20901268, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 122, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": -0.350909084, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.8333333, "sizePercentY": 0.221818179, "sizeType": 0, "tag": 727795843, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 350, "x": 0, "y": -193, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 106, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "common/btn/btn_normal.png", "plistFile": "", "resourceType": 0}, "scale9Enable": true, "scale9Height": 122, "scale9Width": 350}}, {"classname": "Panel", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "bg", "ZOrder": 0, "actiontag": 58374144, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 158, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.01, "positionPercentY": -0.03, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.62, "sizePercentY": 1.58, "sizeType": 0, "tag": 727795839, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 62, "x": -1, "y": -3, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/privilege/napdau.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 158, "scale9Width": 62}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "label", "ZOrder": 1, "actiontag": 38590572, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 175, "colorG": 243, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 120, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.02166667, "positionPercentY": -0.199999943, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.5, "sizePercentY": 1.2, "sizeType": 0, "tag": 727795841, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 50, "x": -2, "y": -19, "areaHeight": 120, "areaWidth": 50, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 30, "hAlignment": 1, "text": "<PERSON><PERSON><PERSON> đầu x2", "touchScaleEnable": false, "vAlignment": 1}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "doublePanel", "ZOrder": 5, "actiontag": 41274657, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 100, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.416666657, "positionPercentY": 0.3581818, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.238095239, "sizePercentY": 0.181818187, "sizeType": 0, "tag": 727795838, "touchAble": false, "useMergedTexture": false, "visible": false, "width": 100, "x": -175, "y": 197, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "Image_90", "ZOrder": 10, "actiontag": 46044878, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 29, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.00238095247, "positionPercentY": 0.414545447, "positionType": 0, "rotation": 0, "scaleX": 2.5, "scaleY": 2.5, "sizePercentX": 0.238095239, "sizePercentY": 0.05272727, "sizeType": 0, "tag": 727836674, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 100, "x": 1, "y": 228, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 15, "capInsetsY": 1, "fileName": null, "fileNameData": {"path": "3q/recharge/title_mingcheng.png", "plistFile": "", "resourceType": 0}, "scale9Enable": true, "scale9Height": 29, "scale9Width": 100}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "Image_91", "ZOrder": 5, "actiontag": 7455372, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 26, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": -0.178181812, "positionType": 0, "rotation": 0, "scaleX": 2, "scaleY": 2, "sizePercentX": 0.4547619, "sizePercentY": 0.0472727269, "sizeType": 0, "tag": 727836675, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 191, "x": 0, "y": -98, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/recharge/bg_title.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 26, "scale9Width": 191}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "item", "ZOrder": 1, "actiontag": 12362971, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 550, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.750080466, "positionPercentY": 3.129495, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.3043478, "sizePercentY": 0.5, "sizeType": 0, "tag": 727795827, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 420, "x": 1035, "y": 3442, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "ListView", "name": null, "children": [], "options": {"__type": "ListViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ListView", "name": "sublist", "ZOrder": 1, "actiontag": 2017585, "anchorPointX": 0, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSListView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 550, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 1.47246373, "positionPercentY": 3.13727283, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.9130435, "sizePercentY": 0.5, "sizeType": 0, "tag": 727836600, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 1260, "x": 2032, "y": 3451, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 75, "bgColorOpacity": 100, "bgColorR": 254, "bgEndColorB": 255, "bgEndColorG": 150, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "bounceEnable": false, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": true, "colorType": 0, "direction": 2, "editorClipAble": false, "gravity": 5, "innerHeight": 0, "innerWidth": 0, "itemMargin": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "Image_49", "ZOrder": 0, "actiontag": 45084558, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 407, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.0007246377, "positionPercentY": 0.31, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.986231863, "sizePercentY": 0.37, "sizeType": 0, "tag": 727837060, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 1361, "x": 1, "y": 341, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/privilege/platform.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 407, "scale9Width": 1361}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "Image_49_0", "ZOrder": 0, "actiontag": 21728487, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 505, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": -0.0945454538, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.986231863, "sizePercentY": 0.459090918, "sizeType": 0, "tag": 727837062, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 1361, "x": 0, "y": -104, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/privilege/plat2.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 505, "scale9Width": 1361}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "rechargePanel", "ZOrder": 3, "actiontag": 26777882, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 1100, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.132421881, "positionPercentY": -0.06458333, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.5390625, "sizePercentY": 0.7638889, "sizeType": 0, "tag": 727795820, "touchAble": false, "useMergedTexture": false, "visible": false, "width": 1380, "x": 339, "y": -93, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "Panel", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "bg", "ZOrder": 0, "actiontag": 36446907, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 930, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.0166666675, "positionPercentY": -0.486363649, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.956521749, "sizePercentY": 0.8454546, "sizeType": 0, "tag": 727795936, "touchAble": false, "useMergedTexture": false, "visible": false, "width": 1320, "x": -23, "y": -535, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 60, "capInsetsY": 60, "fileName": null, "fileNameData": {"path": "3q/privilege/bg_pri.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 930, "scale9Width": 1320}}, {"classname": "Label", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "Image_9", "ZOrder": 0, "actiontag": 45391974, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 29, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.491017967, "positionPercentY": -0.333333343, "positionType": 0, "rotation": 0, "scaleX": 1.1, "scaleY": 1, "sizePercentX": 1.005988, "sizePercentY": 0.508771956, "sizeType": 0, "tag": 727836732, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 336, "x": 164, "y": -19, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/privilege/underline.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 29, "scale9Width": 336}}], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "giftLable", "ZOrder": 6, "actiontag": 19232577, "anchorPointX": 0, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 220, "colorG": 240, "colorR": 252, "customProperty": "", "flipX": false, "flipY": false, "height": 57, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.447101444, "positionPercentY": -0.75, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.242028981, "sizePercentY": 0.05181818, "sizeType": 0, "tag": 727795945, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 334, "x": -617, "y": -825, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 50, "hAlignment": 0, "text": "Privilege Pack:", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "<PERSON><PERSON><PERSON><PERSON>", "name": null, "children": [], "options": {"__type": "PageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "<PERSON><PERSON><PERSON><PERSON>", "name": "pageView", "ZOrder": 12, "actiontag": 47951749, "anchorPointX": 0, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSPageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 1100, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.5, "positionPercentY": -1, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 1, "sizePercentY": 1, "sizeType": 0, "tag": 727795876, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 1380, "x": -690, "y": -1100, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 100, "bgColorG": 150, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 100, "bgEndColorG": 150, "bgEndColorR": 255, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": true, "colorType": 0, "editorClipAble": true, "vectorX": 0, "vectorY": -0.5}}, {"classname": "Panel", "name": null, "children": [{"classname": "Label", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "Image_9", "ZOrder": 0, "actiontag": 24116517, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 29, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.5301205, "positionPercentY": -0.456140339, "positionType": 0, "rotation": 0, "scaleX": 1.1, "scaleY": 1, "sizePercentX": 1.01204824, "sizePercentY": 0.508771956, "sizeType": 0, "tag": 727836707, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 336, "x": 176, "y": -26, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/privilege/underline.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 29, "scale9Width": 336}}], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "name", "ZOrder": 2, "actiontag": 46182199, "anchorPointX": 0, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 220, "colorG": 240, "colorR": 252, "customProperty": "", "flipX": false, "flipY": false, "height": 57, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.0675862059, "positionPercentY": 0.906363666, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.228965521, "sizePercentY": 0.05181818, "sizeType": 0, "tag": 727806637, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 332, "x": 98, "y": 997, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 50, "hAlignment": 0, "text": "V10 privileges:", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "ListView", "name": null, "children": [], "options": {"__type": "ListViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ListView", "name": "list", "ZOrder": 0, "actiontag": 23044126, "anchorPointX": 0, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSListView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 680, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.06896552, "positionPercentY": 0.274545461, "positionType": 0, "rotation": 0, "scaleX": 0.95, "scaleY": 0.95, "sizePercentX": 0.862068951, "sizePercentY": 0.6181818, "sizeType": 0, "tag": 727795864, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 1250, "x": 100, "y": 302, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 150, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 150, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "bounceEnable": false, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": true, "colorType": 0, "direction": 1, "editorClipAble": false, "gravity": 2, "innerHeight": 0, "innerWidth": 0, "itemMargin": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textNode", "ZOrder": 2, "actiontag": 27144664, "anchorPointX": 0, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 220, "colorG": 240, "colorR": 252, "customProperty": "", "flipX": false, "flipY": false, "height": 50, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.6441379, "positionPercentY": 0.24818182, "positionType": 0, "rotation": 0, "scaleX": 0.9, "scaleY": 0.9, "sizePercentX": 0.179310352, "sizePercentY": 0.0454545468, "sizeType": 0, "tag": 727825568, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 260, "x": 934, "y": 273, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 44, "hAlignment": 0, "text": "Original price", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "oldIcon", "ZOrder": 1, "actiontag": 22078965, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 84, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.733103454, "positionPercentY": 0.25636363, "positionType": 0, "rotation": 0, "scaleX": 0.6, "scaleY": 0.6, "sizePercentX": 0.0606896542, "sizePercentY": 0.07636364, "sizeType": 0, "tag": 727806631, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 88, "x": 1063, "y": 282, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "common/icon/icon_diamond.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 84, "scale9Width": 88}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "oldPrice", "ZOrder": 2, "actiontag": 43565735, "anchorPointX": 0, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 220, "colorG": 240, "colorR": 252, "customProperty": "", "flipX": false, "flipY": false, "height": 67, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.765517235, "positionPercentY": 0.24818182, "positionType": 0, "rotation": 0, "scaleX": 0.9, "scaleY": 0.9, "sizePercentX": 0.04551724, "sizePercentY": 0.0609090924, "sizeType": 0, "tag": 727806632, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 66, "x": 1110, "y": 273, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 60, "hAlignment": 0, "text": "60", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "line", "ZOrder": 5, "actiontag": 28372134, "anchorPointX": 0, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 9, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.7075862, "positionPercentY": 0.2509091, "positionType": 0, "rotation": 0, "scaleX": 0.9, "scaleY": 0.9, "sizePercentX": 0.111724138, "sizePercentY": 0.008181818, "sizeType": 0, "tag": 727813017, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 162, "x": 1026, "y": 276, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 7, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/recharge/line_red.png", "plistFile": "", "resourceType": 0}, "scale9Enable": true, "scale9Height": 9, "scale9Width": 162}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textNode2", "ZOrder": 2, "actiontag": 8387487, "anchorPointX": 0, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 220, "colorG": 240, "colorR": 252, "customProperty": "", "flipX": false, "flipY": false, "height": 50, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.6441379, "positionPercentY": 0.183636367, "positionType": 0, "rotation": 0, "scaleX": 0.9, "scaleY": 0.9, "sizePercentX": 0.175172418, "sizePercentY": 0.0454545468, "sizeType": 0, "tag": 727825571, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 254, "x": 934, "y": 202, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 44, "hAlignment": 0, "text": "Current price", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "icon", "ZOrder": 1, "actiontag": 18394737, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 84, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.7337931, "positionPercentY": 0.193636358, "positionType": 0, "rotation": 0, "scaleX": 0.6, "scaleY": 0.6, "sizePercentX": 0.0606896542, "sizePercentY": 0.07636364, "sizeType": 0, "tag": 727795950, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 88, "x": 1064, "y": 213, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "common/icon/icon_diamond.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 84, "scale9Width": 88}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "price", "ZOrder": 2, "actiontag": 11758315, "anchorPointX": 0, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 149, "colorG": 229, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 67, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.765517235, "positionPercentY": 0.189090908, "positionType": 0, "rotation": 0, "scaleX": 0.9, "scaleY": 0.9, "sizePercentX": 0.04551724, "sizePercentY": 0.0609090924, "sizeType": 0, "tag": 727795952, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 66, "x": 1110, "y": 208, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 60, "hAlignment": 0, "text": "60", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "<PERSON><PERSON>", "name": null, "children": [{"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "label", "ZOrder": 0, "actiontag": 9245639, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 21, "colorG": 65, "colorR": 106, "customProperty": "", "flipX": false, "flipY": false, "height": 67, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.002762431, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.281767964, "sizePercentY": 0.5491803, "sizeType": 0, "tag": 727795949, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 102, "x": -1, "y": 0, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 60, "hAlignment": 0, "text": "Buy", "touchScaleEnable": false, "vAlignment": 0}}], "options": {"__type": "ButtonSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "<PERSON><PERSON>", "name": "btn", "ZOrder": 1, "actiontag": 53223076, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSButton", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 122, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.729655147, "positionPercentY": 0.125454545, "positionType": 0, "rotation": 0, "scaleX": 0.8, "scaleY": 0.8, "sizePercentX": 0.249655172, "sizePercentY": 0.110909089, "sizeType": 0, "tag": 727795948, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 362, "x": 1058, "y": 138, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 106, "capInsetsY": 0, "disabled": null, "disabledData": {"path": null, "plistFile": null, "resourceType": 0}, "fontName": "font/youmi.ttf", "fontSize": 14, "fontType": 0, "normal": null, "normalData": {"path": "common/btn/btn_normal.png", "plistFile": "", "resourceType": 0}, "pressed": null, "pressedData": {"path": null, "plistFile": null, "resourceType": 0}, "scale9Enable": true, "scale9Height": 122, "scale9Width": 362, "text": "", "textColorB": 255, "textColorG": 255, "textColorR": 255}}, {"classname": "ListView", "name": null, "children": [], "options": {"__type": "ListViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ListView", "name": "propList", "ZOrder": 0, "actiontag": 56787476, "anchorPointX": 0, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSListView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 200, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.06896552, "positionPercentY": 0.09181818, "positionType": 0, "rotation": 0, "scaleX": 0.7, "scaleY": 0.7, "sizePercentX": 0.7586207, "sizePercentY": 0.181818187, "sizeType": 0, "tag": 727795953, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 1100, "x": 100, "y": 101, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 150, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 150, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "bounceEnable": false, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": true, "colorType": 0, "direction": 2, "editorClipAble": false, "gravity": 5, "innerHeight": 0, "innerWidth": 0, "itemMargin": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "icon_0", "ZOrder": 1, "actiontag": 20692910, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 43, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.8310345, "positionPercentY": 0.193636358, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.03586207, "sizePercentY": 0.03909091, "sizeType": 0, "tag": 727836734, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 52, "x": 1205, "y": 213, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/common/pic_xuanzhong_05.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 43, "scale9Width": 52}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "panel", "ZOrder": 12, "actiontag": 49177265, "anchorPointX": 0, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 1100, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.5384058, "positionPercentY": -0.9990909, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 1.05072463, "sizePercentY": 1, "sizeType": 0, "tag": 727795880, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 1450, "x": -743, "y": -1099, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "<PERSON><PERSON>", "name": null, "children": [], "options": {"__type": "ButtonSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "<PERSON><PERSON>", "name": "leftBtn", "ZOrder": 13, "actiontag": 38507045, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSButton", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": true, "flipY": false, "height": 146, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.476086944, "positionPercentY": -0.472727269, "positionType": 0, "rotation": 0, "scaleX": 0.8, "scaleY": 0.8, "sizePercentX": 0.07753623, "sizePercentY": 0.132727265, "sizeType": 0, "tag": 727812801, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 107, "x": -657, "y": -520, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "disabled": null, "disabledData": {"path": null, "plistFile": null, "resourceType": 0}, "fontName": "font/youmi.ttf", "fontSize": 14, "fontType": 0, "normal": null, "normalData": {"path": "3q/arrow_02.png", "plistFile": "", "resourceType": 0}, "pressed": null, "pressedData": {"path": null, "plistFile": null, "resourceType": 0}, "scale9Enable": false, "scale9Height": 146, "scale9Width": 107, "text": "", "textColorB": 255, "textColorG": 255, "textColorR": 255}}, {"classname": "<PERSON><PERSON>", "name": null, "children": [], "options": {"__type": "ButtonSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "<PERSON><PERSON>", "name": "rightBtn", "ZOrder": 13, "actiontag": 21177288, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSButton", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 146, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.4289855, "positionPercentY": -0.474545449, "positionType": 0, "rotation": 0, "scaleX": 0.8, "scaleY": 0.8, "sizePercentX": 0.07753623, "sizePercentY": 0.132727265, "sizeType": 0, "tag": 727812803, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 107, "x": 592, "y": -522, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "disabled": null, "disabledData": {"path": null, "plistFile": null, "resourceType": 0}, "fontName": "font/youmi.ttf", "fontSize": 14, "fontType": 0, "normal": null, "normalData": {"path": "3q/arrow_02.png", "plistFile": "", "resourceType": 0}, "pressed": null, "pressedData": {"path": null, "plistFile": null, "resourceType": 0}, "scale9Enable": false, "scale9Height": 146, "scale9Width": 107, "text": "", "textColorB": 255, "textColorG": 255, "textColorR": 255}}, {"classname": "Panel", "name": null, "children": [], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "mask", "ZOrder": 21, "actiontag": 9319721, "anchorPointX": 0.5, "anchorPointY": 1, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 1100, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.0007246377, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 1, "sizePercentY": 1, "sizeType": 0, "tag": 727811117, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 1380, "x": -1, "y": 0, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "privilegePanel", "ZOrder": 2, "actiontag": 39301219, "anchorPointX": 0.5, "anchorPointY": 1, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 1100, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.147656247, "positionPercentY": 0.325, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.5390625, "sizePercentY": 0.7638889, "sizeType": 0, "tag": 727795860, "touchAble": false, "useMergedTexture": false, "visible": false, "width": 1380, "x": 378, "y": 468, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "bgex", "ZOrder": 0, "actiontag": 39660476, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 720, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 2, "scaleY": 2, "sizePercentX": 0.609375, "sizePercentY": 0.5, "sizeType": 0, "tag": 727836763, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 1560, "x": 0, "y": 0, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/bg_ziyuanhuishou.jpg", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 720, "scale9Width": 1560}}, {"classname": "Panel", "name": null, "children": [{"classname": "<PERSON><PERSON>", "name": null, "children": [{"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "txt", "ZOrder": 1, "actiontag": 35174132, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 150, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.0333333351, "positionPercentY": 0.0378378369, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.5555556, "sizePercentY": 0.8108108, "sizeType": 0, "tag": 727836880, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 100, "x": -6, "y": 7, "areaHeight": 150, "areaWidth": 100, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 36, "hAlignment": 1, "text": "<PERSON><PERSON><PERSON>", "touchScaleEnable": false, "vAlignment": 1}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "img", "ZOrder": 0, "actiontag": 55521685, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 189, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 1.01111114, "sizePercentY": 1.02162158, "sizeType": 0, "tag": 727836885, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 182, "x": 0, "y": 0, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/privilege/btnactive.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 189, "scale9Width": 182}}], "options": {"__type": "ButtonSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "<PERSON><PERSON>", "name": "nap", "ZOrder": 0, "actiontag": 57613851, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSButton", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 185, "ignoreSize": true, "layoutParameter": null, "opacity": 0, "positionPercentX": 0.495, "positionPercentY": 0.58625, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.9, "sizePercentY": 0.23125, "sizeType": 0, "tag": 727836874, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 180, "x": 99, "y": 469, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "disabled": null, "disabledData": {"path": null, "plistFile": null, "resourceType": 0}, "fontName": "微软雅黑", "fontSize": 14, "fontType": 0, "normal": null, "normalData": {"path": "3q/privilege/btnnormal.png", "plistFile": "", "resourceType": 0}, "pressed": null, "pressedData": {"path": null, "plistFile": null, "resourceType": 0}, "scale9Enable": false, "scale9Height": 185, "scale9Width": 180, "text": "", "textColorB": 255, "textColorG": 255, "textColorR": 255}}, {"classname": "<PERSON><PERSON>", "name": null, "children": [{"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "txt", "ZOrder": 1, "actiontag": 58347558, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 150, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.03888889, "positionPercentY": 0.05945946, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.6666667, "sizePercentY": 0.8108108, "sizeType": 0, "tag": 727836882, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 120, "x": -7, "y": 11, "areaHeight": 150, "areaWidth": 120, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 36, "hAlignment": 1, "text": "Thẻ Tháng", "touchScaleEnable": false, "vAlignment": 1}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "img", "ZOrder": 0, "actiontag": 28611243, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 185, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 1, "sizePercentY": 1, "sizeType": 0, "tag": 727836887, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 180, "x": 0, "y": 0, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/privilege/btnnormal.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 185, "scale9Width": 180}}], "options": {"__type": "ButtonSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "<PERSON><PERSON>", "name": "thethang", "ZOrder": 0, "actiontag": 10476245, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSButton", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 185, "ignoreSize": true, "layoutParameter": null, "opacity": 0, "positionPercentX": 0.495, "positionPercentY": 0.06125, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.9, "sizePercentY": 0.23125, "sizeType": 0, "tag": 727836877, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 180, "x": 99, "y": 49, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "disabled": null, "disabledData": {"path": null, "plistFile": null, "resourceType": 0}, "fontName": "微软雅黑", "fontSize": 14, "fontType": 0, "normal": null, "normalData": {"path": "3q/privilege/btnnormal.png", "plistFile": "", "resourceType": 0}, "pressed": null, "pressedData": {"path": null, "plistFile": null, "resourceType": 0}, "scale9Enable": false, "scale9Height": 185, "scale9Width": 180, "text": "", "textColorB": 255, "textColorG": 255, "textColorR": 255}}, {"classname": "<PERSON><PERSON>", "name": null, "children": [{"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "txt", "ZOrder": 1, "actiontag": 4021845, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 74, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.027777778, "positionPercentY": 0.0270270277, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.5888889, "sizePercentY": 0.4, "sizeType": 0, "tag": 727836884, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 106, "x": -5, "y": 5, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 66, "hAlignment": 1, "text": "VIP", "touchScaleEnable": false, "vAlignment": 1}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "img", "ZOrder": 0, "actiontag": 42694050, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 185, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 1, "sizePercentY": 1, "sizeType": 0, "tag": 727836889, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 180, "x": 0, "y": 0, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/privilege/btnnormal.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 185, "scale9Width": 180}}], "options": {"__type": "ButtonSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "<PERSON><PERSON>", "name": "vip", "ZOrder": 0, "actiontag": 27763963, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSButton", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 185, "ignoreSize": true, "layoutParameter": null, "opacity": 0, "positionPercentX": 0.495, "positionPercentY": -0.2, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.9, "sizePercentY": 0.23125, "sizeType": 0, "tag": 727836879, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 180, "x": 99, "y": -160, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "disabled": null, "disabledData": {"path": null, "plistFile": null, "resourceType": 0}, "fontName": "微软雅黑", "fontSize": 14, "fontType": 0, "normal": null, "normalData": {"path": "3q/privilege/btnnormal.png", "plistFile": "", "resourceType": 0}, "pressed": null, "pressedData": {"path": null, "plistFile": null, "resourceType": 0}, "scale9Enable": false, "scale9Height": 185, "scale9Width": 180, "text": "", "textColorB": 255, "textColorG": 255, "textColorR": 255}}, {"classname": "ImageView", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "rope_0", "ZOrder": 1, "actiontag": 65607977, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 480, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.0357142873, "positionPercentY": -0.870833337, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 1, "sizePercentY": 1, "sizeType": 0, "tag": 727841363, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 28, "x": 1, "y": -418, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/privilege/rope.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 480, "scale9Width": 28}}], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "rope", "ZOrder": 1, "actiontag": 4052090, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 480, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.45, "positionPercentY": 0.715, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.14, "sizePercentY": 0.6, "sizeType": 0, "tag": 727836875, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 28, "x": 90, "y": 572, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/privilege/rope.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 480, "scale9Width": 28}}, {"classname": "<PERSON><PERSON>", "name": null, "children": [{"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "txt", "ZOrder": 1, "actiontag": 714004, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 150, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.0333333351, "positionPercentY": 0.0378378369, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.5555556, "sizePercentY": 0.8108108, "sizeType": 0, "tag": 727841322, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 100, "x": -6, "y": 7, "areaHeight": 150, "areaWidth": 100, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 36, "hAlignment": 1, "text": "<PERSON><PERSON><PERSON>", "touchScaleEnable": false, "vAlignment": 1}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "img", "ZOrder": 0, "actiontag": 6426044, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 189, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 1.01111114, "sizePercentY": 1.02162158, "sizeType": 0, "tag": 727841323, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 182, "x": 0, "y": 0, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/privilege/btnactive.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 189, "scale9Width": 182}}], "options": {"__type": "ButtonSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "<PERSON><PERSON>", "name": "foxCoin", "ZOrder": 0, "actiontag": 44818576, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSButton", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 185, "ignoreSize": true, "layoutParameter": null, "opacity": 0, "positionPercentX": 0.465, "positionPercentY": 0.84625, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.9, "sizePercentY": 0.23125, "sizeType": 0, "tag": 727841321, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 180, "x": 93, "y": 677, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "disabled": null, "disabledData": {"path": null, "plistFile": null, "resourceType": 0}, "fontName": "微软雅黑", "fontSize": 14, "fontType": 0, "normal": null, "normalData": {"path": "3q/privilege/btnnormal.png", "plistFile": "", "resourceType": 0}, "pressed": null, "pressedData": {"path": null, "plistFile": null, "resourceType": 0}, "scale9Enable": false, "scale9Height": 185, "scale9Width": 180, "text": "", "textColorB": 255, "textColorG": 255, "textColorR": 255}}, {"classname": "<PERSON><PERSON>", "name": null, "children": [{"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "txt", "ZOrder": 1, "actiontag": 27662693, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 150, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.0333333351, "positionPercentY": 0.0378378369, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.5555556, "sizePercentY": 0.8108108, "sizeType": 0, "tag": 727841483, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 100, "x": -6, "y": 7, "areaHeight": 150, "areaWidth": 100, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 36, "hAlignment": 1, "text": "Đặc <PERSON><PERSON><PERSON><PERSON>", "touchScaleEnable": false, "vAlignment": 1}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "img", "ZOrder": 0, "actiontag": 47637651, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 189, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 1.01111114, "sizePercentY": 1.02162158, "sizeType": 0, "tag": 727841484, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 182, "x": 0, "y": 0, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/privilege/btnactive.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 189, "scale9Width": 182}}], "options": {"__type": "ButtonSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "<PERSON><PERSON>", "name": "vai", "ZOrder": 0, "actiontag": 25443253, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSButton", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 185, "ignoreSize": true, "layoutParameter": null, "opacity": 0, "positionPercentX": 0.49, "positionPercentY": 0.32625, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.9, "sizePercentY": 0.23125, "sizeType": 0, "tag": 727841482, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 180, "x": 98, "y": 261, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "disabled": null, "disabledData": {"path": null, "plistFile": null, "resourceType": 0}, "fontName": "微软雅黑", "fontSize": 14, "fontType": 0, "normal": null, "normalData": {"path": "3q/privilege/btnnormal.png", "plistFile": "", "resourceType": 0}, "pressed": null, "pressedData": {"path": null, "plistFile": null, "resourceType": 0}, "scale9Enable": false, "scale9Height": 185, "scale9Width": 180, "text": "", "textColorB": 255, "textColorG": 255, "textColorR": 255}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "btnPanel", "ZOrder": 1, "actiontag": 50884809, "anchorPointX": 0, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 800, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.419531256, "positionPercentY": -0.2361111, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.078125, "sizePercentY": 0.5555556, "sizeType": 0, "tag": 727836873, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 200, "x": 1074, "y": -340, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "Panel", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "bg", "ZOrder": 0, "actiontag": 58806219, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 865, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.5050725, "positionPercentY": 0.591818154, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 1.03188407, "sizePercentY": 0.786363661, "sizeType": 0, "tag": 727836935, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 1424, "x": 697, "y": 651, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/privilege/monthcard.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 865, "scale9Width": 1424}}, {"classname": "Panel", "name": null, "children": [{"classname": "<PERSON><PERSON>", "name": null, "children": [{"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "label", "ZOrder": 0, "actiontag": 3226413, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 18, "colorG": 61, "colorR": 102, "customProperty": "", "flipX": false, "flipY": false, "height": 57, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.397515535, "sizePercentY": 0.467213124, "sizeType": 0, "tag": 727836942, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 128, "x": 0, "y": 0, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 50, "hAlignment": 0, "text": "<PERSON><PERSON><PERSON>", "touchScaleEnable": false, "vAlignment": 0}}], "options": {"__type": "ButtonSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "<PERSON><PERSON>", "name": "btn", "ZOrder": 2, "actiontag": 23316706, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSButton", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 122, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.5, "positionPercentY": 0.53, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 1.61, "sizePercentY": 0.61, "sizeType": 0, "tag": 727836941, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 322, "x": 100, "y": 106, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 106, "capInsetsY": 0, "disabled": null, "disabledData": {"path": "common/btn/btn_normal.png", "plistFile": "", "resourceType": 0}, "fontName": "font/youmi.ttf", "fontSize": 14, "fontType": 0, "normal": null, "normalData": {"path": "common/btn/btn_normal.png", "plistFile": "", "resourceType": 0}, "pressed": null, "pressedData": {"path": null, "plistFile": null, "resourceType": 0}, "scale9Enable": true, "scale9Height": 122, "scale9Width": 322, "text": "", "textColorB": 255, "textColorG": 255, "textColorR": 255}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textHas", "ZOrder": 0, "actiontag": 17009121, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 35, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.34, "positionPercentY": 0.235, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.345, "sizePercentY": 0.175, "sizeType": 0, "tag": 727836943, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 69, "x": 68, "y": 47, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 30, "hAlignment": 0, "text": "Own:", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textHasNum", "ZOrder": 0, "actiontag": 18864366, "anchorPointX": 0, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 112, "colorG": 153, "colorR": 92, "customProperty": "", "flipX": false, "flipY": false, "height": 45, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.55, "positionPercentY": 0.235, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.275, "sizePercentY": 0.225, "sizeType": 0, "tag": 727836944, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 55, "x": 110, "y": 47, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 40, "hAlignment": 0, "text": "0/0", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "ListView", "name": null, "children": [], "options": {"__type": "ListViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ListView", "name": "list", "ZOrder": 5, "actiontag": 53188653, "anchorPointX": 0, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSListView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 180, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.56, "positionPercentY": -0.415, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 3.4, "sizePercentY": 0.9, "sizeType": 0, "tag": 727836975, "touchAble": true, "useMergedTexture": false, "visible": false, "width": 680, "x": -112, "y": -83, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 178, "bgColorG": 0, "bgColorOpacity": 100, "bgColorR": 255, "bgEndColorB": 255, "bgEndColorG": 150, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "bounceEnable": false, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": true, "colorType": 0, "direction": 2, "editorClipAble": false, "gravity": 5, "innerHeight": 0, "innerWidth": 0, "itemMargin": 18, "vectorX": 0, "vectorY": -0.5}}, {"classname": "Panel", "name": null, "children": [{"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "label", "ZOrder": 2, "actiontag": 65567591, "anchorPointX": 0, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 91, "colorG": 84, "colorR": 91, "customProperty": "", "flipX": false, "flipY": false, "height": 39, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0.48, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.808, "sizePercentY": 0.78, "sizeType": 0, "tag": 727836977, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 404, "x": 0, "y": 24, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 34, "hAlignment": 0, "text": "Validity period per purchase", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "num", "ZOrder": 2, "actiontag": 56117639, "anchorPointX": 0, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 34, "colorG": 116, "colorR": 230, "customProperty": "", "flipX": false, "flipY": false, "height": 39, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.69, "positionPercentY": 0.5, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.234, "sizePercentY": 0.78, "sizeType": 0, "tag": 727836978, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 117, "x": 345, "y": 25, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 34, "hAlignment": 0, "text": "30 days", "touchScaleEnable": false, "vAlignment": 0}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "item4", "ZOrder": 3, "actiontag": 7285287, "anchorPointX": 0, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 50, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.425, "positionPercentY": -0.565, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 2.5, "sizePercentY": 0.25, "sizeType": 0, "tag": 727836976, "touchAble": true, "useMergedTexture": false, "visible": false, "width": 500, "x": -85, "y": -113, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "Panel", "name": null, "children": [{"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "label1", "ZOrder": 2, "actiontag": 24687351, "anchorPointX": 0, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 91, "colorG": 84, "colorR": 91, "customProperty": "", "flipX": false, "flipY": false, "height": 39, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.006, "positionPercentY": 0.54, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.692, "sizePercentY": 0.78, "sizeType": 0, "tag": 727836980, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 346, "x": -3, "y": 27, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 34, "hAlignment": 0, "text": "Activate multiple games", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "label2", "ZOrder": 2, "actiontag": 20889849, "anchorPointX": 0, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 34, "colorG": 116, "colorR": 230, "customProperty": "", "flipX": false, "flipY": false, "height": 39, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.412, "positionPercentY": 0.54, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.252, "sizePercentY": 0.78, "sizeType": 0, "tag": 727836981, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 126, "x": 206, "y": 27, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 34, "hAlignment": 0, "text": "Privilege", "touchScaleEnable": false, "vAlignment": 0}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "item2", "ZOrder": 3, "actiontag": 40460758, "anchorPointX": 0, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 50, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.415, "positionPercentY": -0.775, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 2.5, "sizePercentY": 0.25, "sizeType": 0, "tag": 727836979, "touchAble": true, "useMergedTexture": false, "visible": false, "width": 500, "x": -83, "y": -155, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "label", "ZOrder": 5, "actiontag": 53790917, "anchorPointX": 0, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 91, "colorG": 84, "colorR": 91, "customProperty": "", "flipX": false, "flipY": false, "height": 39, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.425, "positionPercentY": -0.87, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 1.09, "sizePercentY": 0.195, "sizeType": 0, "tag": 727836982, "touchAble": false, "useMergedTexture": false, "visible": false, "width": 218, "x": -85, "y": -174, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 34, "hAlignment": 0, "text": "Days to expiry:", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "num", "ZOrder": 2, "actiontag": 14356208, "anchorPointX": 1, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 34, "colorG": 116, "colorR": 230, "customProperty": "", "flipX": false, "flipY": false, "height": 39, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.605, "positionPercentY": -0.87, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.19, "sizePercentY": 0.195, "sizeType": 0, "tag": 727836983, "touchAble": false, "useMergedTexture": false, "visible": false, "width": 38, "x": 121, "y": -174, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 34, "hAlignment": 0, "text": "30", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "<PERSON><PERSON>", "name": null, "children": [], "options": {"__type": "ButtonSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "<PERSON><PERSON>", "name": "btnGo", "ZOrder": 0, "actiontag": 49550628, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSButton", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 295, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.505, "positionPercentY": 1.615, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 1.46, "sizePercentY": 1.475, "sizeType": 0, "tag": 727837002, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 292, "x": 101, "y": 323, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "disabled": null, "disabledData": {"path": null, "plistFile": null, "resourceType": 0}, "fontName": "微软雅黑", "fontSize": 14, "fontType": 0, "normal": null, "normalData": {"path": "3q/privilege/thethuong.png", "plistFile": "", "resourceType": 0}, "pressed": null, "pressedData": {"path": null, "plistFile": null, "resourceType": 0}, "scale9Enable": false, "scale9Height": 295, "scale9Width": 292, "text": "", "textColorB": 255, "textColorG": 255, "textColorR": 255}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "panel1", "ZOrder": 0, "actiontag": 44129175, "anchorPointX": 0, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 200, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.128985509, "positionPercentY": 0.189090908, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.144927531, "sizePercentY": 0.181818187, "sizeType": 0, "tag": 727836940, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 200, "x": 178, "y": 208, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "Panel", "name": null, "children": [{"classname": "<PERSON><PERSON>", "name": null, "children": [{"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "label", "ZOrder": 0, "actiontag": 55237473, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 18, "colorG": 61, "colorR": 102, "customProperty": "", "flipX": false, "flipY": false, "height": 57, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.397515535, "sizePercentY": 0.467213124, "sizeType": 0, "tag": 727836952, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 128, "x": 0, "y": 0, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 50, "hAlignment": 0, "text": "<PERSON><PERSON><PERSON>", "touchScaleEnable": false, "vAlignment": 0}}], "options": {"__type": "ButtonSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "<PERSON><PERSON>", "name": "btn", "ZOrder": 2, "actiontag": 65789820, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSButton", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 122, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.5, "positionPercentY": 0.53, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 1.61, "sizePercentY": 0.61, "sizeType": 0, "tag": 727836951, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 322, "x": 100, "y": 106, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 106, "capInsetsY": 0, "disabled": null, "disabledData": {"path": "common/btn/btn_normal.png", "plistFile": "", "resourceType": 0}, "fontName": "font/youmi.ttf", "fontSize": 14, "fontType": 0, "normal": null, "normalData": {"path": "common/btn/btn_normal.png", "plistFile": "", "resourceType": 0}, "pressed": null, "pressedData": {"path": null, "plistFile": null, "resourceType": 0}, "scale9Enable": true, "scale9Height": 122, "scale9Width": 322, "text": "", "textColorB": 255, "textColorG": 255, "textColorR": 255}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textHas", "ZOrder": 0, "actiontag": 27375214, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 35, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.34, "positionPercentY": 0.235, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.345, "sizePercentY": 0.175, "sizeType": 0, "tag": 727836953, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 69, "x": 68, "y": 47, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 30, "hAlignment": 0, "text": "Own:", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textHasNum", "ZOrder": 0, "actiontag": 45050341, "anchorPointX": 0, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 112, "colorG": 153, "colorR": 92, "customProperty": "", "flipX": false, "flipY": false, "height": 45, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.55, "positionPercentY": 0.235, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.275, "sizePercentY": 0.225, "sizeType": 0, "tag": 727836954, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 55, "x": 110, "y": 47, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 40, "hAlignment": 0, "text": "0/0", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "num", "ZOrder": 2, "actiontag": 3851720, "anchorPointX": 1, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 34, "colorG": 116, "colorR": 230, "customProperty": "", "flipX": false, "flipY": false, "height": 39, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 1.06, "positionPercentY": -0.865, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.19, "sizePercentY": 0.195, "sizeType": 0, "tag": 727837001, "touchAble": false, "useMergedTexture": false, "visible": false, "width": 38, "x": 212, "y": -173, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 34, "hAlignment": 0, "text": "30", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "ListView", "name": null, "children": [], "options": {"__type": "ListViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ListView", "name": "list", "ZOrder": 5, "actiontag": 5700168, "anchorPointX": 0, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSListView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 180, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.105, "positionPercentY": -0.41, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 3.4, "sizePercentY": 0.9, "sizeType": 0, "tag": 727836993, "touchAble": true, "useMergedTexture": false, "visible": false, "width": 680, "x": -21, "y": -82, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 178, "bgColorG": 0, "bgColorOpacity": 100, "bgColorR": 255, "bgEndColorB": 255, "bgEndColorG": 150, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "bounceEnable": false, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": true, "colorType": 0, "direction": 2, "editorClipAble": false, "gravity": 5, "innerHeight": 0, "innerWidth": 0, "itemMargin": 18, "vectorX": 0, "vectorY": -0.5}}, {"classname": "Panel", "name": null, "children": [{"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "label", "ZOrder": 2, "actiontag": 59060469, "anchorPointX": 0, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 91, "colorG": 84, "colorR": 91, "customProperty": "", "flipX": false, "flipY": false, "height": 39, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0.48, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.808, "sizePercentY": 0.78, "sizeType": 0, "tag": 727836995, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 404, "x": 0, "y": 24, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 34, "hAlignment": 0, "text": "Validity period per purchase", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "num", "ZOrder": 2, "actiontag": 61782179, "anchorPointX": 0, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 34, "colorG": 116, "colorR": 230, "customProperty": "", "flipX": false, "flipY": false, "height": 39, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.69, "positionPercentY": 0.5, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.234, "sizePercentY": 0.78, "sizeType": 0, "tag": 727836996, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 117, "x": 345, "y": 25, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 34, "hAlignment": 0, "text": "30 days", "touchScaleEnable": false, "vAlignment": 0}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "item4", "ZOrder": 3, "actiontag": 51301517, "anchorPointX": 0, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 50, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.025, "positionPercentY": -0.565, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 2.5, "sizePercentY": 0.25, "sizeType": 0, "tag": 727836994, "touchAble": true, "useMergedTexture": false, "visible": false, "width": 500, "x": 5, "y": -113, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "Panel", "name": null, "children": [{"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "label1", "ZOrder": 2, "actiontag": 38300602, "anchorPointX": 0, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 91, "colorG": 84, "colorR": 91, "customProperty": "", "flipX": false, "flipY": false, "height": 39, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.006, "positionPercentY": 0.54, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.692, "sizePercentY": 0.78, "sizeType": 0, "tag": 727836998, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 346, "x": -3, "y": 27, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 34, "hAlignment": 0, "text": "Activate multiple games", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "label2", "ZOrder": 2, "actiontag": 9161101, "anchorPointX": 0, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 34, "colorG": 116, "colorR": 230, "customProperty": "", "flipX": false, "flipY": false, "height": 39, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.412, "positionPercentY": 0.54, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.252, "sizePercentY": 0.78, "sizeType": 0, "tag": 727836999, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 126, "x": 206, "y": 27, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 34, "hAlignment": 0, "text": "Privilege", "touchScaleEnable": false, "vAlignment": 0}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "item2", "ZOrder": 3, "actiontag": 19168703, "anchorPointX": 0, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 50, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.035, "positionPercentY": -0.775, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 2.5, "sizePercentY": 0.25, "sizeType": 0, "tag": 727836997, "touchAble": true, "useMergedTexture": false, "visible": false, "width": 500, "x": 7, "y": -155, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "label", "ZOrder": 5, "actiontag": 15341049, "anchorPointX": 0, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 91, "colorG": 84, "colorR": 91, "customProperty": "", "flipX": false, "flipY": false, "height": 39, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.02, "positionPercentY": -0.865, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 1.09, "sizePercentY": 0.195, "sizeType": 0, "tag": 727837000, "touchAble": false, "useMergedTexture": false, "visible": false, "width": 218, "x": 4, "y": -173, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 34, "hAlignment": 0, "text": "Days to expiry:", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "<PERSON><PERSON>", "name": null, "children": [], "options": {"__type": "ButtonSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "<PERSON><PERSON>", "name": "btnGo", "ZOrder": 0, "actiontag": 40934461, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSButton", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 338, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.56, "positionPercentY": 1.615, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 1.83, "sizePercentY": 1.69, "sizeType": 0, "tag": 727837059, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 366, "x": 112, "y": 323, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "disabled": null, "disabledData": {"path": null, "plistFile": null, "resourceType": 0}, "fontName": "微软雅黑", "fontSize": 14, "fontType": 0, "normal": null, "normalData": {"path": "3q/privilege/thechiton.png", "plistFile": "", "resourceType": 0}, "pressed": null, "pressedData": {"path": null, "plistFile": null, "resourceType": 0}, "scale9Enable": false, "scale9Height": 338, "scale9Width": 366, "text": "", "textColorB": 255, "textColorG": 255, "textColorR": 255}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "panel2", "ZOrder": 0, "actiontag": 23012204, "anchorPointX": 0, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 200, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.434057981, "positionPercentY": 0.189090908, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.144927531, "sizePercentY": 0.181818187, "sizeType": 0, "tag": 727836950, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 200, "x": 599, "y": 208, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "monthPanel", "ZOrder": 3, "actiontag": 35311297, "anchorPointX": 0, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 1100, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.139453128, "positionPercentY": -0.435416669, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.5390625, "sizePercentY": 0.7638889, "sizeType": 0, "tag": 727836934, "touchAble": true, "useMergedTexture": false, "visible": false, "width": 1380, "x": -357, "y": -627, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "<PERSON><PERSON>", "name": null, "children": [], "options": {"__type": "ButtonSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "<PERSON><PERSON>", "name": "ruleBtn", "ZOrder": 10, "actiontag": 6735967, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSButton", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 23, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.41796875, "positionPercentY": 0.366666675, "positionType": 0, "rotation": 0, "scaleX": 2, "scaleY": 2, "sizePercentX": 0.008984375, "sizePercentY": 0.0159722231, "sizeType": 0, "tag": 727838315, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 23, "x": 1070, "y": 528, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "disabled": null, "disabledData": {"path": null, "plistFile": null, "resourceType": 0}, "fontName": "微软雅黑", "fontSize": 14, "fontType": 0, "normal": null, "normalData": {"path": "3q/common/btn_info.png", "plistFile": "", "resourceType": 0}, "pressed": null, "pressedData": {"path": null, "plistFile": null, "resourceType": 0}, "scale9Enable": false, "scale9Height": 23, "scale9Width": 23, "text": "", "textColorB": 255, "textColorG": 255, "textColorR": 255}}, {"classname": "Panel", "name": null, "children": [{"classname": "ListView", "name": null, "children": [], "options": {"__type": "ListViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ListView", "name": "list", "ZOrder": 1, "actiontag": 22467694, "anchorPointX": 0, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSListView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 1100, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.401449263, "positionPercentY": -0.4227273, "positionType": 0, "rotation": 0, "scaleX": 0.9, "scaleY": 0.9, "sizePercentX": 0.9130435, "sizePercentY": 1, "sizeType": 0, "tag": 727841344, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 1260, "x": -554, "y": -465, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 150, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 150, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "bounceEnable": false, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": true, "colorType": 0, "direction": 1, "editorClipAble": false, "gravity": 1, "innerHeight": 0, "innerWidth": 0, "itemMargin": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "Panel", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "Image_15", "ZOrder": 1, "actiontag": 14925917, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 217, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 2.30000019, "scaleY": 2.5, "sizePercentX": 0.438095242, "sizePercentY": 0.394545466, "sizeType": 0, "tag": 727841347, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 184, "x": 0, "y": 0, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/recharge/btn_chongzhi.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 217, "scale9Width": 184}}], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "bg", "ZOrder": 0, "actiontag": 49066035, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 550, "ignoreSize": false, "layoutParameter": null, "opacity": 0, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 1, "sizePercentY": 1, "sizeType": 0, "tag": 727841346, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 420, "x": 0, "y": 0, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 80, "capInsetsY": 80, "fileName": null, "fileNameData": {"path": "common/box/box_panel_1.png", "plistFile": "", "resourceType": 0}, "scale9Enable": true, "scale9Height": 550, "scale9Width": 420}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "icon", "ZOrder": 1, "actiontag": 115528, "anchorPointX": 0.5, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 230, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.0166666675, "positionPercentY": -0.192727268, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.547619045, "sizePercentY": 0.4181818, "sizeType": 0, "tag": 727841348, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 230, "x": -7, "y": -106, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/recharge/icon_cz_1@.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 230, "scale9Width": 230}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "extraInfo", "ZOrder": 8, "actiontag": 1039755, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 80, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": -0.181818187, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.857142866, "sizePercentY": 0.145454541, "sizeType": 0, "tag": 727841349, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 360, "x": 0, "y": -100, "areaHeight": 80, "areaWidth": 360, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 30, "hAlignment": 1, "text": "f", "touchScaleEnable": false, "vAlignment": 1}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "diamondIcon", "ZOrder": 3, "actiontag": 19784279, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 84, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.183333337, "positionPercentY": 0.4181818, "positionType": 0, "rotation": 0, "scaleX": 0.85, "scaleY": 0.85, "sizePercentX": 0.209523812, "sizePercentY": 0.152727276, "sizeType": 0, "tag": 727841350, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 88, "x": 77, "y": 230, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "common/icon/icon_diamond.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 84, "scale9Width": 88}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "gain", "ZOrder": 3, "actiontag": 43840789, "anchorPointX": 1, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 0, "colorG": 234, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 67, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.104761906, "positionPercentY": 0.4181818, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.157142863, "sizePercentY": 0.121818185, "sizeType": 0, "tag": 727841351, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 66, "x": 44, "y": 230, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 60, "hAlignment": 0, "text": "60", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "price", "ZOrder": 3, "actiontag": 61443009, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 21, "colorG": 65, "colorR": 106, "customProperty": "", "flipX": false, "flipY": false, "height": 67, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.01904762, "positionPercentY": -0.345454544, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.07857143, "sizePercentY": 0.121818185, "sizeType": 0, "tag": 727841352, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 33, "x": -8, "y": -190, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 60, "hAlignment": 0, "text": "6", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "priceBg", "ZOrder": 1, "actiontag": 16116169, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 122, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": -0.350909084, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.8333333, "sizePercentY": 0.221818179, "sizeType": 0, "tag": 727841353, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 350, "x": 0, "y": -193, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 106, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "common/btn/btn_normal.png", "plistFile": "", "resourceType": 0}, "scale9Enable": true, "scale9Height": 122, "scale9Width": 350}}, {"classname": "Panel", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "bg", "ZOrder": 0, "actiontag": 30341274, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 158, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.01, "positionPercentY": -0.03, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.62, "sizePercentY": 1.58, "sizeType": 0, "tag": 727841355, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 62, "x": -1, "y": -3, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/privilege/napdau.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 158, "scale9Width": 62}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "label", "ZOrder": 11, "actiontag": 4636013, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 237, "colorG": 252, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 120, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.00769231329, "positionPercentY": -0.17230767, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.7, "sizePercentY": 1.2, "sizeType": 0, "tag": 727841356, "touchAble": false, "useMergedTexture": false, "visible": false, "width": 70, "x": 0, "y": -17, "areaHeight": 120, "areaWidth": 70, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 34, "hAlignment": 1, "text": "<PERSON><PERSON><PERSON> đầu x2", "touchScaleEnable": false, "vAlignment": 1}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "doublePanel", "ZOrder": 5, "actiontag": 10827793, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 100, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.416666657, "positionPercentY": 0.3581818, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.238095239, "sizePercentY": 0.181818187, "sizeType": 0, "tag": 727841354, "touchAble": false, "useMergedTexture": false, "visible": false, "width": 100, "x": -175, "y": 197, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "Image_90", "ZOrder": 10, "actiontag": 41724124, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 29, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.00238095247, "positionPercentY": 0.414545447, "positionType": 0, "rotation": 0, "scaleX": 2.5, "scaleY": 2.5, "sizePercentX": 0.238095239, "sizePercentY": 0.05272727, "sizeType": 0, "tag": 727841357, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 100, "x": 1, "y": 228, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 15, "capInsetsY": 1, "fileName": null, "fileNameData": {"path": "3q/recharge/title_mingcheng.png", "plistFile": "", "resourceType": 0}, "scale9Enable": true, "scale9Height": 29, "scale9Width": 100}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "Image_91", "ZOrder": 5, "actiontag": 39972798, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 26, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": -0.178181812, "positionType": 0, "rotation": 0, "scaleX": 2, "scaleY": 2, "sizePercentX": 0.4547619, "sizePercentY": 0.0472727269, "sizeType": 0, "tag": 727841358, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 191, "x": 0, "y": -98, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/recharge/bg_title.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 26, "scale9Width": 191}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "itemcoin", "ZOrder": 1, "actiontag": 27821403, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 550, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.902173936, "positionPercentY": -1.319091, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.3043478, "sizePercentY": 0.5, "sizeType": 0, "tag": 727841345, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 420, "x": -1245, "y": -1451, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "ListView", "name": null, "children": [], "options": {"__type": "ListViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ListView", "name": "subl", "ZOrder": 1, "actiontag": 24210870, "anchorPointX": 0, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSListView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 550, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 1.47246373, "positionPercentY": 3.13727283, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.9130435, "sizePercentY": 0.5, "sizeType": 0, "tag": 727841359, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 1260, "x": 2032, "y": 3451, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 75, "bgColorOpacity": 100, "bgColorR": 254, "bgEndColorB": 255, "bgEndColorG": 150, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "bounceEnable": false, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": true, "colorType": 0, "direction": 2, "editorClipAble": false, "gravity": 5, "innerHeight": 0, "innerWidth": 0, "itemMargin": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "Image_49", "ZOrder": 0, "actiontag": 16571244, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 407, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.0007246377, "positionPercentY": 0.31, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.986231863, "sizePercentY": 0.37, "sizeType": 0, "tag": 727841360, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 1361, "x": 1, "y": 341, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/privilege/platform.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 407, "scale9Width": 1361}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "Image_49_0", "ZOrder": 0, "actiontag": 14923473, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 505, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": -0.0945454538, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.986231863, "sizePercentY": 0.459090918, "sizeType": 0, "tag": 727841361, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 1361, "x": 0, "y": -104, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/privilege/plat2.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 505, "scale9Width": 1361}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "foxcoinPanel", "ZOrder": 3, "actiontag": 2496410, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 1100, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.132421881, "positionPercentY": -0.06458333, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.5390625, "sizePercentY": 0.7638889, "sizeType": 0, "tag": 727841343, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 1380, "x": 339, "y": -93, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "Panel", "name": null, "children": [{"classname": "ListView", "name": null, "children": [], "options": {"__type": "ListViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ListView", "name": "list", "ZOrder": 4, "actiontag": 66857310, "anchorPointX": 0, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSListView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 1024, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.4188406, "positionPercentY": -0.4781818, "positionType": 0, "rotation": 0, "scaleX": 0.8, "scaleY": 0.8, "sizePercentX": 1.07246375, "sizePercentY": 0.9309091, "sizeType": 0, "tag": 727841683, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 1480, "x": -578, "y": -526, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 150, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 150, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "bounceEnable": false, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": true, "colorType": 0, "direction": 1, "editorClipAble": false, "gravity": 0, "innerHeight": 0, "innerWidth": 0, "itemMargin": 35, "vectorX": 0, "vectorY": -0.5}}, {"classname": "ListView", "name": null, "children": [], "options": {"__type": "ListViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ListView", "name": "listitem", "ZOrder": 3, "actiontag": 15655720, "anchorPointX": 0, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSListView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 674, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.4188406, "positionPercentY": -0.223636359, "positionType": 0, "rotation": 0, "scaleX": 0.8, "scaleY": 0.8, "sizePercentX": 1.07246375, "sizePercentY": 0.6127273, "sizeType": 0, "tag": 727841684, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 1480, "x": -578, "y": -246, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 150, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 150, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "bounceEnable": false, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": true, "colorType": 0, "direction": 2, "editorClipAble": false, "gravity": 3, "innerHeight": 0, "innerWidth": 0, "itemMargin": 39, "vectorX": 0, "vectorY": -0.5}}, {"classname": "Panel", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "img1", "ZOrder": 0, "actiontag": 48738407, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 26, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.0133587783, "positionPercentY": -0.273516655, "positionType": 0, "rotation": 0, "scaleX": 2.01, "scaleY": 2, "sizePercentX": 0.364503831, "sizePercentY": 0.0376266278, "sizeType": 0, "tag": 727841646, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 191, "x": -7, "y": -189, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 80, "capInsetsY": 80, "fileName": null, "fileNameData": {"path": "3q/recharge/bg_title.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 26, "scale9Width": 191}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "img2", "ZOrder": 0, "actiontag": 35992487, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 46, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.341603041, "positionPercentY": 0.438494921, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.08778626, "sizePercentY": 0.0665701851, "sizeType": 0, "tag": 727841647, "touchAble": false, "useMergedTexture": false, "visible": false, "width": 46, "x": -179, "y": 303, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 80, "capInsetsY": 80, "fileName": null, "fileNameData": {"path": "3q/recharge/img_star.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 46, "scale9Width": 46}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "img3", "ZOrder": 0, "actiontag": 55496934, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 46, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.354961842, "positionPercentY": 0.4413893, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.08778626, "sizePercentY": 0.0665701851, "sizeType": 0, "tag": 727841648, "touchAble": false, "useMergedTexture": false, "visible": false, "width": 46, "x": 186, "y": 305, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 80, "capInsetsY": 80, "fileName": null, "fileNameData": {"path": "3q/recharge/img_star.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 46, "scale9Width": 46}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "img4", "ZOrder": 3, "actiontag": 29710366, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 48, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0.170767009, "positionType": 0, "rotation": 0, "scaleX": 0.66, "scaleY": 0.66, "sizePercentX": 0.0916030556, "sizePercentY": 0.06946454, "sizeType": 0, "tag": 727841649, "touchAble": false, "useMergedTexture": false, "visible": false, "width": 48, "x": 0, "y": 118, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/stat/icon_life.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 48, "scale9Width": 48}}], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "bg", "ZOrder": 0, "actiontag": 65067702, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 691, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0.00143884891, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.9961977, "sizePercentY": 0.9942446, "sizeType": 0, "tag": 727841645, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 524, "x": 0, "y": 1, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 131, "capInsetsY": 61, "fileName": null, "fileNameData": {"path": "3q/recharge/bg_chongzhi01.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 691, "scale9Width": 524}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "title", "ZOrder": 8, "actiontag": 66066702, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 149, "colorG": 213, "colorR": 254, "customProperty": "", "flipX": false, "flipY": false, "height": 57, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.0190114062, "positionPercentY": 0.345323741, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.15209125, "sizePercentY": 0.08201439, "sizeType": 0, "tag": 727841650, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 80, "x": -10, "y": 240, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 50, "hAlignment": 1, "text": "title", "touchScaleEnable": false, "vAlignment": 1}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "times", "ZOrder": 5, "actiontag": 7481206, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 188, "colorG": 226, "colorR": 254, "customProperty": "", "flipX": false, "flipY": false, "height": 41, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.00760456268, "positionPercentY": -0.2705036, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.277566552, "sizePercentY": 0.0589928068, "sizeType": 0, "tag": 727841651, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 146, "x": -4, "y": -188, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 36, "hAlignment": 1, "text": "dailyLimit", "touchScaleEnable": false, "vAlignment": 1}}, {"classname": "ListView", "name": null, "children": [], "options": {"__type": "ListViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ListView", "name": "list", "ZOrder": 6, "actiontag": 221995, "anchorPointX": 0, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSListView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 156, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.450570345, "positionPercentY": -0.241726622, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.89353615, "sizePercentY": 0.224460438, "sizeType": 0, "tag": 727841652, "touchAble": true, "useMergedTexture": false, "visible": false, "width": 470, "x": -237, "y": -168, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 150, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 150, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "bounceEnable": false, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": true, "colorType": 0, "direction": 2, "editorClipAble": false, "gravity": 3, "innerHeight": 0, "innerWidth": 0, "itemMargin": 10, "vectorX": 0, "vectorY": -0.5}}, {"classname": "ListView", "name": null, "children": [], "options": {"__type": "ListViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ListView", "name": "subList", "ZOrder": 6, "actiontag": 1997959, "anchorPointX": 0, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSListView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 110, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.384030432, "positionPercentY": 1.38417268, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.437262356, "sizePercentY": 0.158273384, "sizeType": 0, "tag": 727841653, "touchAble": true, "useMergedTexture": false, "visible": false, "width": 230, "x": -202, "y": 962, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 150, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 150, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "bounceEnable": false, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": true, "colorType": 0, "direction": 2, "editorClipAble": false, "gravity": 5, "innerHeight": 0, "innerWidth": 0, "itemMargin": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "Panel", "name": null, "children": [], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "item", "ZOrder": 0, "actiontag": 17981635, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 145, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.4296578, "positionPercentY": 1.00431657, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.2756654, "sizePercentY": 0.2086331, "sizeType": 0, "tag": 727841654, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 145, "x": -226, "y": 698, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "Panel", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "btn", "ZOrder": 0, "actiontag": 47226344, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 122, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.00285714283, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 1.04571426, "sizePercentY": 1.22, "sizeType": 0, "tag": 727841656, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 366, "x": -1, "y": 0, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 106, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "common/btn/btn_normal.png", "plistFile": "", "resourceType": 0}, "scale9Enable": true, "scale9Height": 122, "scale9Width": 366}}, {"classname": "Panel", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "rmb", "ZOrder": 2, "actiontag": 128641, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 84, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.233333334, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 0.6999999, "scaleY": 0.6999999, "sizePercentX": 0.244444445, "sizePercentY": 0.7, "sizeType": 0, "tag": 727841658, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 88, "x": -84, "y": 0, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "common/icon/icon_diamond.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 84, "scale9Width": 88}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "price", "ZOrder": 4, "actiontag": 1157773, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 16, "colorG": 59, "colorR": 99, "customProperty": "", "flipX": false, "flipY": false, "height": 57, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.00277777785, "positionPercentY": -0.0166666675, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.311111122, "sizePercentY": 0.475, "sizeType": 0, "tag": 727841659, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 112, "x": -1, "y": -2, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 50, "hAlignment": 1, "text": "0000", "touchScaleEnable": false, "vAlignment": 1}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "panel", "ZOrder": 2, "actiontag": 22383914, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 120, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 1.02857149, "sizePercentY": 1.2, "sizeType": 0, "tag": 727841657, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 360, "x": 0, "y": 0, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "bottomPanel", "ZOrder": 7, "actiontag": 27616992, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 100, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.0133079849, "positionPercentY": -0.38705036, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.665399253, "sizePercentY": 0.1438849, "sizeType": 0, "tag": 727841655, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 350, "x": -7, "y": -269, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "Panel", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "bg", "ZOrder": 0, "actiontag": 26670768, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 0, "colorG": 0, "colorR": 0, "customProperty": "", "flipX": false, "flipY": false, "height": 691, "ignoreSize": false, "layoutParameter": null, "opacity": 160, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 1.031496, "sizePercentY": 1.017673, "sizeType": 0, "tag": 727841661, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 524, "x": 0, "y": 0, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 45, "capInsetsY": 40, "fileName": null, "fileNameData": {"path": "3q/recharge/bg_chongzhi01.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 691, "scale9Width": 524}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "img", "ZOrder": 1, "actiontag": 38710321, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 80, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": -0.010309278, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.5551181, "sizePercentY": 0.117820323, "sizeType": 0, "tag": 727841662, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 282, "x": 0, "y": -7, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 45, "capInsetsY": 40, "fileName": null, "fileNameData": {"path": "3q/recharge/bg_title.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 80, "scale9Width": 282}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "label", "ZOrder": 4, "actiontag": 12848571, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 237, "colorG": 252, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 45, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": -0.0117820324, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.163385823, "sizePercentY": 0.0662739351, "sizeType": 0, "tag": 727841663, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 83, "x": 0, "y": -8, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 40, "hAlignment": 1, "text": "desc", "touchScaleEnable": false, "vAlignment": 1}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "mask", "ZOrder": 10, "actiontag": 10419959, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 679, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0.00143884891, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.9657795, "sizePercentY": 0.9769784, "sizeType": 0, "tag": 727841660, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 508, "x": 0, "y": 1, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "icon", "ZOrder": 1, "actiontag": 48528279, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 32, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0.01294964, "positionType": 0, "rotation": 0, "scaleX": 2, "scaleY": 2, "sizePercentX": 0.121673, "sizePercentY": 0.046043165, "sizeType": 0, "tag": 727841664, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 64, "x": 0, "y": 9, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "GUI/image.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 32, "scale9Width": 64}}, {"classname": "Panel", "name": null, "children": [], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "infoPanel", "ZOrder": 6, "actiontag": 23700453, "anchorPointX": 0, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 656, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.5, "positionPercentY": -0.487769783, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 1, "sizePercentY": 0.9438849, "sizeType": 0, "tag": 727841984, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 526, "x": -263, "y": -339, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "item", "ZOrder": 2, "actiontag": 22142825, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 695, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 1.20869565, "positionPercentY": -0.195454553, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.381159425, "sizePercentY": 0.6318182, "sizeType": 0, "tag": 727841644, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 526, "x": 1668, "y": -215, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "Panel", "name": null, "children": [{"classname": "Panel", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [{"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "name", "ZOrder": 0, "actiontag": 65018208, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 230, "colorG": 241, "colorR": 254, "customProperty": "", "flipX": false, "flipY": false, "height": 40, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": -0.09, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.3899371, "sizePercentY": 0.4, "sizeType": 0, "tag": 727841669, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 124, "x": 0, "y": -9, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 35, "hAlignment": 0, "text": "daily gift", "touchScaleEnable": false, "vAlignment": 0}}], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "normal", "ZOrder": 0, "actiontag": 29593866, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 100, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.5, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 1, "sizePercentY": 1, "sizeType": 0, "tag": 727841668, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 318, "x": 159, "y": 0, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 131, "capInsetsY": 61, "fileName": null, "fileNameData": {"path": "3q/privilege/tab_chongzhi_off.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 100, "scale9Width": 318}}, {"classname": "ImageView", "name": null, "children": [{"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "name", "ZOrder": 9, "actiontag": 31983518, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 56, "colorG": 70, "colorR": 98, "customProperty": "", "flipX": false, "flipY": false, "height": 45, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": -0.09, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.449685544, "sizePercentY": 0.45, "sizeType": 0, "tag": 727841671, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 143, "x": 0, "y": -9, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 40, "hAlignment": 0, "text": "daily gift", "touchScaleEnable": false, "vAlignment": 0}}], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "selected", "ZOrder": 1, "actiontag": 48292966, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 100, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.5, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 1, "sizePercentY": 1, "sizeType": 0, "tag": 727841670, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 318, "x": 159, "y": 0, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 130, "capInsetsY": 60, "fileName": null, "fileNameData": {"path": "3q/privilege/tab_chongzhi_on.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 100, "scale9Width": 318}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "item", "ZOrder": 0, "actiontag": 10744747, "anchorPointX": 0, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 100, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.00076923077, "positionPercentY": 0.17, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.244615391, "sizePercentY": 0.333333343, "sizeType": 0, "tag": 727841667, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 318, "x": 1, "y": 51, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "ListView", "name": null, "children": [], "options": {"__type": "ListViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ListView", "name": "list", "ZOrder": 0, "actiontag": 19416207, "anchorPointX": 0, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSListView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 120, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.6923077, "sizePercentY": 0.4, "sizeType": 0, "tag": 727841672, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 900, "x": 0, "y": 0, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 150, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 150, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "bounceEnable": false, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": true, "colorType": 0, "direction": 2, "editorClipAble": false, "gravity": 4, "innerHeight": 0, "innerWidth": 0, "itemMargin": 9, "vectorX": 0, "vectorY": -0.5}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "rightPanel", "ZOrder": 3, "actiontag": 38476563, "anchorPointX": 0, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 300, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.464492768, "positionPercentY": 0.294545442, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.942029, "sizePercentY": 0.272727281, "sizeType": 0, "tag": 727841666, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 1300, "x": -641, "y": 324, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "icon404", "ZOrder": 11, "actiontag": 40528140, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 32, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.328260869, "positionPercentY": -0.224545449, "positionType": 0, "rotation": 0, "scaleX": 2, "scaleY": 2, "sizePercentX": 0.0463768132, "sizePercentY": 0.02909091, "sizeType": 0, "tag": 727841673, "touchAble": false, "useMergedTexture": false, "visible": false, "width": 64, "x": -453, "y": -247, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "GUI/image.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 32, "scale9Width": 64}}, {"classname": "ImageView", "name": null, "children": [{"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "title", "ZOrder": 0, "actiontag": 61553874, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 91, "colorG": 84, "colorR": 91, "customProperty": "", "flipX": false, "flipY": false, "height": 31, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 4.4375, "sizePercentY": 0.96875, "sizeType": 0, "tag": 727841675, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 284, "x": 0, "y": 0, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 26, "hAlignment": 0, "text": "Expedited replenishment", "touchScaleEnable": false, "vAlignment": 0}}], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "bg404", "ZOrder": 9, "actiontag": 29208940, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 32, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.370289862, "positionPercentY": -0.0127272727, "positionType": 0, "rotation": 0, "scaleX": 2, "scaleY": 2, "sizePercentX": 0.0463768132, "sizePercentY": 0.02909091, "sizeType": 0, "tag": 727841674, "touchAble": false, "useMergedTexture": false, "visible": false, "width": 64, "x": -511, "y": -14, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "GUI/image.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 32, "scale9Width": 64}}, {"classname": "Panel", "name": null, "children": [{"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "title", "ZOrder": 2, "actiontag": 19807897, "anchorPointX": 1, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 237, "colorG": 252, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 45, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.44, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.56, "sizePercentY": 0.75, "sizeType": 0, "tag": 727841677, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 280, "x": 220, "y": 0, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 40, "hAlignment": 0, "text": "Time remaining:", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "icon", "ZOrder": 1, "actiontag": 44053348, "anchorPointX": 1, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 44, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.068, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.088, "sizePercentY": 0.733333349, "sizeType": 0, "tag": 727841678, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 44, "x": 34, "y": 0, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "common/icon/icon_sj.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 44, "scale9Width": 44}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "time", "ZOrder": 5, "actiontag": 60935816, "anchorPointX": 1, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 126, "colorG": 233, "colorR": 174, "customProperty": "", "flipX": false, "flipY": false, "height": 45, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.884, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.56, "sizePercentY": 0.75, "sizeType": 0, "tag": 727841679, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 280, "x": 442, "y": 0, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 40, "hAlignment": 0, "text": "2 days 03:03:03", "touchScaleEnable": false, "vAlignment": 0}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "time", "ZOrder": 10, "actiontag": 17113958, "anchorPointX": 0, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 60, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.186956525, "positionPercentY": 0.329090923, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.362318844, "sizePercentY": 0.0545454547, "sizeType": 0, "tag": 727841676, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 500, "x": 258, "y": 362, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "title", "ZOrder": 30, "actiontag": 11551439, "anchorPointX": 0, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 42, "colorG": 183, "colorR": 236, "customProperty": "", "flipX": false, "flipY": false, "height": 45, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.470289856, "positionPercentY": 0.473636359, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.7746377, "sizePercentY": 0.04090909, "sizeType": 0, "tag": 727841680, "touchAble": false, "useMergedTexture": false, "visible": false, "width": 1069, "x": -649, "y": 521, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 40, "hAlignment": 0, "text": "Counted into cumulative recharge activities and gain VIP EXP", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "Image_49_0", "ZOrder": 0, "actiontag": 20842955, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 147, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.006521739, "positionPercentY": 0.4018182, "positionType": 0, "rotation": 0, "scaleX": 2.25, "scaleY": 2, "sizePercentX": 0.457246363, "sizePercentY": 0.13363637, "sizeType": 0, "tag": 727841522, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 631, "x": 9, "y": 442, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/recharge/bg_slogan.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 147, "scale9Width": 631}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "Image_189", "ZOrder": 5, "actiontag": 52750146, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 318, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.007971015, "positionPercentY": -0.378181815, "positionType": 0, "rotation": 0, "scaleX": 0.726, "scaleY": 0.7, "sizePercentX": 1.3905797, "sizePercentY": 0.2890909, "sizeType": 0, "tag": 727841954, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 1919, "x": 11, "y": -416, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/shop/bg_shadow.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 318, "scale9Width": 1919}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "none", "ZOrder": 0, "actiontag": 13759282, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 200, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": -0.042727273, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.5797101, "sizePercentY": 0.181818187, "sizeType": 0, "tag": 727841985, "touchAble": false, "useMergedTexture": false, "visible": false, "width": 800, "x": 0, "y": -47, "areaHeight": 200, "areaWidth": 800, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 60, "hAlignment": 1, "text": "Hiện chưa có gói giới hạn đặc biệt nào, quay lại sau nhé!", "touchScaleEnable": false, "vAlignment": 1}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "vaiPanel", "ZOrder": 3, "actiontag": 26706616, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 1100, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.132421881, "positionPercentY": -0.06388889, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.5390625, "sizePercentY": 0.7638889, "sizeType": 0, "tag": 727841504, "touchAble": false, "useMergedTexture": false, "visible": false, "width": 1380, "x": 339, "y": -92, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "Panel", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "Image_191", "ZOrder": 1, "actiontag": 10254041, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 632, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.511538446, "positionPercentY": 0.518055558, "positionType": 0, "rotation": 0, "scaleX": 1.3, "scaleY": 1.3, "sizePercentX": 0.254166663, "sizePercentY": 0.438888878, "sizeType": 0, "tag": 727841956, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 793, "x": 1596, "y": 746, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/boss/bg_reward.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 632, "scale9Width": 793}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "Image_192", "ZOrder": 1, "actiontag": 40041106, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 390, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.511538446, "positionPercentY": 0.455555558, "positionType": 0, "rotation": 0, "scaleX": 1.3, "scaleY": 0.7, "sizePercentX": 0.221474364, "sizePercentY": 0.270833343, "sizeType": 0, "tag": 727841957, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 691, "x": 1596, "y": 656, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/boss/bg_reward_list.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 390, "scale9Width": 691}}, {"classname": "Panel", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "btn", "ZOrder": 0, "actiontag": 40740838, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 122, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.00285714283, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 1.04571426, "sizePercentY": 1.22, "sizeType": 0, "tag": 727841964, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 366, "x": -1, "y": 0, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 106, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "common/btn/btn_normal.png", "plistFile": "", "resourceType": 0}, "scale9Enable": true, "scale9Height": 122, "scale9Width": 366}}, {"classname": "Panel", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "rmb", "ZOrder": 2, "actiontag": 11673575, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 84, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.233333334, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 0.6999999, "scaleY": 0.6999999, "sizePercentX": 0.244444445, "sizePercentY": 0.7, "sizeType": 0, "tag": 727841966, "touchAble": false, "useMergedTexture": false, "visible": false, "width": 88, "x": -84, "y": 0, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "common/icon/icon_diamond.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 84, "scale9Width": 88}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "price", "ZOrder": 4, "actiontag": 37953311, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 16, "colorG": 59, "colorR": 99, "customProperty": "", "flipX": false, "flipY": false, "height": 57, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.00277777785, "positionPercentY": -0.0166666675, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.311111122, "sizePercentY": 0.475, "sizeType": 0, "tag": 727841967, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 112, "x": -1, "y": -2, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 50, "hAlignment": 1, "text": "0000", "touchScaleEnable": false, "vAlignment": 1}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "panel", "ZOrder": 2, "actiontag": 31123225, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 120, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 1.02857149, "sizePercentY": 1.2, "sizeType": 0, "tag": 727841965, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 360, "x": 0, "y": 0, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "bottomPanel", "ZOrder": 6, "actiontag": 11983300, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 100, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.511538446, "positionPercentY": 0.302777767, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.112179488, "sizePercentY": 0.06944445, "sizeType": 0, "tag": 727841963, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 350, "x": 1596, "y": 436, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "ListView", "name": null, "children": [], "options": {"__type": "ListViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ListView", "name": "list", "ZOrder": 7, "actiontag": 10742033, "anchorPointX": 0, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSListView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 156, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.375320524, "positionPercentY": 0.407638878, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.2724359, "sizePercentY": 0.108333334, "sizeType": 0, "tag": 727841971, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 850, "x": 1171, "y": 587, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 150, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 150, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "bounceEnable": false, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": true, "colorType": 0, "direction": 2, "editorClipAble": false, "gravity": 3, "innerHeight": 0, "innerWidth": 0, "itemMargin": 10, "vectorX": 0, "vectorY": -0.5}}, {"classname": "ListView", "name": null, "children": [], "options": {"__type": "ListViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ListView", "name": "subList", "ZOrder": 7, "actiontag": 64798363, "anchorPointX": 0, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSListView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 110, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 1.07820511, "positionPercentY": 0.954166651, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.07371795, "sizePercentY": 0.07638889, "sizeType": 0, "tag": 727841972, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 230, "x": 3364, "y": 1374, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 150, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 150, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "bounceEnable": false, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": true, "colorType": 0, "direction": 2, "editorClipAble": false, "gravity": 5, "innerHeight": 0, "innerWidth": 0, "itemMargin": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "Panel", "name": null, "children": [], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "item", "ZOrder": 1, "actiontag": 46314358, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 145, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 1.07051277, "positionPercentY": 0.7708333, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.04647436, "sizePercentY": 0.100694448, "sizeType": 0, "tag": 727841973, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 145, "x": 3340, "y": 1110, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "icon", "ZOrder": 2, "actiontag": 24288587, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 32, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.440705121, "positionPercentY": 0.6340278, "positionType": 0, "rotation": 0, "scaleX": 2, "scaleY": 2, "sizePercentX": 0.0205128212, "sizePercentY": 0.0222222228, "sizeType": 0, "tag": 727841975, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 64, "x": 1375, "y": 913, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "GUI/image.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 32, "scale9Width": 64}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "title", "ZOrder": 8, "actiontag": 30234294, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 52, "colorG": 54, "colorR": 72, "customProperty": "", "flipX": false, "flipY": false, "height": 57, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.545833349, "positionPercentY": 0.655555546, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.025641026, "sizePercentY": 0.0395833328, "sizeType": 0, "tag": 727841978, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 80, "x": 1703, "y": 944, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 50, "hAlignment": 1, "text": "title", "touchScaleEnable": false, "vAlignment": 1}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "times", "ZOrder": 5, "actiontag": 33058782, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 99, "colorG": 92, "colorR": 89, "customProperty": "", "flipX": false, "flipY": false, "height": 41, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.5483974, "positionPercentY": 0.606944442, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.0467948727, "sizePercentY": 0.0284722224, "sizeType": 0, "tag": 727841979, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 146, "x": 1711, "y": 874, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 36, "hAlignment": 1, "text": "dailyLimit", "touchScaleEnable": false, "vAlignment": 1}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "confirmBg", "ZOrder": 3, "actiontag": 16797785, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 50, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.5121795, "positionPercentY": 0.747222245, "positionType": 0, "rotation": 0, "scaleX": 1.5, "scaleY": 1, "sizePercentX": 0.0926282, "sizePercentY": 0.0347222239, "sizeType": 0, "tag": 727841980, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 289, "x": 1598, "y": 1076, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/boss/bg_reward_title.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 50, "scale9Width": 289}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "confirmNote", "ZOrder": 4, "actiontag": 61013036, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 72, "colorG": 72, "colorR": 90, "customProperty": "", "flipX": false, "flipY": false, "height": 52, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.512820542, "positionPercentY": 0.749305546, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.06538462, "sizePercentY": 0.0361111127, "sizeType": 0, "tag": 727841981, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 204, "x": 1600, "y": 1079, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 46, "hAlignment": 0, "text": "<PERSON><PERSON><PERSON>", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "Panel", "name": null, "children": [], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "closePanel", "ZOrder": 0, "actiontag": 15325539, "anchorPointX": 0, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 1440, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 1, "sizePercentY": 1, "sizeType": 0, "tag": 727841982, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 3120, "x": 0, "y": 0, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "<PERSON><PERSON>", "name": null, "children": [], "options": {"__type": "ButtonSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "<PERSON><PERSON>", "name": "cancelBtn", "ZOrder": 1, "actiontag": 38428302, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSButton", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 64, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.6647436, "positionPercentY": 0.794444442, "positionType": 0, "rotation": 0, "scaleX": 2, "scaleY": 2, "sizePercentX": 0.0205128212, "sizePercentY": 0.0444444455, "sizeType": 0, "tag": 727841983, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 64, "x": 2074, "y": 1144, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "disabled": null, "disabledData": {"path": null, "plistFile": null, "resourceType": 0}, "fontName": "微软雅黑", "fontSize": 14, "fontType": 0, "normal": null, "normalData": {"path": "3q/common/btn_guanbi.png", "plistFile": "", "resourceType": 0}, "pressed": null, "pressedData": {"path": null, "plistFile": null, "resourceType": 0}, "scale9Enable": false, "scale9Height": 64, "scale9Width": 64, "text": "", "textColorB": 255, "textColorG": 255, "textColorR": 255}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "buyConfirmPanel", "ZOrder": 10, "actiontag": 11878413, "anchorPointX": 0, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 1440, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.609375, "positionPercentY": -0.5, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 1.21875, "sizePercentY": 1, "sizeType": 0, "tag": 727841955, "touchAble": true, "useMergedTexture": false, "visible": false, "width": 3120, "x": -1560, "y": -720, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 0, "bgColorG": 0, "bgColorOpacity": 150, "bgColorR": 0, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 1, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "Panel", "ZOrder": 0, "actiontag": -1, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 1440, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 1, "sizePercentY": 1, "sizeType": 0, "tag": 5813, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 2560, "x": 1280, "y": 720, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 0, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}}