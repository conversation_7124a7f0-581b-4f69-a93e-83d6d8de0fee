{"classname": null, "name": null, "animation": {"classname": null, "name": "AnimationManager", "actionlist": []}, "dataScale": 1, "designHeight": 1440, "designWidth": 2560, "textures": [], "texturesPng": ["3q/event_quasieugioihan/bg_main.png", "activity/preferential_novice/img_tpbx.png", "activity/preferential_novice/img_zxbx.png", "common/btn/btn_hd_close.png", "3q/event_quasieugioihan/btn_tap_02.png", "3q/event_quasieugioihan/btn_tap_01.png", "activity/preferential_novice/txt_thwx.png", "3q/event_quasieugioihan/bg_add.png", "3q/common/pic_xuanzhong_05.png", "3q/event_quasieugioihan/btn_item.png", "GUI/image.png", "3q/event_quasieugioihan/btn_money.png"], "version": "1.6.0.0", "widgetTree": {"classname": "Panel", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "img1", "ZOrder": 1, "actiontag": 56614039, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 422, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.131748065, "positionPercentY": 0.0106508872, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.261568129, "sizePercentY": 0.48284024, "sizeType": 0, "tag": 727809356, "touchAble": false, "useMergedTexture": false, "visible": false, "width": 360, "x": -205, "y": 9, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "activity/preferential_novice/img_tpbx.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 422, "scale9Width": 360}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "img2", "ZOrder": 1, "actiontag": 19314257, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 422, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.131748065, "positionPercentY": 0.0106508872, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.261568129, "sizePercentY": 0.48284024, "sizeType": 0, "tag": 727809358, "touchAble": false, "useMergedTexture": false, "visible": false, "width": 360, "x": -205, "y": 9, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "activity/preferential_novice/img_zxbx.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 422, "scale9Width": 360}}], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "bg", "ZOrder": 1, "actiontag": 19581186, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 845, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.02421875, "positionPercentY": 0.0222222228, "positionType": 0, "rotation": 0, "scaleX": 1.5, "scaleY": 1.5, "sizePercentX": 0.4453125, "sizePercentY": 0.3, "sizeType": 1, "tag": 727808762, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 1556, "x": -62, "y": 32, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 60, "capInsetsY": 60, "fileName": null, "fileNameData": {"path": "3q/event_quasieugioihan/bg_main.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 845, "scale9Width": 1556}}, {"classname": "<PERSON><PERSON>", "name": null, "children": [], "options": {"__type": "ButtonSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "<PERSON><PERSON>", "name": "close", "ZOrder": 20, "actiontag": 40383225, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSButton", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 117, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.372916669, "positionPercentY": 0.37499997, "positionType": 0, "rotation": 0, "scaleX": 1.5, "scaleY": 1.5, "sizePercentX": 0.0269531254, "sizePercentY": 0.0479166657, "sizeType": 0, "tag": 727808767, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 117, "x": 954, "y": 539, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "disabled": null, "disabledData": {"path": null, "plistFile": null, "resourceType": 0}, "fontName": "font/youmi1.ttf", "fontSize": 15, "fontType": 0, "normal": null, "normalData": {"path": "common/btn/btn_hd_close.png", "plistFile": "", "resourceType": 0}, "pressed": null, "pressedData": {"path": null, "plistFile": null, "resourceType": 0}, "scale9Enable": false, "scale9Height": 117, "scale9Width": 117, "text": "", "textColorB": 255, "textColorG": 255, "textColorR": 255}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "time<PERSON><PERSON><PERSON>", "ZOrder": 10, "actiontag": 13199549, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 243, "colorG": 249, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 45, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.102213539, "positionPercentY": -0.354398161, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.1953125, "sizePercentY": 0.027777778, "sizeType": 0, "tag": 727809199, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 330, "x": -261, "y": -510, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 40, "hAlignment": 0, "text": "活动倒计时：2天：02:23:12", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "ListView", "name": null, "children": [], "options": {"__type": "ListViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ListView", "name": "leftList", "ZOrder": 8, "actiontag": 5368600, "anchorPointX": 0, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSListView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 70, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.10781236, "positionPercentY": 0.0564814247, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.5078125, "sizePercentY": 0.0486111119, "sizeType": 0, "tag": 727808911, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 1300, "x": -275, "y": 81, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 150, "bgColorOpacity": 113, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 150, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "bounceEnable": false, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": true, "colorType": 0, "direction": 2, "editorClipAble": false, "gravity": 4, "innerHeight": 0, "innerWidth": 0, "itemMargin": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "Panel", "name": null, "children": [{"classname": "Panel", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "bg", "ZOrder": 1, "actiontag": 34558490, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 49, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1.5, "scaleY": 1.5, "sizePercentX": 1.02651513, "sizePercentY": 1.82432437, "sizeType": 0, "tag": 727809338, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 176, "x": 0, "y": 0, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/event_quasieugioihan/btn_tap_02.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 49, "scale9Width": 176}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "txt", "ZOrder": 10, "actiontag": 25975510, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 189, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 45, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.75757575, "sizePercentY": 0.5405405, "sizeType": 0, "tag": 727809339, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 110, "x": 0, "y": 0, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 40, "hAlignment": 0, "text": "超值突破匣", "touchScaleEnable": false, "vAlignment": 0}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "normal", "ZOrder": 10, "actiontag": 17891472, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 74, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 1, "sizePercentY": 1, "sizeType": 0, "tag": 727809337, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 264, "x": 0, "y": 0, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "Panel", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "bg", "ZOrder": 1, "actiontag": 58290860, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 48, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1.5, "scaleY": 1.5, "sizePercentX": 1.02651513, "sizePercentY": 1.82432437, "sizeType": 0, "tag": 727809344, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 177, "x": 0, "y": 0, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/event_quasieugioihan/btn_tap_01.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 48, "scale9Width": 177}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "txt", "ZOrder": 10, "actiontag": 54855693, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 6, "colorG": 68, "colorR": 106, "customProperty": "", "flipX": false, "flipY": false, "height": 45, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.75757575, "sizePercentY": 0.5405405, "sizeType": 0, "tag": 727809345, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 110, "x": 0, "y": 0, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 40, "hAlignment": 0, "text": "超值突破匣", "touchScaleEnable": false, "vAlignment": 0}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "selected", "ZOrder": 10, "actiontag": 28846383, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 74, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 1, "sizePercentY": 1, "sizeType": 0, "tag": 727809343, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 264, "x": 0, "y": 0, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "leftItem", "ZOrder": 0, "actiontag": 32485204, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 74, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -1.01614571, "positionPercentY": 0.5321763, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.103125, "sizePercentY": 0.05138889, "sizeType": 0, "tag": 727809336, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 264, "x": -2601, "y": 766, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "titleImage", "ZOrder": 10, "actiontag": 43991169, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 316, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.13671875, "positionPercentY": 0.2534722, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.312890619, "sizePercentY": 0.202083334, "sizeType": 0, "tag": 727809250, "touchAble": false, "useMergedTexture": false, "visible": false, "width": 1122, "x": 350, "y": 365, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "activity/preferential_novice/txt_thwx.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 316, "scale9Width": 1122}}, {"classname": "ListView", "name": null, "children": [], "options": {"__type": "ListViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ListView", "name": "goodsList", "ZOrder": 20, "actiontag": 21562526, "anchorPointX": 0, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSListView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 237, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.121875, "positionPercentY": -0.170138881, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.4296875, "sizePercentY": 0.16458334, "sizeType": 0, "tag": 727809346, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 1100, "x": -312, "y": -244, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 150, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 150, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "bounceEnable": false, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": true, "colorType": 0, "direction": 2, "editorClipAble": false, "gravity": 5, "innerHeight": 0, "innerWidth": 0, "itemMargin": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "Panel", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "add", "ZOrder": 1, "actiontag": 1237377, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 158, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1.5, "scaleY": 1.5, "sizePercentX": 0.3755274, "sizePercentY": 0.3755274, "sizeType": 0, "tag": 727809352, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 158, "x": 0, "y": 0, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/event_quasieugioihan/bg_add.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 158, "scale9Width": 158}}, {"classname": "Panel", "name": null, "children": [], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "icon", "ZOrder": 1, "actiontag": 18977121, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 192, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 0.7, "scaleY": 0.7, "sizePercentX": 0.8101266, "sizePercentY": 0.8101266, "sizeType": 0, "tag": 727809355, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 192, "x": 0, "y": 0, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "select", "ZOrder": 2, "actiontag": 46382506, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 43, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.25316456, "positionPercentY": 0.240506336, "positionType": 0, "rotation": 0, "scaleX": 2, "scaleY": 2, "sizePercentX": 0.337552756, "sizePercentY": 0.371308029, "sizeType": 0, "tag": 727809414, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 52, "x": 60, "y": 57, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/common/pic_xuanzhong_05.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 43, "scale9Width": 52}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "bg", "ZOrder": 0, "actiontag": 45557153, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 158, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1.5, "scaleY": 1.5, "sizePercentX": 0.337552756, "sizePercentY": 0.337552756, "sizeType": 0, "tag": 727818350, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 158, "x": 0, "y": 0, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/event_quasieugioihan/btn_item.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 158, "scale9Width": 158}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "item", "ZOrder": 10, "actiontag": 11884140, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 237, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.910807252, "positionPercentY": 0.320138961, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.09257813, "sizePercentY": 0.16458334, "sizeType": 0, "tag": 727809351, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 237, "x": 2331, "y": 461, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "goodsBg", "ZOrder": 15, "actiontag": 14591129, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 270, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.144140631, "positionPercentY": 0.0152777778, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.3515625, "sizePercentY": 0.1875, "sizeType": 0, "tag": 727809387, "touchAble": false, "useMergedTexture": false, "visible": false, "width": 900, "x": 369, "y": 22, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 128, "capInsetsY": 128, "fileName": null, "fileNameData": {"path": "GUI/image.png", "plistFile": "", "resourceType": 0}, "scale9Enable": true, "scale9Height": 270, "scale9Width": 900}}, {"classname": "Panel", "name": null, "children": [{"classname": "<PERSON><PERSON>", "name": null, "children": [{"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "txt", "ZOrder": 1, "actiontag": 19466154, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 182, "colorG": 244, "colorR": 254, "customProperty": "", "flipX": false, "flipY": false, "height": 89, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.0173956268, "positionPercentY": 0.05078125, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.19880715, "sizePercentY": 0.390625, "sizeType": 0, "tag": 727818324, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 132, "x": 8, "y": 6, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 80, "hAlignment": 0, "text": "49元", "touchScaleEnable": false, "vAlignment": 0}}], "options": {"__type": "ButtonSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "<PERSON><PERSON>", "name": "btnBuy", "ZOrder": 5, "actiontag": 46902148, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSButton", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 128, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.62875, "sizePercentY": 0.64, "sizeType": 0, "tag": 727818323, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 503, "x": 0, "y": 0, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "disabled": null, "disabledData": {"path": null, "plistFile": null, "resourceType": 0}, "fontName": "font/youmi1.ttf", "fontSize": 14, "fontType": 0, "normal": null, "normalData": {"path": "3q/event_quasieugioihan/btn_money.png", "plistFile": "", "resourceType": 0}, "pressed": null, "pressedData": {"path": null, "plistFile": null, "resourceType": 0}, "scale9Enable": false, "scale9Height": 128, "scale9Width": 503, "text": "", "textColorB": 255, "textColorG": 255, "textColorR": 255}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "limitTxt", "ZOrder": 1, "actiontag": 40977664, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 243, "colorG": 249, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 45, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": -0.43, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.13125, "sizePercentY": 0.15, "sizeType": 0, "tag": 727818325, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 88, "x": 0, "y": -86, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 40, "hAlignment": 0, "text": "限购1次", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "Panel", "name": null, "children": [{"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "txt", "ZOrder": 1, "actiontag": 30856521, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 45, "colorG": 45, "colorR": 103, "customProperty": "", "flipX": false, "flipY": false, "height": 25, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.105, "positionPercentY": 0.145, "positionType": 0, "rotation": 45, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.22, "sizePercentY": 0.11, "sizeType": 0, "tag": 727818327, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 24, "x": 21, "y": 29, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 22, "hAlignment": 0, "text": "原价", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "num", "ZOrder": 1, "actiontag": 9273238, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 45, "colorG": 45, "colorR": 103, "customProperty": "", "flipX": false, "flipY": false, "height": 57, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.04, "positionPercentY": 0, "positionType": 0, "rotation": 45, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.375, "sizePercentY": 0.25, "sizeType": 0, "tag": 727818328, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 84, "x": -8, "y": 0, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 50, "hAlignment": 0, "text": "198", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "line", "ZOrder": 10, "actiontag": 16350278, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 80, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.005, "positionPercentY": 0.01, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.44, "sizePercentY": 0.43, "sizeType": 0, "tag": 727818329, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 80, "x": 1, "y": 2, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "GUI/image.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 80, "scale9Width": 80}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "originalPricePanel", "ZOrder": 10, "actiontag": 33254664, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 200, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.44125, "positionPercentY": -0.055, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.25, "sizePercentY": 1, "sizeType": 0, "tag": 727818326, "touchAble": true, "useMergedTexture": false, "visible": false, "width": 200, "x": 353, "y": -11, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "Panel", "name": null, "children": [], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "mask", "ZOrder": 10, "actiontag": 12934780, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 250, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.0425, "positionPercentY": 1.36, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 1.5, "sizePercentY": 1.25, "sizeType": 0, "tag": 727818330, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 1200, "x": 34, "y": 272, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "pricePanel", "ZOrder": 25, "actiontag": 5211349, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 200, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.0892299041, "positionPercentY": -0.246924639, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.3125, "sizePercentY": 0.1388889, "sizeType": 0, "tag": 727818322, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 800, "x": 228, "y": -355, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "Panel", "ZOrder": 0, "actiontag": -1, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 1440, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 1, "sizePercentY": 1, "sizeType": 0, "tag": 727808760, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 2560, "x": 1280, "y": 720, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 0, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 1, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}}