{"classname": null, "name": null, "animation": {"classname": null, "name": "AnimationManager", "actionlist": []}, "dataScale": 1, "designHeight": 1440, "designWidth": 2560, "textures": [], "texturesPng": [], "version": "*******", "widgetTree": {"classname": "Panel", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "bg", "ZOrder": 1, "actiontag": 19581186, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 472, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.000390625, "positionPercentY": -0.0069444445, "positionType": 0, "rotation": 0, "scaleX": 2, "scaleY": 2, "sizePercentX": 0.259375, "sizePercentY": 0.372222215, "sizeType": 1, "tag": 727808762, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 574, "x": 1, "y": -10, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 60, "capInsetsY": 60, "fileName": null, "fileNameData": {"path": "activity/preferential_novice/box_thwx.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 472, "scale9Width": 574}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "title", "ZOrder": 20, "actiontag": 20807200, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 66, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0.272916675, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.259375, "sizePercentY": 0.372222215, "sizeType": 1, "tag": 727809382, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 315, "x": 0, "y": 393, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 60, "capInsetsY": 60, "fileName": null, "fileNameData": {"path": "activity/preferential_novice/txt_zxwp.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 66, "scale9Width": 315}}, {"classname": "ListView", "name": null, "children": [], "options": {"__type": "ListViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ListView", "name": "list", "ZOrder": 4, "actiontag": 46412474, "anchorPointX": 0, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSListView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 421, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.216406256, "positionPercentY": -0.2361111, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.4375, "sizePercentY": 0.2923611, "sizeType": 0, "tag": 727809137, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 1120, "x": -554, "y": -340, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 150, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 150, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "bounceEnable": false, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": true, "colorType": 0, "direction": 1, "editorClipAble": false, "gravity": 0, "innerHeight": 0, "innerWidth": 0, "itemMargin": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "ListView", "name": null, "children": [], "options": {"__type": "ListViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ListView", "name": "slotList", "ZOrder": 9, "actiontag": 54191139, "anchorPointX": 0, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSListView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 220, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.194921881, "positionPercentY": 0.07777778, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.390625, "sizePercentY": 0.152777776, "sizeType": 0, "tag": 727809174, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 1000, "x": -499, "y": 112, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 150, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 150, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "bounceEnable": false, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": true, "colorType": 0, "direction": 2, "editorClipAble": false, "gravity": 3, "innerHeight": 0, "innerWidth": 0, "itemMargin": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "Panel", "name": null, "children": [{"classname": "Panel", "name": null, "children": [], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "pic", "ZOrder": 0, "actiontag": 17667695, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 200, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 1, "sizePercentY": 1, "sizeType": 0, "tag": 727809139, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 200, "x": 0, "y": 0, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "select", "ZOrder": 1, "actiontag": 30142387, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 27, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.3, "positionPercentY": -0.3, "positionType": 0, "rotation": 0, "scaleX": 2, "scaleY": 2, "sizePercentX": 0.135, "sizePercentY": 0.135, "sizeType": 0, "tag": 727809140, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 27, "x": 60, "y": -60, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "activity/customize_gift/radio_tishi1.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 27, "scale9Width": 27}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "iconItem", "ZOrder": 5, "actiontag": 38992706, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 200, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.6886719, "positionPercentY": 0.430555552, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.078125, "sizePercentY": 0.1388889, "sizeType": 0, "tag": 727808912, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 200, "x": -1763, "y": 620, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "Panel", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "select", "ZOrder": 2, "actiontag": 56454035, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 223, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1.05, "scaleY": 1.05, "sizePercentX": 1.01363635, "sizePercentY": 1.01363635, "sizeType": 0, "tag": 727809172, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 223, "x": 0, "y": 0, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "common/box/box_selected.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 223, "scale9Width": 223}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "add", "ZOrder": 1, "actiontag": 11367780, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 89, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.00681816461, "positionPercentY": 0.0022727272, "positionType": 0, "rotation": 0, "scaleX": 2.4, "scaleY": 2.4, "sizePercentX": 0.404545456, "sizePercentY": 0.404545456, "sizeType": 0, "tag": 727809173, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 89, "x": -1, "y": 0, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "activity/customize_gift/add.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 89, "scale9Width": 89}}, {"classname": "Panel", "name": null, "children": [], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "pic", "ZOrder": 3, "actiontag": 16438548, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 200, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1.1, "scaleY": 1.1, "sizePercentX": 0.909090936, "sizePercentY": 0.909090936, "sizeType": 0, "tag": 727809171, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 200, "x": 0, "y": 0, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 255, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "slotIcon", "ZOrder": 5, "actiontag": 10156672, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 220, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.315234363, "positionPercentY": 0.6493056, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.0859375, "sizePercentY": 0.152777776, "sizeType": 0, "tag": 727809170, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 220, "x": -807, "y": 935, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "ListView", "name": null, "children": [], "options": {"__type": "ListViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ListView", "name": "subList", "ZOrder": 5, "actiontag": 61667623, "anchorPointX": 0, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSListView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 200, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.158593744, "positionPercentY": 0.5923611, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.4296875, "sizePercentY": 0.1388889, "sizeType": 0, "tag": 727809248, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 1100, "x": -406, "y": 853, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 150, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 150, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "bounceEnable": false, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": true, "colorType": 0, "direction": 2, "editorClipAble": false, "gravity": 3, "innerHeight": 0, "innerWidth": 0, "itemMargin": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "tip1", "ZOrder": 10, "actiontag": 59620593, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 235, "colorG": 249, "colorR": 254, "customProperty": "", "flipX": false, "flipY": false, "height": 40, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.0046875, "positionPercentY": -0.263194442, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.296875, "sizePercentY": 0.027777778, "sizeType": 0, "tag": 727809259, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 800, "x": 12, "y": -379, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 40, "hAlignment": 0, "text": "·请选择心仪的道具搭配成为您的专属物匣！", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "Panel", "name": null, "children": [], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "unTouchPanel", "ZOrder": 0, "actiontag": 29623765, "anchorPointX": 0, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 1000, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.228906244, "positionPercentY": -0.352777779, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.46875, "sizePercentY": 0.6944444, "sizeType": 0, "tag": 727809275, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 1200, "x": -586, "y": -508, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "close", "ZOrder": 10, "actiontag": 45491056, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 69, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.2421875, "positionPercentY": 0.304166675, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.0269531254, "sizePercentY": 0.0479166657, "sizeType": 0, "tag": 727809285, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 69, "x": 620, "y": 438, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "common/btn/btn_hd_close.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 69, "scale9Width": 69}}, {"classname": "<PERSON><PERSON>", "name": null, "children": [{"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "label", "ZOrder": 0, "actiontag": 10332521, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 237, "colorG": 252, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 50, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.451807231, "sizePercentY": 0.409836054, "sizeType": 0, "tag": 727809386, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 150, "x": 0, "y": 0, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 50, "hAlignment": 0, "text": "下一步", "touchScaleEnable": false, "vAlignment": 0}}], "options": {"__type": "ButtonSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "<PERSON><PERSON>", "name": "btn", "ZOrder": 20, "actiontag": 65866990, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSButton", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 122, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.001953125, "positionPercentY": -0.3298611, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.1296875, "sizePercentY": 0.08472222, "sizeType": 0, "tag": 727809385, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 332, "x": 5, "y": -475, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "disabled": null, "disabledData": {"path": null, "plistFile": null, "resourceType": 0}, "fontName": "微软雅黑", "fontSize": 14, "fontType": 0, "normal": null, "normalData": {"path": "activity/preferential_novice/btn_normal.png", "plistFile": "", "resourceType": 0}, "pressed": null, "pressedData": {"path": null, "plistFile": null, "resourceType": 0}, "scale9Enable": false, "scale9Height": 122, "scale9Width": 332, "text": "", "textColorB": 255, "textColorG": 255, "textColorR": 255}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "Panel", "ZOrder": 0, "actiontag": -1, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 1440, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 1, "sizePercentY": 1, "sizeType": 0, "tag": 727808760, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 2560, "x": 1280, "y": 720, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 0, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 1, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}}