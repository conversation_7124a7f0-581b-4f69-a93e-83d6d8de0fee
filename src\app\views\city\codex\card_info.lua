--local ViewBase = cc.load("mvc").ViewBase
local CardInfoViewView = class("CardInfoViewView", Dialog)

local rarityConfigImages = {
    [1] = {
        icon = ui.RARITY_ICON3[1],
        tag = "3q/rarity/tags/icon_rarity3.png",
        star = "common/icon/icon_star.png",
        star_d = "common/icon/icon_star_d.png"

    },
    [2] = {
        icon = ui.RARITY_ICON3[2],
        tag = "3q/rarity/tags/icon_rarity3.png",
        star = "common/icon/icon_star.png",
        star_d = "common/icon/icon_star_d.png"

    },
    [3] = {
        icon = ui.RARITY_ICON3[3],
        tag = "3q/rarity/tags/icon_rarity4.png",
        star = "common/icon/icon_fire.png",
        star_d = "common/icon/icon_fire_d.png"
    },
    [4] = {
        icon = ui.RARITY_ICON3[4],
        tag = "3q/rarity/tags/icon_rarity5.png",
        star = "common/icon/icon_fire.png",
        star_d = "common/icon/icon_fire_d.png"

    },
    [5] = {
        icon = ui.RARITY_ICON3[5],
        tag = "3q/rarity/tags/icon_rarity5.png",
        star = "common/icon/icon_fire.png",
        star_d = "common/icon/icon_fire_d.png"

    },
}

local WAY_BTNTITLE = {
    --扫荡
    gLanguageCsv.mopUp,
    --前往
    gLanguageCsv.leaveFor,
    --未开启
    gLanguageCsv.notOpen,
}

local WAY_TYPE = {
    --扫荡
    MOPUP = 1,
    --前往
    LEAVEFOR = 2,
    --未开启
    NOTOPEN = 3
}

local ADVENTURE_TITLE = {
    endlessTower = gLanguageCsv.adventure, -- 冒险之路
    dispatchTask = gLanguageCsv.adventure, -- 派遣
    randomTower = gLanguageCsv.adventure, -- 以太乐园
    cloneBattle = gLanguageCsv.adventure, -- 元素挑战
    activityGate = gLanguageCsv.adventure, -- 活动副本
    fishing = gLanguageCsv.adventure, -- 钓鱼
    gym = gLanguageCsv.gymChallenge        -- 道馆挑战
}

local ZAWAKE_TITLE = {
    zawakeFragExclusive = gLanguageCsv.zawake,
    zawakeFragCurrency = gLanguageCsv.zawake,
}

local SHOP_UNLOCK_KEY = game.SHOP_UNLOCK_KEY

CardInfoViewView.WAY_TITLE = {
    normal = gLanguageCsv.getplay, -- 获取来源
    shop = {
        gLanguageCsv.spaceHandpick,
        gLanguageCsv.spaceGuild,
        gLanguageCsv.spaceFragment,
        gLanguageCsv.spacePvp,
        gLanguageCsv.explorer,
        gLanguageCsv.randomTower,
        gLanguageCsv.craft,
        gLanguageCsv.equipShop,
        gLanguageCsv.unionCombet,
        gLanguageCsv.crossCraft,
        gLanguageCsv.crossArena,
        gLanguageCsv.fishing,
        gLanguageCsv.onlineFight,
        gLanguageCsv.skin,
        gLanguageCsv.crossMine,
        gLanguageCsv.huntingArea,
    },
    activity = gLanguageCsv.activity, -- 活动面板(分页签)
    gate = gLanguageCsv.mainline, -- 关卡界面（精确到章节）
    endlessTower = gLanguageCsv.endlessTower, -- 冒险之路
    dispatchTask = gLanguageCsv.dispatch, -- 派遣
    randomTower = gLanguageCsv.randomTower, -- 以太乐园
    explorerDraw = gLanguageCsv.explorerDraw, -- 探险器寻宝
    cloneBattle = gLanguageCsv.clone, -- 元素挑战
    craft = gLanguageCsv.craft, -- 石英大会
    task = gLanguageCsv.task, --任务
    talent = gLanguageCsv.talent, -- 天赋
    fishing = gLanguageCsv.angling, -- 钓鱼
    gymChallenge = gLanguageCsv.gymChallenge, -- 道馆挑战
    drawCard = {
        diamond = gLanguageCsv.diamondDraw,
        limit = gLanguageCsv.drawLimit,
        gold = gLanguageCsv.goldDraw,
        equip = gLanguageCsv.drawEquip,
        diamondup = gLanguageCsv.diamondUpDrawCard,
    },
    activityGate = {
        gold = gLanguageCsv.GoldTranscript,
        exp = gLanguageCsv.expTranscript,
        frag = gLanguageCsv.FragmentTranscript,
        gift = gLanguageCsv.GiftTranscript,
    },

    gemDraw = gLanguageCsv.drawGemTitle,
    megaStone = gLanguageCsv.everydayTransform,
    keyStone = gLanguageCsv.everydayTransform,
    zawakeFragExclusive = gLanguageCsv.zawakeFragExchange,
    zawakeFragCurrency = gLanguageCsv.zawakeFragExchange,
}

CardInfoViewView.RESOURCE_FILENAME = "codex_card_info.json"
CardInfoViewView.RESOURCE_BINDING = {
    ["btnExit"] = {
        varname = "btnExit",
        binds = {
            event = "touch",
            class = "listview",
            methods = { ended = bindHelper.self("onClose") },
        },
    },
    ["labelTitle"] = {
		binds = {
			event = "effect",
			data = {shadow = {color = cc.c4b(107, 23, 36, 255), offset = cc.size(3, -3), size = 3}},
		},
	},
    ["labelCoBan"] = "labelCoBan",
    ["labelDuyen"] = "labelDuyen",
    ["labelTangSao"] = "labelTangSao",
    ["labelNhan"] = "labelNhan",
    ["iconRarity"] = "iconRarity",
    ["panelCardInfo.labelCardName"] = "labelCardName",
    ["panelCardInfo.imgTagRarity"] = "imgTagRarity",
    ["panelCardInfo.progressBar"] = "progressBar",
    ["panelCoBan"] = "panelCoBan",
    ["panelCoBan.panelThuocTinh"] = "panelThuocTinh",
    ["panelCoBan.labelGioiThieu"] = "labelGioiThieu",
    ["itemSkill"] = "itemSkill",
    ["panelCoBan.listSkillsActive"] = {
        varname = "listSkillsActive",
        binds = {
            event = "extend",
            class = "listview",
            props = {
                data = bindHelper.self("skillDatas"),
                item = bindHelper.self("itemSkill"),
                margin = 50,
                onItem = function(list, node, k, v)
                    uiEasy.setSkillInfoToItems({
                        name = node:get("lableSkillName"),
                        icon = node:get("imageSkill"),
                    }, v.skillId)

                    bind.touch(list, node, { methods = { ended = functools.partial(list.clickItem, v) } })
                end,
            },
            handlers = {
                clickItem = bindHelper.self("onShowSkillInfo"),
            },
        },
    },
    ["panelCoBan.listSkillsPassive"] = {
        varname = "listskillNoiTai",
        binds = {
            event = "extend",
            class = "listview",
            props = {
                data = bindHelper.self("skillNoiTaiDatas"),
                item = bindHelper.self("itemSkill"),
                margin = 50,
                onItem = function(list, node, k, v)
                    uiEasy.setSkillInfoToItems({
                        name = node:get("lableSkillName"),
                        icon = node:get("imageSkill"),
                    }, v.skillId)

                    bind.touch(list, node, { methods = { ended = functools.partial(list.clickItem, v) } })
                end,
            },
            handlers = {
                clickItem = bindHelper.self("onShowSkillInfo"),
            },
        },
    },
    ["panelTangSao"] = "panelTangSao",
    ["panelNhan"] = "panelNhan",
    ["btnCoBan"] = "btnCoBan",
    ["btnDuyen"] = "btnDuyen",
    ["btnTangSao"] = "btnTangSao",
    ["btnNhan"] = "btnNhan",
    ["pannelTuongBoader"] = "pannelTuongBoader",
    ["itemStar"] = "itemStar",
    ["itemNhan"] = "itemNhan",
    ["panelNhan.panelEmpty.emptyTxt"] = "emptyTxt",
    ["panelNhan.panelEmpty.emptyBg"] = "emptyBg",
    ["panelNhan.listNhan"] = {
        varname = "listNhan",
        binds = {
            event = "extend",
            class = "listview",
            props = {
                data = bindHelper.self("wayDatas"),
                item = bindHelper.self("itemNhan"),
                itemAction = { isAction = true },
                onItem = function(list, node, k, v)
                    local btnTitle = node:get("btn", "btnTitle")
                    local childs = node:multiget("title", "txt", "btn", "list")
                    if matchLanguage({ "cn", "tw" }) then
                        local length = #v.title / 3
                        --if length == 2 then
                            text.addEffect(childs.title, { size = 80 })
                        --elseif length == 3 then
                        --    text.addEffect(childs.title, { size = 60 })
                        --elseif length == 4 or length == 5 then
                        --    text.addEffect(childs.title, { size = 40 })
                        --end
                    end
                    if matchLanguage({ "en" }) then
                        local length = math.floor(#v.title / 3)
                        --if length == 3 then
                            text.addEffect(childs.title, { size = 55 })
                        --elseif length == 4 then
                        --    text.addEffect(childs.title, { size = 38 })
                        --elseif length == 5 then
                        --    text.addEffect(childs.title, { size = 34 })
                        --elseif length > 5 then
                        --    text.addEffect(childs.title, { size = 30 })
                        --end
                    end
                    childs.title:text(v.title)
                    btnTitle:text(v.btnTitle)
                    if v.title == v.txt then
                        childs.txt:visible(false)
                    else
                        childs.txt:text(v.txt)
                    end

                    adapt.setTextAdaptWithSize(childs.txt,
                            { size = cc.size(400, 200), vertical = "center", horizontal = "left" })

                    if v.typ == WAY_TYPE.LEAVEFOR then
                        node:get("btn"):loadTextureNormal("common/btn/btn_leave_1.png")
                    else
                        node:get("btn"):loadTextureNormal("common/btn/btn_normal.png")
                    end
                    if v.typ == WAY_TYPE.MOPUP then
                        cache.setShader(childs.btn, false, "normal")
                        text.addEffect(btnTitle, { color = cc.c4b(101, 63, 0, 255)})
                    elseif v.typ == WAY_TYPE.LEAVEFOR then
                        cache.setShader(childs.btn, false, "normal")
                        text.addEffect(btnTitle, { color = cc.c4b(59, 65, 104, 255)})
                    else
                        cache.setShader(childs.btn, false, "hsl_gray")
                        text.deleteAllEffect(btnTitle)
                        text.addEffect(btnTitle, { color = cc.c4b(115, 115, 115, 255)})
                    end
                    uiEasy.createItemsToList(list, childs.list, v.dropIds, { scale = 0.80, margin = 1 })
                    bind.touch(list, childs.btn, { methods = { ended = functools.partial(list.clickCell, k, v) } })
                    childs.list:setTouchEnabled(false)
                end,
            },
            handlers = {
                clickCell = bindHelper.self("onWayItemClick"),
            },
        },
    },
    ["bg"] = "bg",
    ["panelDuyen"] = "panelDuyen",
    ["panelDuyen.itemList"] = "itemList",
    ["panelDuyen.fetterIconItem"] = "fetterIconItem",
    ["panelDuyen.listFetter"] = {
        varname = "list",
        binds = {
            event = "extend",
            class = "listview",
            props = {
                data = bindHelper.self("fetterDatas"),
                subItem = bindHelper.self("fetterIconItem"),
                cardCfg = bindHelper.self("cardCfg"),
                pokedex = bindHelper.self("pokedex"),
                item = bindHelper.self("itemList"),
                itemAction = { isAction = true, alwaysShow = true },
                onItem = function(list, node, k, fetterID)
                    node:get("listFetterIcon"):setScrollBarEnabled(false)
                    local fetter = csv.fetter[fetterID]
                    local txtTitle = node:get("labelFetterName")
                    local labelLevel = node:get("labelLevel")
                    txtTitle:text(fetter.name)
                    if k < 10 then
                        labelLevel:text("0" .. k)
                    else
                        labelLevel:text(k)
                    end
                    --adapt.setTextScaleWithWidth(node:get("title.textName"), nil, 300)

                    local cardState = {}
                    table.insert(cardState, { id = list.cardCfg.id, state = list.pokedex[list.cardCfg.id] })
                    for i, v in ipairs(fetter.cards) do
                        table.insert(cardState, { id = v or list.cardCfg.id, state = list.pokedex[v] ~= nil })
                    end
                    for _, v in ipairs(cardState) do
                        local cardCfg = csv.cards[v.id]
                        local unitCfg = csv.unit[cardCfg.unitID]
                        local subItem = list.subItem:clone():show()
                        bind.extend(list, subItem:get("head"), {
                            class = "card_icon",
                            props = {
                                cardId = v.id,
                                rarity = unitCfg.rarity,
                                grayState = 2,
                                onNode = function(panel)
                                    panel:setAnchorPoint(cc.p(0.0, 0.0))
                                end,
                            }
                        })
                        node:get("listFetterIcon"):pushBackCustomItem(subItem)
                        node:get("listFetterIcon"):adaptTouchEnabled()
                    end

                    local strs = {}
                    for i, v in csvPairs(fetter.attrMap) do
                        local name = getLanguageAttr(i)
                        local str = string.format("#C0x5B545B#%s " .. gLanguageCsv.increase .. " %s", name,
                                dataEasy.getAttrValueString(i, v)) --EF3453
                        table.insert(strs, str)
                    end
                    local str = table.concat(strs, ", ")
                    local labelFeeterAttr = node:get("labelFeeterAttr")
                    rich.createWithWidth(str, 40, nil, labelFeeterAttr:width())
                        :setAnchorPoint(cc.p(0, 1))
                        :addTo(node, 0, "richtext")
                        :setVerticalAlignment(cc.VERTICAL_TEXT_ALIGNMENT_TOP)
                        :xy(60, 10)
                end,
            }
        },
    },


}

function CardInfoViewView:onWayItemClick(list, k, v)
    if v.typ == WAY_TYPE.LEAVEFOR then
        local isMegaConversion = csv.card_mega_convert[v.id]
        local isZawakeConversion = dataEasy.isZawakeFragment(self.fragInfo.id)
        if isMegaConversion then
            local data = {}
            data.id = v.id
            data.num = v.targetNum
            jumpEasy.jumpTo(v.produceGate, data)
        elseif isZawakeConversion then
            local params = {
                fragID = self.fragInfo.id,
                needNum = v.targetNum,
            }
            jumpEasy.jumpTo(v.produceGate, params)
        else
            jumpEasy.jumpTo(v.produceGate)
        end
    elseif v.typ == WAY_TYPE.MOPUP then
        self.gateId = v.gateId
        self:onSweepBtn()
    end
end

function CardInfoViewView:onSweepBtn()
    if not self:checkSweep() then
        return
    end

    local gateId = self.gateId
    local oldRoleLv = self.roleLv:read()
    local oldCapture = gGameModel.capture:read("limit_sprites")
    gGameApp:requestServer("/game/saodang", function(tb)
        local items = tb.view.result
        table.insert(items, { exp = 0, items = tb.view.extra, isExtra = true })
        gGameUI:stackUI("city.gate.sweep", nil, nil, {
            sweepData = items,
            oldRoleLv = oldRoleLv,
            cb = self:createHandler("onSweepBtn"),
            checkCb = self:createHandler("checkSweep"),
            hasExtra = true,
            from = "gainWay",
            targetNum = self.targetNum,
            targetId = self.fragInfo.id,
            oldCapture = oldCapture,
            gateId = gateId,
            curMopUpNum = self.curMopUpNum,
            isDouble = dataEasy.isGateIdDoubleDrop(gateId),
            catchup = tb.view.catchup
        })
    end, gateId, self.curMopUpNum, self.fragInfo.id,
            self.targetNum and (self.targetNum - dataEasy.getNumByKey(self.fragInfo.id)))
    return true
end

function CardInfoViewView:checkSweep()
    local staminaCost = csv.scene_conf[self.gateId].staminaCost
    local curStamina = dataEasy.getStamina()
    if curStamina < staminaCost then
        gGameUI:stackUI("common.gain_stamina")
        return false
    end
    --self.curMopUpNum = math.min(self.mopUpNum, math.floor(curStamina / staminaCost)) -- 本次可扫荡最大次数
    self.curMopUpNum = 1

    local sceneCsv = csv.scene_conf[self.gateId]
    local surplusTimes = sceneCsv.dayChallengeMax
    if self.gateTimes:read()[self.gateId] then
        surplusTimes = surplusTimes - self.gateTimes:read()[self.gateId]
    end
    -- 今天的重置次数
    local buyHerogateTimes = self.buyHerogateTimes:read()[self.gateId] or 0
    local state, paramMaps, count = dataEasy.isDoubleHuodong("heroGateTimes")
    if state then
        for i, paramMap in pairs(paramMaps) do
            local addTimes = paramMap["count"]
            if addTimes and addTimes > 0 then
                if buyHerogateTimes == 0 then
                    surplusTimes = surplusTimes + addTimes
                end
            end
        end
    end
    if surplusTimes and surplusTimes <= 0 then
        self:onTimesBtnClick(surplusTimes)
        return false
    end

    return true
end

function CardInfoViewView:onTimesBtnClick(surplusTimes)
    local buyTimeMax = gVipCsv[gGameModel.role:read("vip_level")].buyHeroGateTimes
    local buyHerogateTimes = self.buyHerogateTimes:read()
    if (buyHerogateTimes[self.gateId] or 0) >= buyTimeMax then
        gGameUI:showTip(gLanguageCsv.herogateBuyMax)
        return
    end
    if surplusTimes > 0 then
        gGameUI:showTip(gLanguageCsv.haveChallengeTimesUnused)
        return
    end
    local strs = {
        "#C0x5b545b#" ..
                string.format(gLanguageCsv.resetNumberEliteLevels1,
                        gCostCsv.herogate_buy_cost[(buyHerogateTimes[self.gateId] or 0) + 1]),
        "#C0x5b545b#" ..
                string.format(gLanguageCsv.resetNumberEliteLevels2, buyHerogateTimes[self.gateId] or 0, buyTimeMax)
    }
    gGameUI:showDialog({
        content = strs,
        cb = function()
            gGameApp:requestServer("/game/role/hero_gate/buy", function()
                gGameUI:showTip(gLanguageCsv.resetSuccess)
            end, self.gateId)
        end,
        btnType = 2,
        isRich = true,
        dialogParams = { clickClose = false }
    })
end

function CardInfoViewView:initWayDatas(info)
    local key = self.fragInfo.id
    local cfg = dataEasy.getCfgByKey(key)
    for i = 1, math.huge do
        if not cfg["produceGate" .. i] or cfg["produceGate" .. i] == "" then
            break
        end
        local arr = string.split(cfg["produceGate" .. i], "-")
        local typ = WAY_TYPE.NOTOPEN
        local titleKey = arr[1]
        local gateId
        local describe = ""
        local dropIds = {}
        local title = gLanguageCsv[titleKey] or ""
        -- 冒险相关统一显示冒险
        if ADVENTURE_TITLE[titleKey] then
            title = ADVENTURE_TITLE[titleKey]
        end
        if ZAWAKE_TITLE[titleKey] then
            title = ZAWAKE_TITLE[titleKey]
        end
        --长度为1是关卡
        if #arr == 1 and tonumber(arr[1]) then
            gateId = tonumber(arr[1])
            describe, typ = self:getGateDescribe(gateId)
            titleKey = "gate"
            local _type, chapterId, _, _title = dataEasy.getChapterInfoByGateID(gateId)
            title = _title
            if self:checkCanSweep(gateId) then
                typ = WAY_TYPE.MOPUP
                info[_type] = true
            end

            local secenInfo = csv.scene_conf[gateId] or {}
            for k, v in csvMapPairs(secenInfo.dropIds or {}) do
                table.insert(dropIds, { key = k, num = v })
            end
            self.chapterType = _type
        else
            local titles = self.WAY_TITLE[titleKey]
            if type(titles) == "table" and arr[2] then
                describe = titles[tonumber(arr[2]) or arr[2]] or ""
            else
                local str = titles
                if type(titles) == "table" or titles == nil then
                    str = gLanguageCsv[titleKey]
                end
                describe = string.format(gLanguageCsv.acquiringWay, str)
            end
            typ = WAY_TYPE.LEAVEFOR
            if titleKey == "shop" then
                local shopId = tonumber(arr[2]) or 1
                local unlockKey = SHOP_UNLOCK_KEY[shopId].unlockKey
                if unlockKey and (not dataEasy.isUnlock(unlockKey) or (SHOP_UNLOCK_KEY[shopId].mustHaveUion == true and not self.unionId:read())) then
                    --商店未开放
                    typ = WAY_TYPE.NOTOPEN
                end
            end
        end
        if self:canShow(arr) then
            table.insert(self.wayDatas, {
                title = title,
                btnTitle = WAY_BTNTITLE[typ],
                produceGate = cfg["produceGate" .. i],
                typ = typ,
                txt = describe,
                gateId = gateId,
                dropIds = dropIds,
                id = cfg.id,
                targetNum = self.targetNum,
            })
        end
    end
end

function CardInfoViewView:canShow(arr)
    local canShow = true
    if #arr == 1 and tonumber(arr[1]) then
        --长度为1是关卡
        local gateId = tonumber(arr[1])
        local _type, chapterId = dataEasy.getChapterInfoByGateID(gateId)
        if chapterId ~= 0 then
            local gateCsv = self.sectionCsv[_type][chapterId]
            canShow = (gateCsv ~= nil)
        end
    elseif arr[1] == "shop" then
        local shopId = tonumber(arr[2]) or 1
        local unlockKey = SHOP_UNLOCK_KEY[shopId].unlockKey
        if unlockKey and not dataEasy.isUnlock(unlockKey) then
            canShow = false
        end
        -- 多语言筛选后的商店中没有该内容道具
        if not gShopGainMap[self.fragInfo.id] then
            canShow = false
        end
    end
    -- 抽卡预览里没有的不显示
    if arr[1] == "drawCard" then
        if not gDrawPreviewMap[self.fragInfo.id] then
            canShow = false
        end
    end
    return canShow
end

function CardInfoViewView:getGateDescribe(gateId)
    local _type, chapterId, id, title = dataEasy.getChapterInfoByGateID(gateId)
    local typ = WAY_TYPE.NOTOPEN
    local describe = ""
    if chapterId == 0 then
        typ = WAY_TYPE.LEAVEFOR
        if _type == 1 then
            describe = string.format("%s%s", gLanguageCsv.gateStory, gLanguageCsv.gate)
        else
            describe = string.format("%s%s", gLanguageCsv.gateDifficult, gLanguageCsv.gate)
        end
    else
        local gateCsv = self.sectionCsv[_type][chapterId]
        if gateCsv then
            describe = gateCsv.cfg.name
            typ = WAY_TYPE.LEAVEFOR
            if id ~= 0 then
                describe = describe .. " " .. chapterId .. "-" .. id
                typ = WAY_TYPE.NOTOPEN
            end
        end
    end
    return describe, typ
end

function CardInfoViewView:initEmpty()
    local cfg = dataEasy.getCfgByKey(self.fragInfo.id)
    local isShowTip = #self.wayDatas == 0

    self.emptyTxt:visible(isShowTip)
    self.emptyBg:visible(isShowTip)
    self.emptyTxt:text(cfg.produceDesc or "")
    self.emptyBg:scale((self.emptyTxt:width() + 500) / self.emptyBg:width())
end

function CardInfoViewView:getSectionCsv()
    local sectionCsv = {}
    for k, v in csvPairs(csv.world_map) do
        local data = {}
        data.cfg = v
        if data.cfg.chapterType then
            if not sectionCsv[data.cfg.chapterType] then
                sectionCsv[data.cfg.chapterType] = {}
            end
            data.sortIndex = k
            table.insert(sectionCsv[data.cfg.chapterType], data)
        end
    end
    for k, v in pairs(sectionCsv) do
        table.sort(v, function(a, b)
            return a.sortIndex < b.sortIndex
        end)
    end
    return sectionCsv
end

function CardInfoViewView:checkCanSweep(gateId)
    local gateStar = self.gateStar:read()
    return gateStar[gateId] and gateStar[gateId].star == 3
end

function CardInfoViewView:btnExit()
    self:close()
    --self:removeSelf()
end

function CardInfoViewView:onCreate(params)
    --params is itemData, reference: app/views/city/codex/view.lua:422
    local pcardID = params.cardID
    self.isHas = gGameModel.role:getIdler("pokedex"):read()[pcardID] ~= nil
    params = params or {}
    if params.num ~= nil then
        pcardID = params.num
        if type(params.num) == "table" and params.num.id ~= nil then
            pcardID = params.num.id
        end
    end

    if params.showBG then
        self.bg:visible(false)
        -- gGameUI.topuiManager:createView("title", self, { onClose = self:createHandler("onClose") })
        --     :init({ title = gLanguageCsv.detail, subTitle = "CodexView" })
    else
        self.bg:visible(false)
    end


    self.fetterDatas = idlers.newWithMap({})
    self.pokedex = idlers.newWithMap({})
    self.cardID = idler.new(pcardID)
    idlereasy.when(self.cardID, function(_, cardID)
        if not cardID or cardID == 0 then
            return
        end
        self:initCard(cardID)
        self:initCardInfo()
        self:initSkill()
        self:loadTuong()
        self:initBtnTab()
        self:initTangSaoInfo()
    end)

    self.itemNhan:get("list"):setScrollBarEnabled(false)
    self.sectionCsv = self:getSectionCsv()
    self.wayDatas = {}
    local info = {}
    self.chapterType = 1
    self:initWayDatas(info)
    self:initEmpty()

    if itertools.size(info) > 1 then
        self.chapterType = 3
    else
        self.chapterType = next(info) or 1
    end

    Dialog.onCreate2(self)
end

function CardInfoViewView:initCard(cardID)
    self.cardCfg = csv.cards[cardID]
    self.unitCfg = csv.unit[self.cardCfg.unitID]
    local fragCfg = dataEasy.getCfgByKey(self.cardCfg.fragID)
    self.fragInfo = {
        id = self.cardCfg.fragID,
        name = fragCfg.name,
        num = dataEasy.getNumByKey(fragCfg.id),
        maxNum = fragCfg.combCount,
    }
    self.fetterDatas:update(self.cardCfg.fetterList)

    self.skillDatas = idlertable.new({})
    self.skillNoiTaiDatas = idlertable.new({})
    self.gateStar = gGameModel.role:getIdler("gate_star") -- 星星数量
    self.unionId = gGameModel.role:getIdler("union_db_id")

    self.roleLv = gGameModel.role:getIdler("level")
    self.vipLevel = gGameModel.role:getIdler("vip_level")
    self.stamina = gGameModel.role:getIdler("stamina")
    self.buyHerogateTimes = gGameModel.daily_record:getIdler("buy_herogate_times")
    self.gateTimes = gGameModel.daily_record:getIdler("gate_times")
    self.pokedex = gGameModel.role:getIdler("pokedex")
end

local Tabs = {
    btnCoBan = { btn = "btnCoBan", panel = "panelCoBan", txt = "labelCoBan" },
    btnDuyen = { btn = "btnDuyen", panel = "panelDuyen", txt = "labelDuyen" },
    btnTangSao = { btn = "btnTangSao", panel = "panelTangSao", txt = "labelTangSao" },
    btnNhan = { btn = "btnNhan", panel = "panelNhan", txt = "labelNhan" },
}
local activeTabBg = "3q/codex/bg_ttcb_btn_selected.png"
local inActiveTabBg = "3q/codex/bg_ttcb_btn_unselected.png"

function CardInfoViewView:initBtnTab()
    self:onBtnTabClick(Tabs.btnCoBan)

    bind.touch(self, self.btnCoBan, {
        methods = {
            ended = function()
                self:onBtnTabClick(Tabs.btnCoBan)
            end
        }
    })
    bind.touch(self, self.btnDuyen, {
        methods = {
            ended = function()
                self:onBtnTabClick(Tabs.btnDuyen)
            end
        }
    })
    bind.touch(self, self.btnTangSao, {
        methods = {
            ended = function()
                self:onBtnTabClick(Tabs.btnTangSao)
            end
        }
    })
    bind.touch(self, self.btnNhan, {
        methods = {
            ended = function()
                self:onBtnTabClick(Tabs.btnNhan)
            end
        }
    })
end

function CardInfoViewView:onBtnTabClick(tabval)
    for k, v in pairs(Tabs) do
        if v.btn == tabval.btn then
            self[v.btn]:loadTextureNormal(activeTabBg)
            self[v.panel]:visible(true)
            self[v.txt]:setTextColor(cc.c4b(107, 55, 7, 255))
        else
            self[v.btn]:loadTextureNormal(inActiveTabBg)
            self[v.panel]:visible(false)
            self[v.txt]:setTextColor(cc.c4b(226, 218, 205, 255))
        end
    end
end

function CardInfoViewView:initCardInfo()
    local rarityImageCfg = rarityConfigImages[self.unitCfg.rarity]
    self.imgTagRarity:texture(rarityImageCfg.tag)
    self.iconRarity:texture(rarityImageCfg.icon)
    self.labelCardName:text(self.cardCfg.name)

    if self.fragInfo.maxNum > 0 then
        local progressBar = self.progressBar
        progressBar:get("fragCount"):text(self.fragInfo.num .. "/" .. self.fragInfo.maxNum)
        local progressPercent = self.fragInfo.num / self.fragInfo.maxNum
        local maxProcessSize = progressBar:get("pgDark"):size()
        local pgLightWidth = maxProcessSize.width * progressPercent
        if pgLightWidth > maxProcessSize.width then
            pgLightWidth = maxProcessSize.width
        end
        progressBar:get("pgLight"):size(cc.size(pgLightWidth, maxProcessSize.height))
    else
        local progressBar = self.progressBar
        progressBar:get("fragCount"):text("0/0")
        progressBar:get("pgLight"):visible(false)
    end
    local keys = { "hpNum", "speedNum", "damageNum", "defenceNum", "specialDamageNum", "specialDefenceNum" }
    local panelThuocTinh = self.panelThuocTinh:get(v)
    local specValue = self.cardCfg.specValue or {}

    local function getStatRank(value)
        return (value >= 141 and "SS")
            or (value >= 121 and "S")
            or (value >= 101 and "A")
            or (value >= 81 and "B")
            or (value >= 61 and "C")
            or (value >= 41 and "D")
            or "F"
    end
    
    for i, v in ipairs(keys) do
        panelThuocTinh:get(v):text(getStatRank(tonumber(specValue[i])))
    end
    -- for i, v in ipairs(keys) do
    --     panelThuocTinh:get(v):text(math.floor(specValue[i]))
    -- end

    self.labelGioiThieu:text(self.cardCfg.introduction)
end

function CardInfoViewView:setEffectPanel(effectCfg)
    local keys = itertools.keys(effectCfg)
    local c1 = "#C0x5B545B##F44#"
    local c2 = "#C0x60C456##F44#"
    table.sort(keys)
    local star = self.cardCfg.star or 1
    local effectBasicStart = {}
    local effectAdvanceStart = {}

    for k1, i in ipairs(keys) do
        local v = effectCfg[i]
        local str = ""
        local args = {}
        local color = "#C0x5B545B#"
        for k, v in csvPairs(v.attrNum) do
            local attr = game.ATTRDEF_TABLE[k]
            local name = gLanguageCsv["attr" .. string.caption(attr)]
            local effectNum = "+" .. dataEasy.getAttrValueString(k, v)
            table.insert(args, c1 .. name .. c2 .. effectNum)
        end

        str = table.concat(args, " ")
        str = str .. c1 .. (v.effectDesc or "")
        local data = {
            str = str,
            star = i,
            color = color,
        }
        if i <= star then
            table.insert(effectBasicStart, data)
        else
            table.insert(effectAdvanceStart, data)
        end
    end

    self.effectBasicStart:set(effectBasicStart)
    self.effectAdvanceStart:set(effectAdvanceStart)
end

function CardInfoViewView:initTangSaoInfo()
    local panelTangSao = self.panelTangSao
    self.effectBasicStart = idlertable.new({})
    self.effectAdvanceStart = idlertable.new({})
    self:setEffectPanel(gStarEffectCsv[self.cardCfg.starEffectIndex])

    local listBasicBind = {
        class = "listview",
        props = {
            data = self.effectBasicStart,
            item = self.itemStar,
            itemAction = { isAction = true },
            onItem = function(list, node, k, v)
                local childs = node:multiget("textNote", "iconStar")
                local richText1 = rich.createWithWidth(string.format("%s%s", v.color, "x" .. v.star .. ": "), 44, nil,
                        100, 5)
                                      :anchorPoint(0, 0.5)
                                      :addTo(childs.textNote, 6)

                local richText2 = rich.createWithWidth(v.str, 44, nil, 678, 5)
                                      :anchorPoint(0, 0.5)
                                      :addTo(childs.textNote, 6)

                local height1 = richText1:size().height - 46
                local height2 = richText2:size().height - 46
                richText1:y(height2 - height1 - 0)
                richText2:y(height2 / 2)
                richText2:x(richText2:x() + 100)
                node:size(879, height2 + 74)

                local starIconPath = string.format("common/icon/icon_star.png")
                childs.iconStar:texture(starIconPath):y(node:size().height / 2 + height2 / 2)
                childs.textNote:text("")
            end
        }
    }
    bind.extend(self, panelTangSao:get("listStartBasic"), listBasicBind)
    local listAdvanceBind = {
        class = "listview",
        props = {
            data = self.effectAdvanceStart,
            item = self.itemStar,
            itemAction = { isAction = true },
            onItem = function(list, node, k, v)
                local childs = node:multiget("textNote", "iconStar")
                local richText1 = rich.createWithWidth(string.format("%s%s", v.color, "x" .. v.star .. ": "), 44, nil,
                        100, 5)
                                      :anchorPoint(0, 0.5)
                                      :addTo(childs.textNote, 6)

                local richText2 = rich.createWithWidth(v.str, 44, nil, 678, 5)
                                      :anchorPoint(0, 0.5)
                                      :addTo(childs.textNote, 6)

                local height1 = richText1:size().height - 46
                local height2 = richText2:size().height - 46
                richText1:y(height2 - height1 - 0)
                richText2:y(height2 / 2)
                richText2:x(richText2:x() + 100)
                node:size(879, height2 + 74)

                local starIconPath = string.format("common/icon/icon_star.png")
                childs.iconStar:texture(starIconPath):y(node:size().height / 2 + height2 / 2)
                childs.textNote:text("")
            end
        }
    }
    bind.extend(self, panelTangSao:get("lsitStarAdvance"), listAdvanceBind)
end

function CardInfoViewView:initSkill()
    local skillDatas = {}
    local skillNoiTaiDatas = {}
    local cardcfg = csv.cards[self.cardCfg.id]
    for k, v in csvPairs(cardcfg.skillList) do
        local passive = 1
        if csv.skill[v].id == 61101 or csv.skill[v].id == 61102 or csv.skill[v].id == 61151 or csv.skill[v].id == 61152 then
            passive = 2
            table.insert(skillNoiTaiDatas, {
                skillId = v,
                skillLevel = 1,
                skillPassive = passive,
            })
        else
            table.insert(skillDatas, {
                skillId = v,
                skillLevel = 1,
                skillPassive = passive,
            })
        end
    end

    self.skillDatas:set(skillDatas)
    self.skillNoiTaiDatas:set(skillNoiTaiDatas)
end

function CardInfoViewView:onShowSkillInfo(node, skillInfo)
    if not self.isHas then
        gGameUI:showTip(gLanguageCsv.skillInfoMystery)
        return
    end
    local view = gGameUI:stackUI("common.skill_detail", nil, { clickClose = true }, {
        skillId = skillInfo.skillId,
        skillLevel = skillInfo.skillLevel,
        cardId = self.cardCfg.id,
        star = uiEasy.getMaxStar(self.cardCfg.id)
    }, "handbook")
    local panel = view:getResourceNode()
    local x, y = panel:xy()
    panel:x(x - 165)
end

local heroSpineViewInfo = {
    [1611] = {
        scale = 8,
    },
    [1271] = {
        scale = 8,
    },
    [2351] = {
        scale = 8.5,
    },
    [761] = {
        scale = 7.5,
        height = 2000,
    },
    [1971] = {
        scale = 7,
        height = 2000,
        width = -150
    },
    [2391] = {
        scale = 6.3,
        height = 2000,
        width = -50
    },
    [1991] = {
        scale = 8.2,
    },
    [2361] = {
        scale = 8,
    },
    [2431] = {
        scale = 8,
    },
    [3211] = {
        scale = 8,
    },
    [3221] = {
        scale = 8.2,
    },
    [3241] = {
        scale = 8.5,
    },
    [3601] = {
        scale = 7.5,
        width = 80,
    },
    [3631] = {
        scale = 8.2,
    },
    [4121] = {
        scale = 8,
    },
    [161] = {
        scale = 8.8,
    },
    [261] = {
        scale = 8.3,
    },
    [331] = {
        scale = 8.3,
    },
    [381] = {
        scale = 8.3,
    },
    [401] = {
        scale = 8.2,
    },
    [501] = {
        scale = 8.1,
    },
    [571] = {
        scale = 8.2,
    },
    [591] = {
        scale = 8,
    },
    [601] = {
        width = -50,
        scale = 7.8,
    },
    [611] = {
        scale = 10,
        width = -55,
    },
    --lỗi không scale được
    [632] = {
        scale = 8.3,
        width = 100,
    },
    [641] = {
        scale = 8.3,
        width = -40,
    },
    [701] = {
        scale = 8.2,
    },
    [711] = {
        scale = 8.4,
        width = 30,
    },
    [721] = {
        scale = 8.4,
        width = 40,
    },
    [731] = {
        scale = 8.2,
        width = -20,
    },
    [741] = {
        scale = 8.4,
    },
    [771] = {
        scale = 8.3,
    },
    [861] = {
        scale = 8,
        width = 40,
    },
    [1071] = {
        width = -100,
    },
    [1081] = {
        scale = 8.2,
        width = -40,
    },
    [1231] = {
        scale = 8.1,
    },
    [1251] = {
        scale = 8.6,
        width = -50,
    },
    [1301] = {
        scale = 8.1,
        width = -30,
    },
    [1311] = {
        scale = 8.4,
        width = -60,
    },
    [1391] = {
        scale = 8.6,
        width = -40,
    },
    [1421] = {
        scale = 8.4,
        width = 40,
    },
    [1501] = {
        scale = 8.2,
    },
    [1771] = {
        scale = 8.2,
        width = -20,
    },
    [1841] = {
        scale = 8,
        width = -40,
    },
    [1891] = {
        scale = 8.3,
    },
    [1911] = {
        scale = 8.2,
    },
    [2241] = {
        scale = 8.3,
        width = -30,
    },
    [2401] = {
        scale = 8.4,
        width = 40,
    },
    [2721] = {
        scale = 8.3,
    },
    [3001] = {
        scale = 7.8,
    },
    [3151] = {
        scale = 8.4,
    },
    [3161] = {
        scale = 8.5,
    },
    [3181] = {
        scale = 8.4,
        width = -50,
    },
    [3301] = {
        scale = 8.4,
        width = 30,
    },
    [3661] = {
        scale = 8.4,
    },
    [3841] = {
        scale = 8.4,
        width = 30,
    },
    [1] = {
        scale = 9.5,
        width = 40,
    },
    [11] = {
        scale = 8,
    },
    [21] = {
        scale = 8.4,
        width = -20,
    },
    [31] = {
        scale = 8,
        width = -40,
    },
    [51] = {
        scale = 8.2,
        width = 35,
    },
    [61] = {
        scale = 8.1,
        width = -10,
    },
    [91] = {
        scale = 8.4,
        width = -20,
    },
    [151] = {
        scale = 8.3,
        width = 10,
    },
    [171] = {
        scale = 8.4,
    },
    [271] = {
        scale = 8.2,
    },
    [281] = {
        scale = 8.4,
        width = 30,
    },
    [301] = {
        scale = 8.5,
        width = 10,
    },
    [341] = {
        scale = 8.1,
    },
    [461] = {
        scale = 8.1,
    },
    [471] = {
        scale = 8.1,
    },
    [551] = {
        scale = 8.1,
    },
    [691] = {
        scale = 8.5,
    },
    [781] = {
        scale = 8.1,
    },
    [791] = {
        scale = 8.1,
        width = 30,
    },
    [801] = {
        scale = 8.1,
        width = 20,
    },
    [891] = {
        scale = 8.2,
        width = -25,
    },
    [1351] = {
        scale = 8.4,
        width = -25,
    },
    [1851] = {
        scale = 8.2,
        width = -25,
    },
    [1861] = {
        scale = 8.4,
        width = 15,
    },
    [2481] = {
        scale = 8.2,
        width = -25,
    },
    [2591] = {
        scale = 8.2,
    },
    [2611] = {
        scale = 8.2,
        width = -25,
    },
    [3011] = {
        scale = 8.2,
        width = 60,
    },
    [3471] = {
        scale = 8,
        width = -20,
    },
    [391] = {
        scale = 8.1,
    },
    --[631] = {
    --    scale = 20.0,
    --},
    [1171] = {
        scale = 8,
    },
    [1291] = {
        scale = 8.2,
        width = -20,
    },
    [2631] = {
        scale = 8.2,
    },
    [371] = {
        scale = 8.8,
        width = -35,
    },
    [241] = {
        scale = 8.8,
    },
    [1241] = {
        scale = 8.8,
        width = -35,
    },
    [2071] = {
        scale = 8.1,
        width = -35,
    },
    [3281] = {
        scale = 8.2,
        width = 40,
    },
}

local function showHeroSpine(view, unitInfo, size)
    local unitRes = unitInfo.unitRes
    local unitResBig = string.gsub(unitRes, "(.skel)$", "Big%1")
    -- print("load spine unitResBig: ", unitResBig)
    -- print("load spine cardID: ", unitInfo.id)

    local scale = 9
    local height = size.height - 2300
    local width = size.width / 2
    if heroSpineViewInfo[unitInfo.id] then
        if heroSpineViewInfo[unitInfo.id].scale ~= nil then
            scale = heroSpineViewInfo[unitInfo.id].scale
        end
        if heroSpineViewInfo[unitInfo.id].height ~= nil then
            height = size.height - heroSpineViewInfo[unitInfo.id].height
        end

        if heroSpineViewInfo[unitInfo.id].width ~= nil then
            width = (size.width / 2) + heroSpineViewInfo[unitInfo.id].width
        end
    end

    local isBig = cc.FileUtils:getInstance():isFileExist(unitResBig)
    if isBig then
        return widget.addAnimation(view, unitResBig, "standby_loop1")
                     :scale(scale):xy(width, height)
    else
        return widget.addAnimation(view, unitRes, "standby_loop")
                     :scale(scale):xy(width, height)
    end
end

function CardInfoViewView:loadTuong()
    if self.sprite then
        self.sprite:removeFromParent()
    end
    local size = self.pannelTuongBoader:size()

    self.sprite = showHeroSpine(self.pannelTuongBoader:get("view"), self.unitCfg, size)
    if self.voice then
        audio.stopSound(self.voice)
        self.voice = nil
    end
    self.voice = audio.playEffectWithWeekBGM(self.unitCfg.voice)
end

function CardInfoViewView:onClose()
    local cb = self.closeHandler
    Dialog.onClose(self)
    if cb then
        cb()
    end
    audio.stopAllSounds()
    audio.stopAllSounds()
end

return CardInfoViewView
