{"classname": null, "name": null, "animation": {"classname": null, "name": "AnimationManager", "actionlist": []}, "dataScale": 1, "designHeight": 1440, "designWidth": 2560, "textures": [], "texturesPng": ["city/embattle/bg_img.jpg", "3q/common/bg_gongneng_01.png", "common/btn/btn_arrow.png", "3q/icon_shanling.png", "city/embattle/bg_lucChien1.png", "3q/common/txt_zhanli.png", "3q/common/pic_xuanzhong.png", "3q/common/pic_xuanzhong_02.png", "3q/106.png", "city/embattle/img_base_bottom.png", "3q/stat/icon_life.png", "city/embattle/bg_5.png", "city/embattle/img_base_drag.png", "city/adventure/clone_battle/box_zhanli_bg.png", "city/pvp/online_fight/ban/logo_sxd0.png", "common/icon/attr/icon_combat.png", "common/box/box_panel_1.png", "city/adventure/clone_battle/bg_yuansu_biandui.jpg", "config/embattle/icon_gh.png", "city/embattle/bg_zhuxiandi.png", "city/embattle/btn.png", "common/btn/btn_normal.png", "3q/icon_xuanzhen.png"], "version": "*******", "widgetTree": {"classname": "Panel", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "imgBG", "ZOrder": 0, "actiontag": 24551280, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 640, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 2.29999971, "scaleY": 2.29999971, "sizePercentX": 0.5414063, "sizePercentY": 0.444444448, "sizeType": 0, "tag": 727806583, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 1386, "x": 0, "y": 0, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "city/embattle/bg_img.jpg", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 640, "scale9Width": 1386}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textLimit", "ZOrder": 2, "actiontag": 6022779, "anchorPointX": 0, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 84, "colorG": 91, "colorR": 84, "customProperty": "", "flipX": false, "flipY": false, "height": 0, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.236718744, "positionPercentY": -0.2375, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 3.90624973e-06, "sizePercentY": 6.944444e-06, "sizeType": 0, "tag": 727822313, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 0, "x": -606, "y": -342, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 40, "hAlignment": 0, "text": "", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textTip", "ZOrder": 11, "actiontag": 10747939, "anchorPointX": 0, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 52, "colorG": 89, "colorR": 226, "customProperty": "", "flipX": false, "flipY": false, "height": 40, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.19921875, "positionPercentY": -0.3090278, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.191796869, "sizePercentY": 0.027777778, "sizeType": 0, "tag": 727822322, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 491, "x": 510, "y": -445, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 35, "hAlignment": 0, "text": "* <PERSON><PERSON><PERSON> thả để tinh chỉnh đội hình", "touchScaleEnable": false, "vAlignment": 1}}, {"classname": "<PERSON><PERSON>", "name": null, "children": [{"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textNote", "ZOrder": 1, "actiontag": 19489060, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 196, "colorG": 241, "colorR": 252, "customProperty": "", "flipX": false, "flipY": false, "height": 47, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": -0.4189189, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 1.18589747, "sizePercentY": 0.317567557, "sizeType": 0, "tag": 727816444, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 185, "x": 0, "y": -62, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 42, "hAlignment": 0, "text": "<PERSON><PERSON>", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "imgIcon", "ZOrder": 2, "actiontag": 18296119, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 59, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.615384638, "positionPercentY": 0, "positionType": 0, "rotation": 90, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.493589729, "sizePercentY": 0.398648649, "sizeType": 0, "tag": 727816445, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 77, "x": 96, "y": 0, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "common/btn/btn_arrow.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 59, "scale9Width": 77}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "Image_78", "ZOrder": 0, "actiontag": 61343110, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 74, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1.5, "scaleY": 1.5, "sizePercentX": 0.5, "sizePercentY": 0.5, "sizeType": 0, "tag": 727832006, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 78, "x": 0, "y": 0, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/icon_shanling.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 74, "scale9Width": 78}}], "options": {"__type": "ButtonSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "<PERSON><PERSON>", "name": "btnJump", "ZOrder": 2, "actiontag": 49174790, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSButton", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 148, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.411328137, "positionPercentY": 0.3951389, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.0609375, "sizePercentY": 0.102777779, "sizeType": 0, "tag": 727816443, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 156, "x": 1053, "y": 569, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 106, "capInsetsY": 61, "disabled": null, "disabledData": {"path": null, "plistFile": null, "resourceType": 0}, "fontName": "font/youmi.ttf", "fontSize": 14, "fontType": 0, "normal": null, "normalData": {"path": "3q/common/bg_gongneng_01.png", "plistFile": "", "resourceType": 0}, "pressed": null, "pressedData": {"path": null, "plistFile": null, "resourceType": 0}, "scale9Enable": false, "scale9Height": 148, "scale9Width": 156, "text": "", "textColorB": 255, "textColorG": 255, "textColorR": 255}}, {"classname": "Panel", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "imgIcon", "ZOrder": 1, "actiontag": 44666688, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 77, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 2, "scaleY": 2, "sizePercentX": 0.502564132, "sizePercentY": 0.48125, "sizeType": 0, "tag": 727816447, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 392, "x": 0, "y": 0, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "city/embattle/bg_lucChien1.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 77, "scale9Width": 392}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textFightNote", "ZOrder": 2, "actiontag": 31928366, "anchorPointX": 0, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 16, "colorG": 242, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 67, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.401282042, "positionPercentY": 0.1875, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.3653846, "sizePercentY": 0.41875, "sizeType": 0, "tag": 727816448, "touchAble": false, "useMergedTexture": false, "visible": false, "width": 285, "x": -313, "y": 30, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 60, "hAlignment": 0, "text": "<PERSON><PERSON><PERSON>:", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textFightPoint", "ZOrder": 2, "actiontag": 10027156, "anchorPointX": 0, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 0, "colorG": 127, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 67, "ignoreSize": true, "layoutParameter": null, "opacity": 0, "positionPercentX": -0.115384616, "positionPercentY": 0.2, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.169230774, "sizePercentY": 0.41875, "sizeType": 0, "tag": 727816450, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 132, "x": -90, "y": 32, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/OkamiThuPhap.ttf", "fontSize": 60, "hAlignment": 0, "text": "9999", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textNote", "ZOrder": 2, "actiontag": 31854851, "anchorPointX": 1, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 45, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.0435897447, "positionPercentY": -0.26875, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.2871795, "sizePercentY": 0.28125, "sizeType": 0, "tag": 727822315, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 224, "x": -34, "y": -43, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 40, "hAlignment": 0, "text": "<PERSON><PERSON>:", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textNum", "ZOrder": 2, "actiontag": 2616925, "anchorPointX": 0, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 45, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.0179487187, "positionPercentY": -0.26875, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.0705128238, "sizePercentY": 0.28125, "sizeType": 0, "tag": 727822317, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 55, "x": -14, "y": -43, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 40, "hAlignment": 0, "text": "0/6", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "Image_79", "ZOrder": 1, "actiontag": 57260009, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 94, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.326923072, "positionPercentY": 0.175, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.105128206, "sizePercentY": 0.5875, "sizeType": 0, "tag": 727832077, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 82, "x": -255, "y": 28, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/common/txt_zhanli.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 94, "scale9Width": 82}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "fightNote", "ZOrder": 2, "actiontag": 19452882, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 160, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.408593744, "positionPercentY": 0.115277775, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.3046875, "sizePercentY": 0.111111112, "sizeType": 0, "tag": 727816446, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 780, "x": 1046, "y": 166, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "Panel", "name": null, "children": [{"classname": "CheckBox", "name": null, "children": [], "options": {"__type": "CheckBoxSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "CheckBox", "name": "checkBox", "ZOrder": 0, "actiontag": 58827231, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSCheckBox", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 46, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.422222227, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1.5, "scaleY": 1.5, "sizePercentX": 0.127777785, "sizePercentY": 0.6571429, "sizeType": 0, "tag": 727816899, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 46, "x": -152, "y": 0, "backGroundBox": null, "backGroundBoxData": {"path": "3q/common/pic_xuanzhong.png", "plistFile": "", "resourceType": 0}, "backGroundBoxDisabled": null, "backGroundBoxDisabledData": {"path": null, "plistFile": null, "resourceType": 0}, "backGroundBoxSelected": null, "backGroundBoxSelectedData": {"path": null, "plistFile": null, "resourceType": 0}, "frontCross": null, "frontCrossData": {"path": "3q/common/pic_xuanzhong_02.png", "plistFile": "", "resourceType": 0}, "frontCrossDisabled": null, "frontCrossDisabledData": {"path": null, "plistFile": null, "resourceType": 0}, "selectedState": true}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "text", "ZOrder": 1, "actiontag": 53013653, "anchorPointX": 0, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 186, "colorG": 226, "colorR": 237, "customProperty": "", "flipX": false, "flipY": false, "height": 45, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.330555558, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 1.11111116, "sizePercentY": 0.642857134, "sizeType": 0, "tag": 727816898, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 400, "x": -119, "y": 0, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 40, "hAlignment": 0, "text": "Sử dụng đội hình ch<PERSON>h", "touchScaleEnable": false, "vAlignment": 0}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "useDefaultBattle", "ZOrder": 1, "actiontag": 53839548, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 70, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.425, "positionPercentY": -0.256944448, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.140625, "sizePercentY": 0.0486111119, "sizeType": 0, "tag": 727816896, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 360, "x": 1088, "y": -370, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "ImageView", "name": null, "children": [{"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textNote", "ZOrder": 1, "actiontag": 23313415, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 133, "colorG": 128, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 57, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": -0.6125, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 1.425, "sizePercentY": 0.35625, "sizeType": 0, "tag": 727816508, "touchAble": false, "useMergedTexture": false, "visible": false, "width": 228, "x": 0, "y": -98, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 50, "hAlignment": 0, "text": "Challenge", "touchScaleEnable": false, "vAlignment": 0}}], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "btnChallenge", "ZOrder": 12, "actiontag": 53343963, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 160, "ignoreSize": false, "layoutParameter": null, "opacity": 0, "positionPercentX": 0.428125, "positionPercentY": -0.14791666, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.0625, "sizePercentY": 0.111111112, "sizeType": 0, "tag": 727830326, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 160, "x": 1096, "y": -213, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 106, "capInsetsY": 61, "fileName": null, "fileNameData": {"path": "3q/106.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 160, "scale9Width": 160}}, {"classname": "Panel", "name": null, "children": [{"classname": "Panel", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "imgBg", "ZOrder": 1, "actiontag": 37079099, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 188, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": -0.04918033, "positionType": 0, "rotation": 0, "scaleX": 0.6999999, "scaleY": 0.6999999, "sizePercentX": 0.951111138, "sizePercentY": 0.616393447, "sizeType": 0, "tag": 727816465, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 428, "x": 0, "y": -15, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "city/embattle/img_base_bottom.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 188, "scale9Width": 428}}, {"classname": "Panel", "name": null, "children": [], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "pos", "ZOrder": 0, "actiontag": 22246058, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 200, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": -0.04918033, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.444444448, "sizePercentY": 0.6557377, "sizeType": 0, "tag": 727817623, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 200, "x": 0, "y": -15, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "item1", "ZOrder": 2, "actiontag": 51086111, "anchorPointX": 0.5, "anchorPointY": 0.3, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 305, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.194921881, "positionPercentY": 0.103472225, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.17578125, "sizePercentY": 0.211805552, "sizeType": 0, "tag": 727816464, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 450, "x": 499, "y": 149, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "Panel", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "imgBg", "ZOrder": 1, "actiontag": 27474565, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 188, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": -0.04918033, "positionType": 0, "rotation": 0, "scaleX": 0.6999999, "scaleY": 0.6999999, "sizePercentX": 0.951111138, "sizePercentY": 0.616393447, "sizeType": 0, "tag": 727818369, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 428, "x": 0, "y": -15, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "city/embattle/img_base_bottom.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 188, "scale9Width": 428}}, {"classname": "Panel", "name": null, "children": [], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "pos", "ZOrder": 0, "actiontag": 10847255, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 200, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": -0.04918033, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.444444448, "sizePercentY": 0.6557377, "sizeType": 0, "tag": 727818371, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 200, "x": 0, "y": -15, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "item4", "ZOrder": 2, "actiontag": 10509269, "anchorPointX": 0.5, "anchorPointY": 0.3, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 305, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.00546875, "positionPercentY": 0.10486111, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.17578125, "sizePercentY": 0.211805552, "sizeType": 0, "tag": 727818368, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 450, "x": -14, "y": 151, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "Panel", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "imgBg", "ZOrder": 1, "actiontag": 44691700, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 188, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": -0.08444444, "positionType": 0, "rotation": 0, "scaleX": 0.799999952, "scaleY": 0.799999952, "sizePercentX": 0.856, "sizePercentY": 0.835555553, "sizeType": 0, "tag": 727816471, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 428, "x": 0, "y": -19, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "city/embattle/img_base_bottom.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 188, "scale9Width": 428}}, {"classname": "Panel", "name": null, "children": [], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "pos", "ZOrder": 0, "actiontag": 35091369, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 200, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.4, "sizePercentY": 0.8888889, "sizeType": 0, "tag": 727817621, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 200, "x": 0, "y": 0, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "item2", "ZOrder": 4, "actiontag": 34791906, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 225, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.162109375, "positionPercentY": -0.010416667, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.1953125, "sizePercentY": 0.15625, "sizeType": 0, "tag": 727816470, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 500, "x": 415, "y": -15, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "Panel", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "imgBg", "ZOrder": 1, "actiontag": 2730676, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 188, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": -0.08444444, "positionType": 0, "rotation": 0, "scaleX": 0.799999952, "scaleY": 0.799999952, "sizePercentX": 0.856, "sizePercentY": 0.835555553, "sizeType": 0, "tag": 727816489, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 428, "x": 0, "y": -19, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "city/embattle/img_base_bottom.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 188, "scale9Width": 428}}, {"classname": "Panel", "name": null, "children": [], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "pos", "ZOrder": 0, "actiontag": 37732330, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 200, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.4, "sizePercentY": 0.8888889, "sizeType": 0, "tag": 727817616, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 200, "x": 0, "y": 0, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "item5", "ZOrder": 3, "actiontag": 45042651, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 225, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.078125, "positionPercentY": -0.009027778, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.1953125, "sizePercentY": 0.15625, "sizeType": 0, "tag": 727816488, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 500, "x": -200, "y": -13, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "Panel", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "imgBg", "ZOrder": 1, "actiontag": 24438705, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 188, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": -0.124444447, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.7781818, "sizePercentY": 0.835555553, "sizeType": 0, "tag": 727818393, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 428, "x": 0, "y": -28, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "city/embattle/img_base_bottom.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 188, "scale9Width": 428}}, {"classname": "Panel", "name": null, "children": [], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "pos", "ZOrder": 0, "actiontag": 33378069, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 200, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.00181818183, "positionPercentY": -0.124444447, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.363636374, "sizePercentY": 0.8888889, "sizeType": 0, "tag": 727818395, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 200, "x": -1, "y": -28, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "item3", "ZOrder": 6, "actiontag": 47454654, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 225, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.115234375, "positionPercentY": -0.14791666, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.21484375, "sizePercentY": 0.15625, "sizeType": 0, "tag": 727818392, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 550, "x": 295, "y": -213, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "Panel", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "imgBg", "ZOrder": 1, "actiontag": 35525810, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 188, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": -0.124444447, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.7781818, "sizePercentY": 0.835555553, "sizeType": 0, "tag": 727818385, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 428, "x": 0, "y": -28, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "city/embattle/img_base_bottom.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 188, "scale9Width": 428}}, {"classname": "Panel", "name": null, "children": [], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "pos", "ZOrder": 0, "actiontag": 59018395, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 200, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": -0.124444447, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.363636374, "sizePercentY": 0.8888889, "sizeType": 0, "tag": 727818387, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 200, "x": 0, "y": -28, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "item6", "ZOrder": 6, "actiontag": 11403852, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 225, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.1421875, "positionPercentY": -0.14791666, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.21484375, "sizePercentY": 0.15625, "sizeType": 0, "tag": 727818384, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 550, "x": -364, "y": -213, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "battlePanel", "ZOrder": 3, "actiontag": 18667810, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 1440, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0.00555555569, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 1, "sizePercentY": 1, "sizeType": 0, "tag": 727816463, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 2560, "x": 0, "y": 8, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "Panel", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "imgBg", "ZOrder": 1, "actiontag": 54770348, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 48, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.6956522, "sizePercentY": 0.6956522, "sizeType": 0, "tag": 727816497, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 48, "x": 0, "y": 0, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/stat/icon_life.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 48, "scale9Width": 48}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textNote", "ZOrder": 2, "actiontag": 31888324, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 237, "colorG": 252, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 35, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.0173913036, "positionPercentY": 0.130434781, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.6, "sizePercentY": 0.3043478, "sizeType": 0, "tag": 727816499, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 69, "x": 2, "y": 15, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 30, "hAlignment": 0, "text": "Front", "touchScaleEnable": false, "vAlignment": 0}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "ahead", "ZOrder": 4, "actiontag": 40195084, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 115, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.26953125, "positionPercentY": -0.214583337, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.044921875, "sizePercentY": 0.07986111, "sizeType": 0, "tag": 727816498, "touchAble": true, "useMergedTexture": false, "visible": false, "width": 115, "x": 690, "y": -309, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "Panel", "name": null, "children": [{"classname": "Panel", "name": null, "children": [{"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "name", "ZOrder": 2, "actiontag": 50151176, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 35, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0.0333333351, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.675, "sizePercentY": 0.5833333, "sizeType": 0, "tag": 727831969, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 135, "x": 0, "y": 2, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 30, "hAlignment": 0, "text": "Text Label", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "Image_136", "ZOrder": 0, "actiontag": 48965793, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 26, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.005, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 2, "scaleY": 2, "sizePercentX": 0.9, "sizePercentY": 0.433333337, "sizeType": 0, "tag": 727831971, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 180, "x": -1, "y": 0, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 50, "capInsetsY": 1, "fileName": null, "fileNameData": {"path": "city/embattle/bg_5.png", "plistFile": "", "resourceType": 0}, "scale9Enable": true, "scale9Height": 26, "scale9Width": 180}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "info", "ZOrder": 13, "actiontag": 7870247, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 60, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 2.59259248, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.634920657, "sizePercentY": 0.444444448, "sizeType": 0, "tag": 727831970, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 200, "x": 0, "y": 350, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "imgBg", "ZOrder": 1, "actiontag": 65905596, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 81, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0.13333334, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.847619057, "sizePercentY": 0.6, "sizeType": 0, "tag": 727816532, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 267, "x": 0, "y": 18, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "city/embattle/img_base_drag.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 81, "scale9Width": 267}}, {"classname": "Panel", "name": null, "children": [], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "icon", "ZOrder": 2, "actiontag": 58481665, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 100, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.317460328, "sizePercentY": 0.7407407, "sizeType": 0, "tag": 727816534, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 100, "x": 0, "y": 0, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "Panel", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "imgIcon", "ZOrder": 1, "actiontag": 46423253, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 48, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 1.11111116, "sizePercentY": 1.11111116, "sizeType": 0, "tag": 727819729, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 48, "x": 0, "y": 0, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/stat/icon_life.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 48, "scale9Width": 48}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "bg", "ZOrder": 0, "actiontag": 19127489, "anchorPointX": 0, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 86, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.2777778, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 4.16666651, "sizePercentY": 1.19444442, "sizeType": 0, "tag": 727819731, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 300, "x": -20, "y": 0, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 50, "capInsetsY": 43, "fileName": null, "fileNameData": {"path": "city/adventure/clone_battle/box_zhanli_bg.png", "plistFile": "", "resourceType": 0}, "scale9Enable": true, "scale9Height": 86, "scale9Width": 300}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "text", "ZOrder": 10, "actiontag": 18420279, "anchorPointX": 0, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 0, "colorG": 127, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 57, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.5555556, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 3.5, "sizePercentY": 0.7916667, "sizeType": 0, "tag": 727819732, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 252, "x": 40, "y": 0, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 50, "hAlignment": 0, "text": "123456789", "touchScaleEnable": false, "vAlignment": 0}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "fightPoint", "ZOrder": 10, "actiontag": 4912550, "anchorPointX": 0, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 72, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.368253976, "positionPercentY": 0.125925928, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.22857143, "sizePercentY": 0.533333361, "sizeType": 0, "tag": 727819730, "touchAble": true, "useMergedTexture": false, "visible": false, "width": 72, "x": -116, "y": 17, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "Panel", "name": null, "children": [{"classname": "Panel", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "bg", "ZOrder": 0, "actiontag": 64992778, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 48, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 0.8, "scaleY": 0.8, "sizePercentX": 0.8888889, "sizePercentY": 0.8888889, "sizeType": 0, "tag": 727830108, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 48, "x": 0, "y": 0, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/stat/icon_life.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 48, "scale9Width": 48}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "bg2", "ZOrder": 0, "actiontag": 877384, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 77, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.0222222228, "positionPercentY": 0.0222222228, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.855555534, "sizePercentY": 0.855555534, "sizeType": 0, "tag": 727830109, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 77, "x": 2, "y": 2, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "city/pvp/online_fight/ban/logo_sxd0.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 77, "scale9Width": 77}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "img", "ZOrder": 1, "actiontag": 7896460, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 92, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.0111111114, "positionPercentY": 0.0333333351, "positionType": 0, "rotation": 0, "scaleX": 0.64, "scaleY": 0.64, "sizePercentX": 1.02222228, "sizePercentY": 1.02222228, "sizeType": 0, "tag": 727830110, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 92, "x": 1, "y": 3, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "common/icon/attr/icon_combat.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 92, "scale9Width": 92}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "attr1", "ZOrder": 5, "actiontag": 2879553, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 90, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.26, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.6, "sizePercentY": 0.9, "sizeType": 0, "tag": 727830107, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 90, "x": -39, "y": 0, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "Panel", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "bg", "ZOrder": 0, "actiontag": 35633491, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 48, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 0.8, "scaleY": 0.8, "sizePercentX": 0.8888889, "sizePercentY": 0.8888889, "sizeType": 0, "tag": 727830112, "touchAble": false, "useMergedTexture": false, "visible": false, "width": 48, "x": 0, "y": 0, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/stat/icon_life.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 48, "scale9Width": 48}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "img", "ZOrder": 1, "actiontag": 52265969, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 92, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.0222222228, "positionPercentY": 0.0333333351, "positionType": 0, "rotation": 0, "scaleX": 0.64, "scaleY": 0.64, "sizePercentX": 1.02222228, "sizePercentY": 1.02222228, "sizeType": 0, "tag": 727830113, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 92, "x": 2, "y": 3, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "common/icon/attr/icon_combat.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 92, "scale9Width": 92}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "bg2", "ZOrder": 0, "actiontag": 631679, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 77, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.0222222228, "positionPercentY": 0.0333333351, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.855555534, "sizePercentY": 0.855555534, "sizeType": 0, "tag": 727830114, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 77, "x": 2, "y": 3, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "city/pvp/online_fight/ban/logo_sxd0.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 77, "scale9Width": 77}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "attr2", "ZOrder": 5, "actiontag": 3959276, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 90, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.2, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.6, "sizePercentY": 0.9, "sizeType": 0, "tag": 727830111, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 90, "x": 30, "y": 0, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "attrBg", "ZOrder": 5, "actiontag": 37602652, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 100, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.7047619, "positionPercentY": 0.7111111, "positionType": 0, "rotation": 0, "scaleX": 1.2, "scaleY": 1.2, "sizePercentX": 0.476190478, "sizePercentY": 0.7407407, "sizeType": 0, "tag": 727830106, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 150, "x": 222, "y": 96, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "spritePanel", "ZOrder": 4, "actiontag": 7322844, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 135, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.0265625, "positionPercentY": 0.606944442, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.123046875, "sizePercentY": 0.09375, "sizeType": 0, "tag": 727816531, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 315, "x": -68, "y": 874, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "Panel", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "imgBg", "ZOrder": 1, "actiontag": 10247930, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 48, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.6956522, "sizePercentY": 0.6956522, "sizeType": 0, "tag": 727816504, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 48, "x": 0, "y": 0, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/stat/icon_life.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 48, "scale9Width": 48}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textNote", "ZOrder": 2, "actiontag": 25122506, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 237, "colorG": 252, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 35, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.0173913036, "positionPercentY": 0.130434781, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.573913038, "sizePercentY": 0.3043478, "sizeType": 0, "tag": 727816505, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 66, "x": 2, "y": 15, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi.ttf", "fontSize": 30, "hAlignment": 0, "text": "Back", "touchScaleEnable": false, "vAlignment": 0}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "back", "ZOrder": 4, "actiontag": 16051739, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 115, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.0121093746, "positionPercentY": -0.215277776, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.044921875, "sizePercentY": 0.07986111, "sizeType": 0, "tag": 727816503, "touchAble": true, "useMergedTexture": false, "visible": false, "width": 115, "x": 31, "y": -310, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "Panel", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "bg", "ZOrder": 0, "actiontag": 45508046, "anchorPointX": 0.5, "anchorPointY": 1, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 252, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 23500, "positionPercentY": 3600, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 56000, "sizePercentY": 25200, "sizeType": 0, "tag": 727818954, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 560, "x": 235, "y": 36, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 60, "capInsetsY": 60, "fileName": null, "fileNameData": {"path": "3q/common/panel_tips_1.png", "plistFile": "", "resourceType": 0}, "scale9Enable": true, "scale9Height": 252, "scale9Width": 560}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "dailyGateTipsPos", "ZOrder": 3, "actiontag": 57018048, "anchorPointX": 0, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 0.01, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.266015619, "positionPercentY": 0.376388878, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 3.90624973e-06, "sizePercentY": 6.944444e-06, "sizeType": 0, "tag": 727818961, "touchAble": true, "useMergedTexture": false, "visible": false, "width": 0.01, "x": 681, "y": 542, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "bottomMask", "ZOrder": 10, "actiontag": 27278612, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 162, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": -0.406944454, "positionType": 0, "rotation": 0, "scaleX": 2, "scaleY": 2, "sizePercentX": 0.609375, "sizePercentY": 0.1125, "sizeType": 0, "tag": 727819727, "touchAble": true, "useMergedTexture": false, "visible": false, "width": 1560, "x": 0, "y": -586, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "city/adventure/clone_battle/bg_yuansu_biandui.jpg", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 162, "scale9Width": 1560}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "btnGHimg", "ZOrder": 0, "actiontag": 52220351, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 100, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.380078137, "positionPercentY": 0.22986111, "positionType": 0, "rotation": 0, "scaleX": 2, "scaleY": 2, "sizePercentX": 0.0390625, "sizePercentY": 0.06944445, "sizeType": 0, "tag": 727819797, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 100, "x": 973, "y": 331, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "config/embattle/icon_gh.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 100, "scale9Width": 100}}, {"classname": "Panel", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "bg", "ZOrder": 0, "actiontag": 40821806, "anchorPointX": 0.5, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 250, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 1, "sizePercentY": 1, "sizeType": 0, "tag": 727831722, "touchAble": false, "useMergedTexture": false, "visible": false, "width": 2560, "x": 0, "y": 0, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 330, "capInsetsY": 1, "fileName": null, "fileNameData": {"path": "city/embattle/bg_zhuxiandi.png", "plistFile": "", "resourceType": 0}, "scale9Enable": true, "scale9Height": 250, "scale9Width": 2560}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "bottomPanel", "ZOrder": 4, "actiontag": 66001837, "anchorPointX": 0.5, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 250, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.00234375, "positionPercentY": -0.5, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 1, "sizePercentY": 0.1736111, "sizeType": 0, "tag": 727822220, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 2560, "x": 6, "y": -720, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "Panel", "name": null, "children": [{"classname": "<PERSON><PERSON>", "name": null, "children": [{"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textNote", "ZOrder": 1, "actiontag": 48877908, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 186, "colorG": 226, "colorR": 237, "customProperty": "", "flipX": false, "flipY": false, "height": 67, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.9897436, "sizePercentY": 0.31904763, "sizeType": 0, "tag": 727822321, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 193, "x": 0, "y": 0, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 60, "hAlignment": 1, "text": "<PERSON><PERSON>", "touchScaleEnable": false, "vAlignment": 1}}], "options": {"__type": "ButtonSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "<PERSON><PERSON>", "name": "btnOneKeySet", "ZOrder": 12, "actiontag": 57626661, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSButton", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 210, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.00917431153, "positionPercentY": -0.16, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.5963303, "sizePercentY": 1.05, "sizeType": 0, "tag": 727822320, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 195, "x": -3, "y": -32, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 106, "capInsetsY": 61, "disabled": null, "disabledData": {"path": null, "plistFile": null, "resourceType": 0}, "fontName": "font/youmi.ttf", "fontSize": 14, "fontType": 0, "normal": null, "normalData": {"path": "city/embattle/btn.png", "plistFile": "", "resourceType": 0}, "pressed": null, "pressedData": {"path": null, "plistFile": null, "resourceType": 0}, "scale9Enable": false, "scale9Height": 210, "scale9Width": 195, "text": "", "textColorB": 255, "textColorG": 255, "textColorR": 255}}, {"classname": "<PERSON><PERSON>", "name": null, "children": [{"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textNote", "ZOrder": 1, "actiontag": 42548197, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 36, "colorG": 88, "colorR": 130, "customProperty": "", "flipX": false, "flipY": false, "height": 57, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.42, "sizePercentY": 0.467213124, "sizeType": 0, "tag": 727830264, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 84, "x": 0, "y": 0, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 50, "hAlignment": 0, "text": "<PERSON><PERSON><PERSON>", "touchScaleEnable": false, "vAlignment": 0}}], "options": {"__type": "ButtonSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "<PERSON><PERSON>", "name": "btnSaveReady", "ZOrder": 13, "actiontag": 12184117, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSButton", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 122, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": -0.935, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.6116208, "sizePercentY": 0.61, "sizeType": 0, "tag": 727830263, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 200, "x": 0, "y": -187, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 106, "capInsetsY": 61, "disabled": null, "disabledData": {"path": null, "plistFile": null, "resourceType": 0}, "fontName": "font/youmi.ttf", "fontSize": 14, "fontType": 0, "normal": null, "normalData": {"path": "common/btn/btn_normal.png", "plistFile": "", "resourceType": 0}, "pressed": null, "pressedData": {"path": null, "plistFile": null, "resourceType": 0}, "scale9Enable": true, "scale9Height": 122, "scale9Width": 200, "text": "", "textColorB": 255, "textColorG": 255, "textColorR": 255}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "rightDown", "ZOrder": 12, "actiontag": 63283514, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 200, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.429296881, "positionPercentY": -0.129166663, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.127734378, "sizePercentY": 0.1388889, "sizeType": 0, "tag": 727822314, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 327, "x": 1099, "y": -186, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "<PERSON><PERSON>", "name": null, "children": [{"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textNote", "ZOrder": 1, "actiontag": 49290721, "anchorPointX": 0, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 196, "colorG": 241, "colorR": 252, "customProperty": "", "flipX": false, "flipY": false, "height": 47, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.288461536, "positionPercentY": -0.445945948, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 1.04487181, "sizePercentY": 0.317567557, "sizeType": 0, "tag": 727830258, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 163, "x": -45, "y": -66, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 42, "hAlignment": 0, "text": "<PERSON><PERSON><PERSON>", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "img", "ZOrder": 0, "actiontag": 36182015, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 74, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1.5, "scaleY": 1.5, "sizePercentX": 0.5, "sizePercentY": 0.5, "sizeType": 0, "tag": 727830259, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 78, "x": 0, "y": 0, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/icon_xuanzhen.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 74, "scale9Width": 78}}], "options": {"__type": "ButtonSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "<PERSON><PERSON>", "name": "btnReady", "ZOrder": 1, "actiontag": 24935918, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSButton", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 148, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.4207031, "positionPercentY": 0.350694448, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.0609375, "sizePercentY": 0.102777779, "sizeType": 0, "tag": 727830257, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 156, "x": -1077, "y": 505, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 106, "capInsetsY": 61, "disabled": null, "disabledData": {"path": null, "plistFile": null, "resourceType": 0}, "fontName": "font/youmi.ttf", "fontSize": 14, "fontType": 0, "normal": null, "normalData": {"path": "3q/common/bg_gongneng_01.png", "plistFile": "", "resourceType": 0}, "pressed": null, "pressedData": {"path": null, "plistFile": null, "resourceType": 0}, "scale9Enable": false, "scale9Height": 148, "scale9Width": 156, "text": "", "textColorB": 255, "textColorG": 255, "textColorR": 255}}, {"classname": "Panel", "name": null, "children": [], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "Chu<PERSON>uong", "ZOrder": 0, "actiontag": 47814869, "anchorPointX": 0.5, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 200, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.304296881, "positionPercentY": -0.04027778, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.078125, "sizePercentY": 0.1388889, "sizeType": 0, "tag": 727831972, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 200, "x": -779, "y": -58, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "Panel_82", "ZOrder": 0, "actiontag": -1, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 1440, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 1, "sizePercentY": 1, "sizeType": 0, "tag": 727806582, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 2560, "x": 1280, "y": 720, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 0, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 1, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}}