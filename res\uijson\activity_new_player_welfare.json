{"classname": null, "name": null, "animation": {"classname": null, "name": "AnimationManager", "actionlist": []}, "dataScale": 1, "designHeight": 1440, "designWidth": 2560, "textures": [], "texturesPng": ["3q/new_player_welfare/bg.png", "3q/new_player_welfare/btn_guanbi.png", "3q/new_player_welfare/itembg.png", "3q/new_player_welfare/mask.png", "3q/new_player_welfare/tick.png", "GUI/image.png", "3q/common/btn_big_on.png", "3q/common/btn_big_sp.png", "3q/common/btn_big_sp02.png", "3q/stat/icon_life.png", "3q/rarity/icon_rarity4.png", "3q/new_player_welfare/bgtit.png", "3q/new_player_welfare/wave.png", "3q/new_player_welfare/bird.png", "3q/new_player_welfare/3days.png", "3q/new_player_welfare/title.png", "3q/new_player_welfare/dragonunder.png", "3q/new_player_welfare/dragonabove.png"], "version": "*******", "widgetTree": {"classname": "Panel", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "bg", "ZOrder": 0, "actiontag": 34791427, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 915, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.7222656, "sizePercentY": 0.6354167, "sizeType": 0, "tag": 727812626, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 1849, "x": 0, "y": 0, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/new_player_welfare/bg.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 915, "scale9Width": 1849}}, {"classname": "<PERSON><PERSON>", "name": null, "children": [], "options": {"__type": "ButtonSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "<PERSON><PERSON>", "name": "btnClose", "ZOrder": 3, "actiontag": 2884644, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSButton", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 64, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.336328119, "positionPercentY": 0.260416657, "positionType": 0, "rotation": 0, "scaleX": 2, "scaleY": 2, "sizePercentX": 0.025, "sizePercentY": 0.0444444455, "sizeType": 0, "tag": 727812637, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 64, "x": 861, "y": 375, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "disabled": null, "disabledData": {"path": "3q/new_player_welfare/btn_guanbi.png", "plistFile": "", "resourceType": 0}, "fontName": "font/youmi1.ttf", "fontSize": 14, "fontType": 0, "normal": null, "normalData": {"path": "3q/new_player_welfare/btn_guanbi.png", "plistFile": "", "resourceType": 0}, "pressed": null, "pressedData": {"path": "3q/new_player_welfare/btn_guanbi.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 64, "scale9Width": 64, "text": "", "textColorB": 255, "textColorG": 255, "textColorR": 255}}, {"classname": "ListView", "name": null, "children": [], "options": {"__type": "ListViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ListView", "name": "list", "ZOrder": 4, "actiontag": 51833409, "anchorPointX": 0, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSListView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 800, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.141015619, "positionPercentY": -0.319444448, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.56640625, "sizePercentY": 0.5555556, "sizeType": 0, "tag": 727812634, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 1450, "x": -361, "y": -460, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 150, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 150, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "bounceEnable": false, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": true, "colorType": 0, "direction": 2, "editorClipAble": false, "gravity": 5, "innerHeight": 0, "innerWidth": 0, "itemMargin": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "Panel", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "imgBg", "ZOrder": 0, "actiontag": 22954200, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 645, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.5, "positionPercentY": 0.5, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.95625, "sizePercentY": 0.992307663, "sizeType": 0, "tag": 727812636, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 153, "x": 80, "y": 325, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/new_player_welfare/itembg.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 645, "scale9Width": 153}}, {"classname": "ImageView", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "Image_45", "ZOrder": 0, "actiontag": 4154054, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 81, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.5882353, "sizePercentY": 0.1255814, "sizeType": 0, "tag": 727812745, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 90, "x": 0, "y": 0, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/new_player_welfare/tick.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 81, "scale9Width": 90}}], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "imgGift", "ZOrder": 2, "actiontag": 36982251, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 645, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.5, "positionPercentY": 0.5, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.95625, "sizePercentY": 0.992307663, "sizeType": 0, "tag": 727812638, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 153, "x": 80, "y": 325, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/new_player_welfare/mask.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 645, "scale9Width": 153}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textGotten", "ZOrder": 1, "actiontag": 50580584, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 187, "colorG": 254, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 50, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.49375, "positionPercentY": 0.0553846136, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.45, "sizePercentY": 0.07692308, "sizeType": 0, "tag": 727812640, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 72, "x": 79, "y": 36, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 44, "hAlignment": 0, "text": "已领取", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textTian", "ZOrder": 1, "actiontag": 36973338, "anchorPointX": 0, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 40, "colorG": 88, "colorR": 129, "customProperty": "", "flipX": false, "flipY": false, "height": 57, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.86875, "positionPercentY": 0.853846133, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.175, "sizePercentY": 0.0876923054, "sizeType": 0, "tag": 727812642, "touchAble": false, "useMergedTexture": false, "visible": false, "width": 28, "x": 139, "y": 555, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 50, "hAlignment": 0, "text": "天", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textDi", "ZOrder": 1, "actiontag": 57124168, "anchorPointX": 1, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 40, "colorG": 88, "colorR": 129, "customProperty": "", "flipX": false, "flipY": false, "height": 57, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.125, "positionPercentY": 0.853846133, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.175, "sizePercentY": 0.0876923054, "sizeType": 0, "tag": 727812644, "touchAble": false, "useMergedTexture": false, "visible": false, "width": 28, "x": 20, "y": 555, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 50, "hAlignment": 0, "text": "第", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "imgDayBg", "ZOrder": 1, "actiontag": 31904287, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 32, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.49375, "positionPercentY": 0.8630769, "positionType": 0, "rotation": 0, "scaleX": 1.2, "scaleY": 1.2, "sizePercentX": 0.5, "sizePercentY": 0.123076923, "sizeType": 0, "tag": 727812646, "touchAble": false, "useMergedTexture": false, "visible": false, "width": 64, "x": 79, "y": 561, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "GUI/image.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 32, "scale9Width": 64}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textDay", "ZOrder": 2, "actiontag": 65328429, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 89, "ignoreSize": true, "layoutParameter": null, "opacity": 0, "positionPercentX": 0.50625, "positionPercentY": 0.9369231, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.275, "sizePercentY": 0.136923075, "sizeType": 0, "tag": 727812648, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 44, "x": 81, "y": 609, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 80, "hAlignment": 0, "text": "1", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "<PERSON><PERSON>", "name": null, "children": [{"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textGet", "ZOrder": 0, "actiontag": 50540081, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 5, "colorG": 38, "colorR": 83, "customProperty": "", "flipX": false, "flipY": false, "height": 45, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0.05882353, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.3846154, "sizePercentY": 0.882352948, "sizeType": 0, "tag": 727812660, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 55, "x": 0, "y": 3, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 40, "hAlignment": 0, "text": "领 取", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "imgGift", "ZOrder": 0, "actiontag": 11637782, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 32, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.6083916, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 0.5, "scaleY": 0.5, "sizePercentX": 0.559440553, "sizePercentY": 1.56862748, "sizeType": 0, "tag": 727812661, "touchAble": false, "useMergedTexture": false, "visible": false, "width": 64, "x": -87, "y": 0, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "GUI/image.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 32, "scale9Width": 64}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textNotGet", "ZOrder": 0, "actiontag": 63221540, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 44, "colorG": 44, "colorR": 44, "customProperty": "", "flipX": false, "flipY": false, "height": 39, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0.05882353, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.3986014, "sizePercentY": 0.7647059, "sizeType": 0, "tag": 727812666, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 57, "x": 0, "y": 3, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 34, "hAlignment": 0, "text": "未达成", "touchScaleEnable": false, "vAlignment": 0}}], "options": {"__type": "ButtonSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "<PERSON><PERSON>", "name": "btnGet", "ZOrder": 1, "actiontag": 8293825, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSButton", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 51, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.5, "positionPercentY": 0.0538461544, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.89375, "sizePercentY": 0.0784615353, "sizeType": 0, "tag": 727812658, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 143, "x": 80, "y": 35, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "disabled": null, "disabledData": {"path": "3q/common/btn_big_sp02.png", "plistFile": "", "resourceType": 0}, "fontName": "font/youmi1.ttf", "fontSize": 14, "fontType": 0, "normal": null, "normalData": {"path": "3q/common/btn_big_on.png", "plistFile": "", "resourceType": 0}, "pressed": null, "pressedData": {"path": "3q/common/btn_big_sp.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 51, "scale9Width": 143, "text": "", "textColorB": 255, "textColorG": 255, "textColorR": 255}}, {"classname": "ListView", "name": null, "children": [], "options": {"__type": "ListViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ListView", "name": "list", "ZOrder": 1, "actiontag": 40259238, "anchorPointX": 0, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSListView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 650, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.11875, "positionPercentY": 0.1, "positionType": 0, "rotation": 0, "scaleX": 0.8, "scaleY": 0.8, "sizePercentX": 0.95625, "sizePercentY": 1, "sizeType": 0, "tag": 727812746, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 153, "x": 19, "y": 65, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 150, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 150, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "bounceEnable": false, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": true, "colorType": 0, "direction": 1, "editorClipAble": false, "gravity": 2, "innerHeight": 0, "innerWidth": 0, "itemMargin": 0, "vectorX": 0, "vectorY": -0.5}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "item", "ZOrder": 0, "actiontag": 3282622, "anchorPointX": 0, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 650, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.11875, "positionPercentY": 0.65069443, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.0625, "sizePercentY": 0.4513889, "sizeType": 0, "tag": 727812635, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 160, "x": -304, "y": 937, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 0, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "ImageView", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "imgSptireName", "ZOrder": 0, "actiontag": 9431711, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 32, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 3.375, "positionPercentY": -1.5625, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 1.66666663, "sizePercentY": 1.66666663, "sizeType": 0, "tag": 727812652, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 64, "x": 162, "y": -75, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "GUI/image.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 32, "scale9Width": 64}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "imgTotalDay", "ZOrder": 0, "actiontag": 32935530, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 32, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.4375, "positionPercentY": 2.29166675, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 1.66666663, "sizePercentY": 1.66666663, "sizeType": 0, "tag": 727812653, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 64, "x": 21, "y": 110, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "GUI/image.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 32, "scale9Width": 64}}], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "ImageActivity", "ZOrder": 3, "actiontag": 4432377, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 48, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.278906256, "positionPercentY": -0.242361113, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.01875, "sizePercentY": 0.0333333351, "sizeType": 0, "tag": 727812651, "touchAble": false, "useMergedTexture": false, "visible": false, "width": 48, "x": -714, "y": -349, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/stat/icon_life.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 48, "scale9Width": 48}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "imgRarity", "ZOrder": 4, "actiontag": 9926229, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 79, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.321875, "positionPercentY": 0.286111116, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.0375, "sizePercentY": 0.06944445, "sizeType": 0, "tag": 727812654, "touchAble": false, "useMergedTexture": false, "visible": false, "width": 87, "x": -824, "y": 412, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/rarity/icon_rarity4.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 79, "scale9Width": 87}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "imgAttr1", "ZOrder": 4, "actiontag": 4915022, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 32, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.321875, "positionPercentY": 0.235416666, "positionType": 0, "rotation": 0, "scaleX": 0.5, "scaleY": 0.5, "sizePercentX": 0.03125, "sizePercentY": 0.055555556, "sizeType": 0, "tag": 727812655, "touchAble": false, "useMergedTexture": false, "visible": false, "width": 64, "x": -824, "y": 339, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "GUI/image.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 32, "scale9Width": 64}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "imgAttr2", "ZOrder": 4, "actiontag": 45446256, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 32, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.321875, "positionPercentY": 0.19861111, "positionType": 0, "rotation": 0, "scaleX": 0.5, "scaleY": 0.5, "sizePercentX": 0.03125, "sizePercentY": 0.055555556, "sizeType": 0, "tag": 727812656, "touchAble": false, "useMergedTexture": false, "visible": false, "width": 64, "x": -824, "y": 286, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "GUI/image.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 32, "scale9Width": 64}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "Image_41", "ZOrder": 3, "actiontag": 44696655, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 185, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.321875, "positionPercentY": 0.221527785, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.0300781243, "sizePercentY": 0.128472224, "sizeType": 0, "tag": 727812741, "touchAble": false, "useMergedTexture": false, "visible": false, "width": 77, "x": -824, "y": 319, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/new_player_welfare/bgtit.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 185, "scale9Width": 77}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textToday", "ZOrder": 3, "actiontag": 59365165, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 138, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 45, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.180078119, "positionPercentY": -0.320833325, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.034375, "sizePercentY": 0.03125, "sizeType": 0, "tag": 727812662, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 88, "x": 461, "y": -462, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 40, "hAlignment": 0, "text": "当前登录", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textTodayNum", "ZOrder": 2, "actiontag": 61307380, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 119, "colorG": 255, "colorR": 141, "customProperty": "", "flipX": false, "flipY": false, "height": 60, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.2296875, "positionPercentY": -0.3201389, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.0113281254, "sizePercentY": 0.0416666679, "sizeType": 0, "tag": 727812664, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 29, "x": 588, "y": -461, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 52, "hAlignment": 0, "text": "0", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "Label", "name": null, "children": [], "options": {"__type": "LabelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Label", "name": "textToday1", "ZOrder": 3, "actiontag": 4496426, "anchorPointX": 1, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSLabel", "colorB": 138, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 45, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.261328131, "positionPercentY": -0.320833325, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.00859375, "sizePercentY": 0.03125, "sizeType": 0, "tag": 727812724, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 22, "x": 669, "y": -462, "areaHeight": 0, "areaWidth": 0, "fontFile": null, "fontName": "font/youmi1.ttf", "fontSize": 40, "hAlignment": 0, "text": "天", "touchScaleEnable": false, "vAlignment": 0}}, {"classname": "ListView", "name": null, "children": [{"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "imgSpriteShow", "ZOrder": 0, "actiontag": 67009241, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 48, "ignoreSize": true, "layoutParameter": {"classname": null, "name": null, "align": 0, "gravity": 2, "layoutEageType": 0, "layoutNormalHorizontal": 0, "layoutNormalVertical": 0, "layoutParentHorizontal": 0, "layoutParentVertical": 0, "marginDown": 0, "marginLeft": 212, "marginRight": 0, "marginTop": -303, "relativeName": "imgSpriteShow", "relativeToName": "<PERSON><PERSON><PERSON><PERSON>", "type": 1}, "opacity": 255, "positionPercentX": 0.016, "positionPercentY": 0.976, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.032, "sizePercentY": 0.048, "sizeType": 0, "tag": 727812681, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 48, "x": 24, "y": 976, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/stat/icon_life.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 48, "scale9Width": 48}}], "options": {"__type": "ListViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ListView", "name": "<PERSON><PERSON><PERSON><PERSON>", "ZOrder": 1, "actiontag": 19562533, "anchorPointX": 0, "anchorPointY": 0, "classType": "CocoStudio.EngineAdapterWrap.CSListView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 1000, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.4984375, "positionPercentY": -0.288194448, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.5859375, "sizePercentY": 0.6944444, "sizeType": 0, "tag": 727812727, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 1500, "x": -1276, "y": -415, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 150, "bgColorOpacity": 100, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 150, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "bounceEnable": false, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": true, "colorType": 0, "direction": 2, "editorClipAble": false, "gravity": 3, "innerHeight": 0, "innerWidth": 0, "itemMargin": 0, "vectorX": 0, "vectorY": -0.5}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "Image_32", "ZOrder": 2, "actiontag": 36022006, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 377, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.08554687, "positionPercentY": -0.197916672, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.516406238, "sizePercentY": 0.261805564, "sizeType": 0, "tag": 727812728, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 1322, "x": -219, "y": -285, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/new_player_welfare/wave.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 377, "scale9Width": 1322}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "Image_38", "ZOrder": 6, "actiontag": 49202992, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 1038, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.7410156, "sizePercentY": 0.720833361, "sizeType": 0, "tag": 727812734, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 1897, "x": 0, "y": 0, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/new_player_welfare/bird.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 1038, "scale9Width": 1897}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "Image_36", "ZOrder": 2, "actiontag": 23079928, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 171, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.235156253, "positionPercentY": -0.166666672, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.1671875, "sizePercentY": 0.11875, "sizeType": 0, "tag": 727812735, "touchAble": false, "useMergedTexture": false, "visible": false, "width": 428, "x": -602, "y": -240, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/new_player_welfare/3days.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 171, "scale9Width": 428}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "Image_40", "ZOrder": 2, "actiontag": 33825759, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 191, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": 0.112109378, "positionPercentY": 0.250694454, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.4546875, "sizePercentY": 0.132638887, "sizeType": 0, "tag": 727812740, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 1164, "x": 287, "y": 361, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/new_player_welfare/title.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 191, "scale9Width": 1164}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "Image_42", "ZOrder": 0, "actiontag": 59874743, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 1072, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.232031256, "positionPercentY": -0.0069444445, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.286328137, "sizePercentY": 0.74444443, "sizeType": 0, "tag": 727812743, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 733, "x": -594, "y": -10, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/new_player_welfare/dragonunder.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 1072, "scale9Width": 733}}, {"classname": "ImageView", "name": null, "children": [], "options": {"__type": "ImageViewSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "ImageView", "name": "Image_44", "ZOrder": 2, "actiontag": 55189750, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSImageView", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 1068, "ignoreSize": true, "layoutParameter": null, "opacity": 255, "positionPercentX": -0.244921878, "positionPercentY": -0.007638889, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 0.2609375, "sizePercentY": 0.7416667, "sizeType": 0, "tag": 727812744, "touchAble": false, "useMergedTexture": false, "visible": true, "width": 668, "x": -627, "y": -11, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "fileName": null, "fileNameData": {"path": "3q/new_player_welfare/dragonabove.png", "plistFile": "", "resourceType": 0}, "scale9Enable": false, "scale9Height": 1068, "scale9Width": 668}}], "options": {"__type": "PanelSurrogate:#EditorCommon.JsonModel.Component.GUI", "classname": "Panel", "name": "Panel", "ZOrder": 0, "actiontag": -1, "anchorPointX": 0.5, "anchorPointY": 0.5, "classType": "CocoStudio.EngineAdapterWrap.CSPanel", "colorB": 255, "colorG": 255, "colorR": 255, "customProperty": "", "flipX": false, "flipY": false, "height": 1440, "ignoreSize": false, "layoutParameter": null, "opacity": 255, "positionPercentX": 0, "positionPercentY": 0, "positionType": 0, "rotation": 0, "scaleX": 1, "scaleY": 1, "sizePercentX": 1, "sizePercentY": 1, "sizeType": 0, "tag": 727812589, "touchAble": true, "useMergedTexture": false, "visible": true, "width": 2560, "x": 1280, "y": 720, "adaptScreen": false, "backGroundImage": null, "backGroundImageData": null, "backGroundScale9Enable": false, "bgColorB": 255, "bgColorG": 200, "bgColorOpacity": 0, "bgColorR": 150, "bgEndColorB": 255, "bgEndColorG": 200, "bgEndColorR": 150, "bgStartColorB": 255, "bgStartColorG": 255, "bgStartColorR": 255, "capInsetsHeight": 1, "capInsetsWidth": 1, "capInsetsX": 0, "capInsetsY": 0, "clipAble": false, "colorType": 1, "layoutType": 0, "vectorX": 0, "vectorY": -0.5}}}