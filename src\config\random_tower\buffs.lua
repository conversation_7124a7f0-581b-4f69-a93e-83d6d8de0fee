
csv['random_tower']['buffs'] = {
	[1] = {
		id = 1,
		desc = 'Vật Công của toàn đội +10%',
		desc_vn = 'Vật Công của toàn đội +10%',
		desc_en = 'Team Physical Attack +10%',
		desc_th = 'พลังโจมตีของทีมทั้งหมด +10%',
		desc_idn = 'Serangan Fisik seluruh tim +10%',
		desc_cn = '全队物理攻击 +10%',
		desc_kr = '전대의 물리 공격 +10%',
		weight = 100,
		icon = 'city/adventure/random_tower/icon/icon_dk.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect',
		attrType1 = 7,
		attrNum1 = '10%'
	},
	[2] = {
		id = 2,
		group = 2,
		desc = 'Vật Công của toàn đội +15%',
		desc_vn = 'Vật Công của toàn đội +15%',
		desc_en = 'Team Physical Attack +15%',
		desc_th = 'พลังโจมตีของทีมทั้งหมด +15%',
		desc_idn = 'Serangan Fisik seluruh tim +15%',
		desc_cn = '全队物理攻击 +15%',
		desc_kr = '전대의 물리 공격 +15%',
		weight = 100,
		icon = 'city/adventure/random_tower/icon/icon_dk.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect',
		attrType1 = 7,
		attrNum1 = '15%',
		limit = 3
	},
	[3] = {
		id = 3,
		group = 3,
		desc = 'Vật Công của toàn đội +20%',
		desc_vn = 'Vật Công của toàn đội +20%',
		desc_en = 'Team Physical Attack +20%',
		desc_th = 'พลังโจมตีของทีมทั้งหมด +20%',
		desc_idn = 'Serangan Fisik seluruh tim +20%',
		desc_cn = '全队物理攻击 +20%',
		desc_kr = '전대의 물리 공격 +20%',
		weight = 100,
		icon = 'city/adventure/random_tower/icon/icon_dk.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect',
		attrType1 = 7,
		attrNum1 = '20%',
		onlyOne = true,
		limit = 1
	},
	[4] = {
		id = 4,
		desc = 'Trí Lực của toàn đội +10%',
		desc_vn = 'Trí Lực của toàn đội +10%',
		desc_en = 'Team Intelligence +10%',
		desc_th = 'ปัญญาของทีมทั้งหมด +10%',
		desc_idn = 'Kecerdasan seluruh tim +10%',
		desc_cn = '全队智力 +10%',
		desc_kr = '전대의 지력 +10%',
		weight = 100,
		icon = 'city/adventure/random_tower/icon/icon_zl.png',
		iconBg = 'city/adventure/random_tower/icon/box_blue.png',
		buffColor = 'effect',
		attrType1 = 8,
		attrNum1 = '10%'
	},
	[5] = {
		id = 5,
		group = 2,
		desc = 'Trí Lực của toàn đội +15%',
		desc_vn = 'Trí Lực của toàn đội +15%',
		desc_en = 'Team Intelligence +15%',
		desc_th = 'ปัญญาของทีมทั้งหมด +15%',
		desc_idn = 'Kecerdasan seluruh tim +15%',
		desc_cn = '全队智力 +15%',
		desc_kr = '전대의 지력 +15%',
		weight = 100,
		icon = 'city/adventure/random_tower/icon/icon_zl.png',
		iconBg = 'city/adventure/random_tower/icon/box_blue.png',
		buffColor = 'effect',
		attrType1 = 8,
		attrNum1 = '15%',
		limit = 3
	},
	[6] = {
		id = 6,
		group = 3,
		desc = 'Trí Lực của toàn đội +20%',
		desc_vn = 'Trí Lực của toàn đội +20%',
		desc_en = 'Team Intelligence +20%',
		desc_th = 'ปัญญาของทีมทั้งหมด +20%',
		desc_idn = 'Kecerdasan seluruh tim +20%',
		desc_cn = '全队智力 +20%',
		desc_kr = '전대의 지력 +20%',
		weight = 100,
		icon = 'city/adventure/random_tower/icon/icon_zl.png',
		iconBg = 'city/adventure/random_tower/icon/box_blue.png',
		buffColor = 'effect',
		attrType1 = 8,
		attrNum1 = '20%',
		onlyOne = true,
		limit = 1
	},
	[7] = {
		id = 7,
		desc = 'Vật Công và Trí Lực toàn đội +5%',
		desc_vn = 'Vật Công và Trí Lực toàn đội +5%',
		desc_en = 'Team Physical Attack and Intelligence +5%',
		desc_th = 'พลังโจมตีและปัญญาของทีมทั้งหมด +5%',
		desc_idn = 'Serangan Fisik dan Kecerdasan seluruh tim +5%',
		desc_cn = '全队物理攻击和智力 +5%',
		desc_kr = '전대의 물리 공격과 지력 +5%',
		weight = 150,
		icon = 'city/adventure/random_tower/icon/icon_gj.png',
		iconBg = 'city/adventure/random_tower/icon/box_red.png',
		buffColor = 'effect',
		attrType1 = 8,
		attrNum1 = '5%',
		attrType2 = 7,
		attrNum2 = '5%'
	},
	[8] = {
		id = 8,
		group = 2,
		desc = 'Vật Công và Trí Lực toàn đội +10%',
		desc_vn = 'Vật Công và Trí Lực toàn đội +10%',
		desc_en = 'Team Physical Attack and Intelligence +10%',
		desc_th = 'พลังโจมตีและปัญญาของทีมทั้งหมด +10%',
		desc_idn = 'Serangan Fisik dan Kecerdasan seluruh tim +10%',
		desc_cn = '全队物理攻击和智力 +10%',
		desc_kr = '전대의 물리 공격과 지력 +10%',
		weight = 150,
		icon = 'city/adventure/random_tower/icon/icon_gj.png',
		iconBg = 'city/adventure/random_tower/icon/box_red.png',
		buffColor = 'effect',
		attrType1 = 8,
		attrNum1 = '10%',
		attrType2 = 7,
		attrNum2 = '10%',
		limit = 3
	},
	[9] = {
		id = 9,
		group = 3,
		desc = 'Vật Công và Trí Lực toàn đội +15%',
		desc_vn = 'Vật Công và Trí Lực toàn đội +15%',
		desc_en = 'Team Physical Attack and Intelligence +15%',
		desc_th = 'พลังโจมตีและปัญญาของทีมทั้งหมด +15%',
		desc_idn = 'Serangan Fisik dan Kecerdasan seluruh tim +15%',
		desc_cn = '全队物理攻击和智力 +15%',
		desc_kr = '전대의 물리 공격과 지력 +15%',
		weight = 150,
		icon = 'city/adventure/random_tower/icon/icon_gj.png',
		iconBg = 'city/adventure/random_tower/icon/box_red.png',
		buffColor = 'effect',
		attrType1 = 8,
		attrNum1 = '15%',
		attrType2 = 7,
		attrNum2 = '15%',
		limit = 2
	},
	[10] = {
		id = 10,
		desc = 'Sát thương gây ra của toàn đội +5%',
		desc_vn = 'Sát thương gây ra của toàn đội +5%',
		desc_en = 'Team Damage Dealt +5%',
		desc_th = 'ความเสียหายที่ทีมทั้งหมดสร้าง +5%',
		desc_idn = 'Kerusakan yang diberikan seluruh tim +5%',
		desc_cn = '全队造成的伤害 +5%',
		desc_kr = '전대가 입히는 피해 +5%',
		weight = 100,
		icon = 'city/adventure/random_tower/icon/icon_sw.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect',
		attrType1 = 22,
		attrNum1 = '500'
	},
	[11] = {
		id = 11,
		group = 2,
		desc = 'Sát thương gây ra của toàn đội +10%',
		desc_vn = 'Sát thương gây ra của toàn đội +10%',
		desc_en = 'Team Damage Dealt +10%',
		desc_th = 'ความเสียหายที่ทีมทั้งหมดสร้าง +10%',
		desc_idn = 'Kerusakan yang diberikan seluruh tim +10%',
		desc_cn = '全队造成的伤害 +10%',
		desc_kr = '전대가 입히는 피해 +10%',
		weight = 120,
		icon = 'city/adventure/random_tower/icon/icon_sw.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect',
		attrType1 = 22,
		attrNum1 = '1000',
		limit = 3
	},
	[12] = {
		id = 12,
		group = 3,
		desc = 'Sát thương gây ra của toàn đội +15%',
		desc_vn = 'Sát thương gây ra của toàn đội +15%',
		desc_en = 'Team Damage Dealt +15%',
		desc_th = 'ความเสียหายที่ทีมทั้งหมดสร้าง +15%',
		desc_idn = 'Kerusakan yang diberikan seluruh tim +15%',
		desc_cn = '全队造成的伤害 +15%',
		desc_kr = '전대가 입히는 피해 +15%',
		weight = 150,
		icon = 'city/adventure/random_tower/icon/icon_sw.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect',
		attrType1 = 22,
		attrNum1 = '1500',
		onlyOne = true,
		limit = 1
	},
	[13] = {
		id = 13,
		desc = 'Tỷ lệ chí mạng của toàn đội +5%',
		desc_vn = 'Tỷ lệ chí mạng của toàn đội +5%',
		desc_en = 'Team Critical Rate +5%',
		desc_th = 'อัตราคริติคอลของทีมทั้งหมด +5%',
		desc_idn = 'Peluang kritikal seluruh tim +5%',
		desc_cn = '全队暴击率 +5%',
		desc_kr = '전대의 치명타 확률 +5%',
		weight = 100,
		icon = 'city/adventure/random_tower/icon/icon_mf.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect',
		attrType1 = 14,
		attrNum1 = '500'
	},
	[14] = {
		id = 14,
		group = 2,
		desc = 'Tỷ lệ chí mạng của toàn đội +10%',
		desc_vn = 'Tỷ lệ chí mạng của toàn đội +10%',
		desc_en = 'Team Critical Rate +10%',
		desc_th = 'อัตราคริติคอลของทีมทั้งหมด +10%',
		desc_idn = 'Peluang kritikal seluruh tim +10%',
		desc_cn = '全队暴击率 +10%',
		desc_kr = '전대의 치명타 확률 +10%',
		weight = 120,
		icon = 'city/adventure/random_tower/icon/icon_mf.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect',
		attrType1 = 14,
		attrNum1 = '1000',
		limit = 3
	},
	[15] = {
		id = 15,
		group = 3,
		desc = 'Tỷ lệ chí mạng của toàn đội +15%',
		desc_vn = 'Tỷ lệ chí mạng của toàn đội +15%',
		desc_en = 'Team Critical Rate +15%',
		desc_th = 'อัตราคริติคอลของทีมทั้งหมด +15%',
		desc_idn = 'Peluang kritikal seluruh tim +15%',
		desc_cn = '全队暴击率 +15%',
		desc_kr = '전대의 치명타 확률 +15%',
		weight = 150,
		icon = 'city/adventure/random_tower/icon/icon_mf.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect',
		attrType1 = 14,
		attrNum1 = '1500',
		onlyOne = true,
		limit = 1
	},
	[16] = {
		id = 16,
		desc = 'Sát thương chí mạng của toàn đội +10%',
		desc_vn = 'Sát thương chí mạng của toàn đội +10%',
		desc_en = 'Team Critical Damage +10%',
		desc_th = 'ความเสียหายคริติคอลของทีมทั้งหมด +10%',
		desc_idn = 'Kerusakan kritikal seluruh tim +10%',
		desc_cn = '全队暴击伤害 +10%',
		desc_kr = '전대의 치명타 피해 +10%',
		weight = 50,
		icon = 'city/adventure/random_tower/icon/icon_sh.png',
		iconBg = 'city/adventure/random_tower/icon/box_orange.png',
		buffColor = 'effect',
		attrType1 = 15,
		attrNum1 = '1000'
	},
	[17] = {
		id = 17,
		group = 2,
		desc = 'Sát thương chí mạng của toàn đội +15%',
		desc_vn = 'Sát thương chí mạng của toàn đội +15%',
		desc_en = 'Team Critical Damage +15%',
		desc_th = 'ความเสียหายคริติคอลของทีมทั้งหมด +15%',
		desc_idn = 'Kerusakan kritikal seluruh tim +15%',
		desc_cn = '全队暴击伤害 +15%',
		desc_kr = '전대의 치명타 피해 +15%',
		weight = 50,
		icon = 'city/adventure/random_tower/icon/icon_sh.png',
		iconBg = 'city/adventure/random_tower/icon/box_orange.png',
		buffColor = 'effect',
		attrType1 = 15,
		attrNum1 = '1500',
		limit = 3
	},
	[18] = {
		id = 18,
		group = 3,
		desc = 'Sát thương chí mạng của toàn đội +20%',
		desc_vn = 'Sát thương chí mạng của toàn đội +20%',
		desc_en = 'Team Critical Damage +20%',
		desc_th = 'ความเสียหายคริติคอลของทีมทั้งหมด +20%',
		desc_idn = 'Kerusakan kritikal seluruh tim +20%',
		desc_cn = '全队暴击伤害 +20%',
		desc_kr = '전대의 치명타 피해 +20%',
		weight = 50,
		icon = 'city/adventure/random_tower/icon/icon_sh.png',
		iconBg = 'city/adventure/random_tower/icon/box_orange.png',
		buffColor = 'effect',
		attrType1 = 15,
		attrNum1 = '2000',
		onlyOne = true,
		limit = 1
	},
	[19] = {
		id = 19,
		group = 1111,
		desc = 'Vật Phòng của toàn đội +15%',
		desc_vn = 'Vật Phòng của toàn đội +15%',
		desc_en = 'Team Physical Defense +15%',
		desc_th = 'พลังป้องกันกายภาพของทีมทั้งหมด +15%',
		desc_idn = 'Pertahanan Fisik seluruh tim +15%',
		desc_cn = '全队物理防御 +15%',
		desc_kr = '전대의 물리 방어 +15%',
		weight = 20,
		icon = 'city/adventure/random_tower/icon/icon_jh.png',
		iconBg = 'city/adventure/random_tower/icon/box_green.png',
		buffColor = 'effect_lan',
		attrType1 = 9,
		attrNum1 = '15%'
	},
	[20] = {
		id = 20,
		group = 1111,
		desc = 'Vật Phòng của toàn đội +20%',
		desc_vn = 'Vật Phòng của toàn đội +20%',
		desc_en = 'Team Physical Defense +20%',
		desc_th = 'พลังป้องกันกายภาพของทีมทั้งหมด +20%',
		desc_idn = 'Pertahanan Fisik seluruh tim +20%',
		desc_cn = '全队物理防御 +20%',
		desc_kr = '전대의 물리 방어 +20%',
		weight = 50,
		icon = 'city/adventure/random_tower/icon/icon_jh.png',
		iconBg = 'city/adventure/random_tower/icon/box_green.png',
		buffColor = 'effect_lan',
		attrType1 = 9,
		attrNum1 = '20%',
		limit = 3
	},
	[21] = {
		id = 21,
		group = 1111,
		desc = 'Vật Phòng của toàn đội +25%',
		desc_vn = 'Vật Phòng của toàn đội +25%',
		desc_en = 'Team Physical Defense +25%',
		desc_th = 'พลังป้องกันกายภาพของทีมทั้งหมด +25%',
		desc_idn = 'Pertahanan Fisik seluruh tim +25%',
		desc_cn = '全队物理防御 +25%',
		desc_kr = '전대의 물리 방어 +25%',
		weight = 10,
		icon = 'city/adventure/random_tower/icon/icon_jh.png',
		iconBg = 'city/adventure/random_tower/icon/box_green.png',
		buffColor = 'effect_lan',
		attrType1 = 9,
		attrNum1 = '25%',
		onlyOne = true,
		limit = 1
	},
	[22] = {
		id = 22,
		group = 1111,
		desc = 'Pháp Phòng của toàn đội +15%',
		desc_vn = 'Pháp Phòng của toàn đội +15%',
		desc_en = 'Team Magical Defense +15%',
		desc_th = 'พลังป้องกันเวทย์ของทีมทั้งหมด +15%',
		desc_idn = 'Pertahanan Magis seluruh tim +15%',
		desc_cn = '全队法术防御 +15%',
		desc_kr = '전대의 법술 방어 +15%',
		weight = 20,
		icon = 'city/adventure/random_tower/icon/icon_fs.png',
		iconBg = 'city/adventure/random_tower/icon/box_purple.png',
		buffColor = 'effect_lan',
		attrType1 = 10,
		attrNum1 = '15%'
	},
	[23] = {
		id = 23,
		group = 1111,
		desc = 'Pháp Phòng của toàn đội +20%',
		desc_vn = 'Pháp Phòng của toàn đội +20%',
		desc_en = 'Team Magical Defense +20%',
		desc_th = 'พลังป้องกันเวทย์ของทีมทั้งหมด +20%',
		desc_idn = 'Pertahanan Magis seluruh tim +20%',
		desc_cn = '全队法术防御 +20%',
		desc_kr = '전대의 법술 방어 +20%',
		weight = 50,
		icon = 'city/adventure/random_tower/icon/icon_fs.png',
		iconBg = 'city/adventure/random_tower/icon/box_purple.png',
		buffColor = 'effect_lan',
		attrType1 = 10,
		attrNum1 = '20%',
		limit = 3
	},
	[24] = {
		id = 24,
		group = 1111,
		desc = 'Pháp Phòng của toàn đội +25%',
		desc_vn = 'Pháp Phòng của toàn đội +25%',
		desc_en = 'Team Magical Defense +25%',
		desc_th = 'พลังป้องกันเวทย์ของทีมทั้งหมด +25%',
		desc_idn = 'Pertahanan Magis seluruh tim +25%',
		desc_cn = '全队法术防御 +25%',
		desc_kr = '전대의 법술 방어 +25%',
		weight = 10,
		icon = 'city/adventure/random_tower/icon/icon_fs.png',
		iconBg = 'city/adventure/random_tower/icon/box_purple.png',
		buffColor = 'effect_lan',
		attrType1 = 10,
		attrNum1 = '25%',
		onlyOne = true,
		limit = 1
	},
	[25] = {
		id = 25,
		desc = 'Vật Phòng và Pháp Phòng toàn đội +10%',
		desc_vn = 'Vật Phòng và Pháp Phòng toàn đội +10%',
		desc_en = 'Team Physical and Magical Defense +10%',
		desc_th = 'พลังป้องกันกายภาพและเวทย์ของทีมทั้งหมด +10%',
		desc_idn = 'Pertahanan Fisik dan Pertahanan Magis seluruh tim +10%',
		desc_cn = '全队物理防御和法术防御 +10%',
		desc_kr = '전대의 물리 방어와 법술 방어 +10%',
		weight = 200,
		icon = 'city/adventure/random_tower/icon/icon_fy.png',
		iconBg = 'city/adventure/random_tower/icon/box_blue.png',
		buffColor = 'effect_lan',
		attrType1 = 10,
		attrNum1 = '10%',
		attrType2 = 9,
		attrNum2 = '10%'
	},
	[26] = {
		id = 26,
		group = 2,
		desc = 'Vật Phòng và Pháp Phòng toàn đội +15%',
		desc_vn = 'Vật Phòng và Pháp Phòng toàn đội +15%',
		desc_en = 'Team Physical and Magical Defense +15%',
		desc_th = 'พลังป้องกันกายภาพและเวทย์ของทีมทั้งหมด +15%',
		desc_idn = 'Pertahanan Fisik dan Pertahanan Magis seluruh tim +15%',
		desc_cn = '全队物理防御和法术防御 +15%',
		desc_kr = '전대의 물리 방어와 법술 방어 +15%',
		weight = 200,
		icon = 'city/adventure/random_tower/icon/icon_fy.png',
		iconBg = 'city/adventure/random_tower/icon/box_blue.png',
		buffColor = 'effect_lan',
		attrType1 = 10,
		attrNum1 = '15%',
		attrType2 = 9,
		attrNum2 = '15%',
		limit = 3
	},
	[27] = {
		id = 27,
		group = 3,
		desc = 'Vật Phòng và Pháp Phòng toàn đội +20%',
		desc_vn = 'Vật Phòng và Pháp Phòng toàn đội +20%',
		desc_en = 'Team Physical and Magical Defense +20%',
		desc_th = 'พลังป้องกันกายภาพและเวทย์ของทีมทั้งหมด +20%',
		desc_idn = 'Pertahanan Fisik dan Pertahanan Magis seluruh tim +20%',
		desc_cn = '全队物理防御和法术防御 +20%',
		desc_kr = '전대의 물리 방어와 법술 방어 +20%',
		weight = 200,
		icon = 'city/adventure/random_tower/icon/icon_fy.png',
		iconBg = 'city/adventure/random_tower/icon/box_blue.png',
		buffColor = 'effect_lan',
		attrType1 = 10,
		attrNum1 = '20%',
		attrType2 = 9,
		attrNum2 = '20%',
		limit = 2
	},
	[28] = {
		id = 28,
		desc = 'Giảm sát thương nhận vào của toàn đội +5%',
		desc_vn = 'Giảm sát thương nhận vào của toàn đội +5%',
		desc_en = 'Team Damage Received Reduced by 5%',
		desc_th = 'ลดความเสียหายที่ทีมทั้งหมดได้รับ +5%',
		desc_idn = 'Mengurangi kerusakan yang diterima seluruh tim +5%',
		desc_cn = '全队受到伤害减少 +5%',
		desc_kr = '전대가 받는 피해 감소 +5%',
		weight = 100,
		icon = 'city/adventure/random_tower/icon/icon_ph.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect_lan',
		attrType1 = 23,
		attrNum1 = '500'
	},
	[29] = {
		id = 29,
		group = 2,
		desc = 'Giảm sát thương nhận vào của toàn đội +10%',
		desc_vn = 'Giảm sát thương nhận vào của toàn đội +10%',
		desc_en = 'Team Damage Received Reduced by 10%',
		desc_th = 'ลดความเสียหายที่ทีมทั้งหมดได้รับ +10%',
		desc_idn = 'Mengurangi kerusakan yang diterima seluruh tim +10%',
		desc_cn = '全队受到伤害减少 +10%',
		desc_kr = '전대가 받는 피해 감소 +10%',
		weight = 100,
		icon = 'city/adventure/random_tower/icon/icon_ph.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect_lan',
		attrType1 = 23,
		attrNum1 = '1000',
		limit = 3
	},
	[30] = {
		id = 30,
		group = 3,
		desc = 'Giảm sát thương nhận vào của toàn đội +15%',
		desc_vn = 'Giảm sát thương nhận vào của toàn đội +15%',
		desc_en = 'Team Damage Received Reduced by 15%',
		desc_th = 'ลดความเสียหายที่ทีมทั้งหมดได้รับ +15%',
		desc_idn = 'Mengurangi kerusakan yang diterima seluruh tim +15%',
		desc_cn = '全队受到伤害减少 +15%',
		desc_kr = '전대가 받는 피해 감소 +15%',
		weight = 50,
		icon = 'city/adventure/random_tower/icon/icon_ph.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect_lan',
		attrType1 = 23,
		attrNum1 = '1500',
		onlyOne = true,
		limit = 1
	},
	[31] = {
		id = 31,
		desc = 'Phòng ngự toàn đội +10%',
		desc_vn = 'Phòng ngự toàn đội +10%',
		desc_en = 'Team Defense +10%',
		desc_th = 'การป้องกันของทีมทั้งหมด +10%',
		desc_idn = 'Pertahanan seluruh tim +10%',
		desc_cn = '全队防御 +10%',
		desc_kr = '전대 방어 +10%',
		weight = 50,
		icon = 'city/adventure/random_tower/icon/icon_fy.png',
		iconBg = 'city/adventure/random_tower/icon/box_blue.png',
		buffColor = 'effect_lan',
		attrType1 = 17,
		attrNum1 = '1000'
	},
	[32] = {
		id = 32,
		group = 2,
		desc = 'Phòng ngự toàn đội +15%',
		desc_vn = 'Phòng ngự toàn đội +15%',
		desc_en = 'Team Defense +15%',
		desc_th = 'การป้องกันของทีมทั้งหมด +15%',
		desc_idn = 'Pertahanan seluruh tim +15%',
		desc_cn = '全队防御 +15%',
		desc_kr = '전대 방어 +15%',
		weight = 50,
		icon = 'city/adventure/random_tower/icon/icon_fy.png',
		iconBg = 'city/adventure/random_tower/icon/box_blue.png',
		buffColor = 'effect_lan',
		attrType1 = 17,
		attrNum1 = '1500'
	},
	[33] = {
		id = 33,
		group = 3,
		desc = 'Phòng ngự toàn đội +20%',
		desc_vn = 'Phòng ngự toàn đội +20%',
		desc_en = 'Team Defense +20%',
		desc_th = 'การป้องกันของทีมทั้งหมด +20%',
		desc_idn = 'Pertahanan seluruh tim +20%',
		desc_cn = '全队防御 +20%',
		desc_kr = '전대 방어 +20%',
		weight = 50,
		icon = 'city/adventure/random_tower/icon/icon_fy.png',
		iconBg = 'city/adventure/random_tower/icon/box_blue.png',
		buffColor = 'effect_lan',
		attrType1 = 17,
		attrNum1 = '2000'
	},
	[34] = {
		id = 34,
		desc = 'Hút máu toàn đội +4%',
		desc_vn = 'Hút máu toàn đội +4%',
		desc_en = 'Team Life Steal +4%',
		desc_th = 'ดูดเลือดของทีมทั้งหมด +4%',
		desc_idn = 'Serap darah seluruh tim +4%',
		desc_cn = '全队吸血 +4%',
		desc_kr = '전대 생명력 흡수 +4%',
		weight = 50,
		icon = 'city/adventure/random_tower/icon/icon_xx.png',
		iconBg = 'city/adventure/random_tower/icon/box_pink.png',
		buffColor = 'effect_lan',
		attrType1 = 26,
		attrNum1 = '400'
	},
	[35] = {
		id = 35,
		group = 2,
		desc = 'Hút máu toàn đội +8%',
		desc_vn = 'Hút máu toàn đội +8%',
		desc_en = 'Team Life Steal +8%',
		desc_th = 'ดูดเลือดของทีมทั้งหมด +8%',
		desc_idn = 'Serap darah seluruh tim +8%',
		desc_cn = '全队吸血 +8%',
		desc_kr = '전대 생명력 흡수 +8%',
		weight = 40,
		icon = 'city/adventure/random_tower/icon/icon_xx.png',
		iconBg = 'city/adventure/random_tower/icon/box_pink.png',
		buffColor = 'effect_lan',
		attrType1 = 26,
		attrNum1 = '800'
	},
	[36] = {
		id = 36,
		group = 3,
		desc = 'Hút máu toàn đội +10%',
		desc_vn = 'Hút máu toàn đội +10%',
		desc_en = 'Team Life Steal +10%',
		desc_th = 'ดูดเลือดของทีมทั้งหมด +10%',
		desc_idn = 'Serap darah seluruh tim +10%',
		desc_cn = '全队吸血 +10%',
		desc_kr = '전대 생명력 흡수 +10%',
		weight = 30,
		icon = 'city/adventure/random_tower/icon/icon_xx.png',
		iconBg = 'city/adventure/random_tower/icon/box_pink.png',
		buffColor = 'effect_lan',
		attrType1 = 26,
		attrNum1 = '1000'
	},
	[37] = {
		id = 37,
		group = 4,
		desc = 'Hồi 25% HP cho 1 tướng',
		desc_vn = 'Hồi 25% HP cho 1 tướng',
		desc_en = 'Heals 25% HP for 1 officer',
		desc_th = 'ฟื้นฟู HP 25% ให้กับ 1 ขุนพล',
		desc_idn = 'Memulihkan 25% HP untuk 1 jenderal',
		desc_cn = '回复1名将领25%生命值',
		desc_kr = '1장수 HP 25% 회복',
		weight = 20,
		icon = 'city/adventure/random_tower/icon/icon_hx.png',
		iconBg = 'city/adventure/random_tower/icon/box_pink.png',
		buffType = 2,
		supplyType = 1,
		supplyTarget = 1,
		supplyNum = 25,
		condition = 1
	},
	[38] = {
		id = 38,
		group = 5,
		desc = 'Hồi 50% HP cho 1 tướng',
		desc_vn = 'Hồi 50% HP cho 1 tướng',
		desc_en = 'Heals 50% HP for 1 officer',
		desc_th = 'ฟื้นฟู HP 50% ให้กับ 1 ขุนพล',
		desc_idn = 'Memulihkan 50% HP untuk 1 jenderal',
		desc_cn = '回复1名将领50%生命值',
		desc_kr = '1장수 HP 50% 회복',
		weight = 50,
		icon = 'city/adventure/random_tower/icon/icon_hx.png',
		iconBg = 'city/adventure/random_tower/icon/box_pink.png',
		buffType = 2,
		supplyType = 1,
		supplyTarget = 1,
		supplyNum = 50,
		condition = 1
	},
	[39] = {
		id = 39,
		group = 5,
		desc = 'Hồi 100% HP cho 1 tướng',
		desc_vn = 'Hồi 100% HP cho 1 tướng',
		desc_en = 'Heals 100% HP for 1 officer',
		desc_th = 'ฟื้นฟู HP 100% ให้กับ 1 ขุนพล',
		desc_idn = 'Memulihkan 100% HP untuk 1 jenderal',
		desc_cn = '回复1名将领100%生命值',
		desc_kr = '1장수 HP 100% 회복',
		weight = 10,
		icon = 'city/adventure/random_tower/icon/icon_hx.png',
		iconBg = 'city/adventure/random_tower/icon/box_pink.png',
		buffType = 2,
		supplyType = 1,
		supplyTarget = 1,
		supplyNum = 100,
		condition = 1
	},
	[40] = {
		id = 40,
		group = 4,
		desc = 'Hồi 25% HP cho 1 tướng',
		desc_vn = 'Hồi 25% HP cho 1 tướng',
		desc_en = 'Heals 25% HP for 1 officer',
		desc_th = 'ฟื้นฟู HP 25% ให้กับ 1 ขุนพล',
		desc_idn = 'Memulihkan 25% HP untuk 1 jenderal',
		desc_cn = '回复1名将领25%生命值',
		desc_kr = '1장수 HP 25% 회복',
		weight = 1,
		icon = 'city/adventure/random_tower/icon/icon_hx.png',
		iconBg = 'city/adventure/random_tower/icon/box_pink.png',
		buffType = 2,
		supplyType = 1,
		supplyTarget = 1,
		supplyNum = 25,
		condition = 1
	},
	[41] = {
		id = 41,
		group = 4,
		desc = 'Hồi 200 nộ cho 1 tướng',
		desc_vn = 'Hồi 200 nộ cho 1 tướng',
		desc_en = 'Restores 200 Rage for 1 officer',
		desc_th = 'ฟื้นฟูพลังโกรธ 200 ให้กับ 1 ขุนพล',
		desc_idn = 'Memulihkan 200 kemarahan untuk 1 jenderal',
		desc_cn = '回复1名将领200怒气',
		desc_kr = '1장수 분노 200 회복',
		weight = 20,
		icon = 'city/adventure/random_tower/icon/icon_hn.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffType = 2,
		supplyType = 2,
		supplyTarget = 1,
		supplyNum = 25,
		condition = 2
	},
	[42] = {
		id = 42,
		group = 5,
		desc = 'Hồi 500 nộ cho 1 tướng',
		desc_vn = 'Hồi 500 nộ cho 1 tướng',
		desc_en = 'Restores 500 Rage for 1 officer',
		desc_th = 'ฟื้นฟูพลังโกรธ 500 ให้กับ 1 ขุนพล',
		desc_idn = 'Memulihkan 500 kemarahan untuk 1 jenderal',
		desc_cn = '回复1名将领500怒气',
		desc_kr = '1장수 분노 500 회복',
		weight = 50,
		icon = 'city/adventure/random_tower/icon/icon_hn.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffType = 2,
		supplyType = 2,
		supplyTarget = 1,
		supplyNum = 50,
		condition = 2
	},
	[43] = {
		id = 43,
		group = 5,
		desc = 'Hồi 1000 nộ cho 1 tướng',
		desc_vn = 'Hồi 1000 nộ cho 1 tướng',
		desc_en = 'Restores 1000 Rage for 1 officer',
		desc_th = 'ฟื้นฟูพลังโกรธ 1000 ให้กับ 1 ขุนพล',
		desc_idn = 'Memulihkan 1000 kemarahan untuk 1 jenderal',
		desc_cn = '回复1名将领1000怒气',
		desc_kr = '1장수 분노 1000 회복',
		weight = 10,
		icon = 'city/adventure/random_tower/icon/icon_hn.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffType = 2,
		supplyType = 2,
		supplyTarget = 1,
		supplyNum = 100,
		condition = 2
	},
	[44] = {
		id = 44,
		group = 5,
		desc = 'Hồi 1000 nộ cho 1 tướng',
		desc_vn = 'Hồi 1000 nộ cho 1 tướng',
		desc_en = 'Restores 1000 Rage for 1 officer',
		desc_th = 'ฟื้นฟูพลังโกรธ 1000 ให้กับ 1 ขุนพล',
		desc_idn = 'Memulihkan 1000 kemarahan untuk 1 jenderal',
		desc_cn = '回复1名将领1000怒气',
		desc_kr = '1장수 분노 1000 회복',
		weight = 1,
		icon = 'city/adventure/random_tower/icon/icon_hn.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffType = 2,
		supplyType = 2,
		supplyTarget = 1,
		supplyNum = 100,
		condition = 2
	},
	[45] = {
		id = 45,
		group = 4,
		desc = 'Hồi sinh 1 tướng',
		desc_vn = 'Hồi sinh 1 tướng',
		desc_en = 'Revives 1 officer',
		desc_th = 'ชุบชีวิตขุนพล 1 คน',
		desc_idn = 'Menghidupkan kembali 1 jenderal',
		desc_cn = '复活1名将领',
		desc_kr = '1장수 부활',
		weight = 20,
		icon = 'city/adventure/random_tower/icon/icon_fh.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffType = 2,
		supplyType = 3,
		supplyTarget = 1,
		condition = 3
	},
	[46] = {
		id = 46,
		group = 5,
		desc = 'Hồi sinh 1 tướng',
		desc_vn = 'Hồi sinh 1 tướng',
		desc_en = 'Revives 1 officer',
		desc_th = 'ชุบชีวิตขุนพล 1 คน',
		desc_idn = 'Menghidupkan kembali 1 jenderal',
		desc_cn = '复活1名将领',
		desc_kr = '1장수 부활',
		weight = 20,
		icon = 'city/adventure/random_tower/icon/icon_fh.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffType = 2,
		supplyType = 3,
		supplyTarget = 1,
		condition = 3
	},
	[47] = {
		id = 47,
		group = 4,
		desc = 'Hồi sinh 1 tướng',
		desc_vn = 'Hồi sinh 1 tướng',
		desc_en = 'Revives 1 officer',
		desc_th = 'ชุบชีวิตขุนพล 1 คน',
		desc_idn = 'Menghidupkan kembali 1 jenderal',
		desc_cn = '复活1名将领',
		desc_kr = '1장수 부활',
		weight = 1,
		icon = 'city/adventure/random_tower/icon/icon_fh.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffType = 2,
		supplyType = 3,
		supplyTarget = 1,
		condition = 3
	},
	[48] = {
		id = 48,
		group = 5,
		desc = 'Hồi sinh 1 tướng',
		desc_vn = 'Hồi sinh 1 tướng',
		desc_en = 'Revives 1 officer',
		desc_th = 'ชุบชีวิตขุนพล 1 คน',
		desc_idn = 'Menghidupkan kembali 1 jenderal',
		desc_cn = '复活1名将领',
		desc_kr = '1장수 부활',
		weight = 1,
		icon = 'city/adventure/random_tower/icon/icon_fh.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffType = 2,
		supplyType = 3,
		supplyTarget = 1,
		condition = 3
	},
	[49] = {
		id = 49,
		group = 6,
		desc = 'HP toàn đội hồi đầy',
		desc_vn = 'HP toàn đội hồi đầy',
		desc_en = 'Fully restores HP for the entire team',
		desc_th = 'ฟื้นฟู HP ของทีมทั้งหมดเต็ม',
		desc_idn = 'HP seluruh tim pulih penuh',
		desc_cn = '全队生命值恢复满',
		desc_kr = '전대 HP 전부 회복',
		weight = 20,
		icon = 'city/adventure/random_tower/icon/icon_hx.png',
		iconBg = 'city/adventure/random_tower/icon/box_pink.png',
		buffType = 2,
		supplyType = 1,
		supplyTarget = 2,
		supplyNum = 100,
		condition = 1
	},
	[50] = {
		id = 50,
		group = 6,
		desc = 'Nộ toàn đội hồi đầy',
		desc_vn = 'Nộ toàn đội hồi đầy',
		desc_en = 'Fully restores Rage for the entire team',
		desc_th = 'ฟื้นฟูพลังโกรธของทีมทั้งหมดเต็ม',
		desc_idn = 'Kemarahan seluruh tim pulih penuh',
		desc_cn = '全队怒气恢复满',
		desc_kr = '전대 분노 전부 회복',
		weight = 20,
		icon = 'city/adventure/random_tower/icon/icon_hn.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffType = 2,
		supplyType = 2,
		supplyTarget = 2,
		supplyNum = 100,
		condition = 2
	},
	[51] = {
		id = 51,
		group = 6,
		desc = 'Hồi sinh 1 tướng',
		desc_vn = 'Hồi sinh 1 tướng',
		desc_en = 'Revives 1 officer',
		desc_th = 'ชุบชีวิตขุนพล 1 คน',
		desc_idn = 'Menghidupkan kembali 1 jenderal',
		desc_cn = '复活1名将领',
		desc_kr = '1장수 부활',
		weight = 1,
		icon = 'city/adventure/random_tower/icon/icon_fh.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffType = 2,
		supplyType = 3,
		supplyTarget = 1,
		condition = 3
	},
	[52] = {
		id = 52,
		group = 99,
		desc = 'Cuối trận nhận thêm điểm = số lượt còn lại ×2',
		desc_vn = 'Cuối trận nhận thêm điểm = số lượt còn lại ×2',
		desc_en = 'End of battle bonus points = remaining turns ×2',
		desc_th = 'รับคะแนนเพิ่มเมื่อจบการรบ = จำนวนรอบที่เหลือ ×2',
		desc_idn = 'Mendapat poin tambahan di akhir pertandingan = jumlah giliran tersisa ×2',
		desc_cn = '战斗结束额外获得积分 = 剩余回合数 ×2',
		desc_kr = '전투 종료 시 추가 점수 = 남은 턴 수 ×2',
		icon = 'city/adventure/random_tower/icon/icon_yybm.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect_huang',
		buffType = 3,
		pointType = 1,
		pointValue = 2,
		onlyOne = true
	},
	[53] = {
		id = 53,
		group = 99,
		desc = 'Cuối trận nhận thêm điểm = số tướng sống sót ×1',
		desc_vn = 'Cuối trận nhận thêm điểm = số tướng sống sót ×1',
		desc_en = 'End of battle bonus points = number of surviving officers ×1',
		desc_th = 'รับคะแนนเพิ่มเมื่อจบการรบ = จำนวนขุนพลที่รอดชีวิต ×1',
		desc_idn = 'Mendapat poin tambahan di akhir pertandingan = jumlah jenderal yang bertahan ×1',
		desc_cn = '战斗结束额外获得积分 = 存活将领数 ×1',
		desc_kr = '전투 종료 시 추가 점수 = 생존 장수 수 ×1',
		weight = 10,
		icon = 'city/adventure/random_tower/icon/icon_yybm.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect_huang',
		buffType = 3,
		pointType = 2,
		pointValue = 1,
		onlyOne = true
	},
	[54] = {
		id = 54,
		group = 99,
		desc = 'Cuối trận nhận thêm điểm = số tướng sống sót ×2',
		desc_vn = 'Cuối trận nhận thêm điểm = số tướng sống sót ×2',
		desc_en = 'End of battle bonus points = number of surviving officers ×2',
		desc_th = 'รับคะแนนเพิ่มเมื่อจบการรบ = จำนวนขุนพลที่รอดชีวิต ×2',
		desc_idn = 'Mendapat poin tambahan di akhir pertandingan = jumlah jenderal yang bertahan ×2',
		desc_cn = '战斗结束额外获得积分 = 存活将领数 ×2',
		desc_kr = '전투 종료 시 추가 점수 = 생존 장수 수 ×2',
		icon = 'city/adventure/random_tower/icon/icon_yybm.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect_huang',
		buffType = 3,
		pointType = 2,
		pointValue = 2,
		onlyOne = true
	},
	[55] = {
		id = 55,
		group = 99,
		desc = 'Cuối trận nhận thêm điểm = tầng hiện tại ×2',
		desc_vn = 'Cuối trận nhận thêm điểm = tầng hiện tại ×2',
		desc_en = 'End of battle bonus points = current floor ×2',
		desc_th = 'รับคะแนนเพิ่มเมื่อจบการรบ = ชั้นปัจจุบัน ×2',
		desc_idn = 'Mendapat poin tambahan di akhir pertandingan = lantai saat ini ×2',
		desc_cn = '战斗结束额外获得积分 = 当前层数 ×2',
		desc_kr = '전투 종료 시 추가 점수 = 현재 층수 ×2',
		icon = 'city/adventure/random_tower/icon/icon_yybm.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect_huang',
		buffType = 3,
		pointType = 3,
		pointValue = 2,
		onlyOne = true
	},
	[56] = {
		id = 56,
		group = 99,
		desc = 'Vật Công và Trí Lực toàn đội +5%',
		desc_vn = 'Vật Công và Trí Lực toàn đội +5%',
		desc_en = 'Team Physical Attack and Intelligence +5%',
		desc_th = 'พลังโจมตีและปัญญาของทีมทั้งหมด +5%',
		desc_idn = 'Serangan Fisik dan Kecerdasan seluruh tim +5%',
		desc_cn = '全队物理攻击和智力 +5%',
		desc_kr = '전대의 물리 공격과 지력 +5%',
		weight = 1,
		icon = 'city/adventure/random_tower/icon/icon_gj.png',
		iconBg = 'city/adventure/random_tower/icon/box_red.png',
		buffColor = 'effect',
		attrType1 = 8,
		attrNum1 = '5%',
		attrType2 = 7,
		attrNum2 = '5%'
	},
	[101] = {
		id = 101,
		group = 21,
		desc = 'Vật Công của toàn đội +20%',
		desc_vn = 'Vật Công của toàn đội +20%',
		desc_en = 'Team Physical Attack +20%',
		desc_th = 'พลังโจมตีของทีมทั้งหมด +20%',
		desc_idn = 'Serangan Fisik seluruh tim +20%',
		desc_cn = '全队物理攻击 +20%',
		desc_kr = '전대의 물리 공격 +20%',
		icon = 'city/adventure/random_tower/icon/icon_gj.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect',
		attrType1 = 7,
		attrNum1 = '20%',
		onlyOne = true,
		limit = 1
	},
	[102] = {
		id = 102,
		group = 21,
		desc = 'Trí Lực của toàn đội +20%',
		desc_vn = 'Trí Lực của toàn đội +20%',
		desc_en = 'Team Intelligence +20%',
		desc_th = 'ปัญญาของทีมทั้งหมด +20%',
		desc_idn = 'Kecerdasan seluruh tim +20%',
		desc_cn = '全队智力 +20%',
		desc_kr = '전대의 지력 +20%',
		icon = 'city/adventure/random_tower/icon/icon_zl.png',
		iconBg = 'city/adventure/random_tower/icon/box_blue.png',
		buffColor = 'effect',
		attrType1 = 8,
		attrNum1 = '20%',
		onlyOne = true,
		limit = 1
	},
	[103] = {
		id = 103,
		group = 21,
		desc = 'Vật Công và Trí Lực toàn đội +15%',
		desc_vn = 'Vật Công và Trí Lực toàn đội +15%',
		desc_en = 'Team Physical Attack and Intelligence +15%',
		desc_th = 'พลังโจมตีและปัญญาของทีมทั้งหมด +15%',
		desc_idn = 'Serangan Fisik dan Kecerdasan seluruh tim +15%',
		desc_cn = '全队物理攻击和智力 +15%',
		desc_kr = '전대의 물리 공격과 지력 +15%',
		icon = 'city/adventure/random_tower/icon/icon_gj.png',
		iconBg = 'city/adventure/random_tower/icon/box_red.png',
		buffColor = 'effect',
		attrType1 = 8,
		attrNum1 = '15%',
		attrType2 = 7,
		attrNum2 = '15%',
		limit = 2
	},
	[104] = {
		id = 104,
		group = 21,
		desc = 'Sát thương gây ra của toàn đội +20%',
		desc_vn = 'Sát thương gây ra của toàn đội +20%',
		desc_en = 'Team Damage Dealt +20%',
		desc_th = 'ความเสียหายที่ทีมทั้งหมดสร้าง +20%',
		desc_idn = 'Kerusakan yang diberikan seluruh tim +20%',
		desc_cn = '全队造成的伤害 +20%',
		desc_kr = '전대가 입히는 피해 +20%',
		icon = 'city/adventure/random_tower/icon/icon_fh.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect',
		attrType1 = 22,
		attrNum1 = '2000',
		onlyOne = true,
		limit = 1
	},
	[105] = {
		id = 105,
		group = 21,
		desc = 'Tỷ lệ chí mạng của toàn đội +20%',
		desc_vn = 'Tỷ lệ chí mạng của toàn đội +20%',
		desc_en = 'Team Critical Rate +20%',
		desc_th = 'อัตราคริติคอลของทีมทั้งหมด +20%',
		desc_idn = 'Peluang kritikal seluruh tim +20%',
		desc_cn = '全队暴击率 +20%',
		desc_kr = '전대의 치명타 확률 +20%',
		icon = 'city/adventure/random_tower/icon/icon_xw.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect',
		attrType1 = 14,
		attrNum1 = '2000',
		onlyOne = true,
		limit = 1
	},
	[106] = {
		id = 106,
		group = 21,
		desc = 'Sát thương chí mạng của toàn đội +30%',
		desc_vn = 'Sát thương chí mạng của toàn đội +30%',
		desc_en = 'Team Critical Damage +30%',
		desc_th = 'ความเสียหายคริติคอลของทีมทั้งหมด +30%',
		desc_idn = 'Kerusakan kritikal seluruh tim +30%',
		desc_cn = '全队暴击伤害 +30%',
		desc_kr = '전대의 치명타 피해 +30%',
		icon = 'city/adventure/random_tower/icon/icon_js1.png',
		iconBg = 'city/adventure/random_tower/icon/box_orange.png',
		buffColor = 'effect',
		attrType1 = 15,
		attrNum1 = '30%',
		onlyOne = true,
		limit = 1
	},
	[121] = {
		id = 121,
		group = 22,
		desc = 'Vật Phòng của toàn đội +30%',
		desc_vn = 'Vật Phòng của toàn đội +30%',
		desc_en = 'Team Physical Defense +30%',
		desc_th = 'พลังป้องกันกายภาพของทีมทั้งหมด +30%',
		desc_idn = 'Pertahanan Fisik seluruh tim +30%',
		desc_cn = '全队物理防御 +30%',
		desc_kr = '전대의 물리 방어 +30%',
		icon = 'city/adventure/random_tower/icon/icon_jh.png',
		iconBg = 'city/adventure/random_tower/icon/box_green.png',
		buffColor = 'effect_lan',
		attrType1 = 9,
		attrNum1 = '30%',
		onlyOne = true,
		limit = 1
	},
	[122] = {
		id = 122,
		group = 22,
		desc = 'Pháp Phòng của toàn đội +30%',
		desc_vn = 'Pháp Phòng của toàn đội +30%',
		desc_en = 'Team Magical Defense +30%',
		desc_th = 'พลังป้องกันเวทย์ของทีมทั้งหมด +30%',
		desc_idn = 'Pertahanan Magis seluruh tim +30%',
		desc_cn = '全队法术防御 +30%',
		desc_kr = '전대의 법술 방어 +30%',
		icon = 'city/adventure/random_tower/icon/icon_fs.png',
		iconBg = 'city/adventure/random_tower/icon/box_purple.png',
		buffColor = 'effect_lan',
		attrType1 = 10,
		attrNum1 = '30%',
		onlyOne = true,
		limit = 1
	},
	[123] = {
		id = 123,
		group = 22,
		desc = 'Vật Phòng và Pháp Phòng toàn đội +25%',
		desc_vn = 'Vật Phòng và Pháp Phòng toàn đội +25%',
		desc_en = 'Team Physical and Magical Defense +25%',
		desc_th = 'พลังป้องกันกายภาพและเวทย์ของทีมทั้งหมด +25%',
		desc_idn = 'Pertahanan Fisik dan Pertahanan Magis seluruh tim +25%',
		desc_cn = '全队物理防御和法术防御 +25%',
		desc_kr = '전대의 물리 방어와 법술 방어 +25%',
		icon = 'city/adventure/random_tower/icon/icon_fy.png',
		iconBg = 'city/adventure/random_tower/icon/box_blue.png',
		buffColor = 'effect_lan',
		attrType1 = 10,
		attrNum1 = '25%',
		attrType2 = 9,
		attrNum2 = '25%',
		limit = 2
	},
	[124] = {
		id = 124,
		group = 22,
		desc = 'Giảm sát thương nhận vào của toàn đội +20%',
		desc_vn = 'Giảm sát thương nhận vào của toàn đội +20%',
		desc_en = 'Team Damage Received Reduced by 20%',
		desc_th = 'ลดความเสียหายที่ทีมทั้งหมดได้รับ +20%',
		desc_idn = 'Mengurangi kerusakan yang diterima seluruh tim +20%',
		desc_cn = '全队受到伤害减少 +20%',
		desc_kr = '전대가 받는 피해 감소 +20%',
		icon = 'city/adventure/random_tower/icon/icon_hx1.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect_lan',
		attrType1 = 23,
		attrNum1 = '2000',
		onlyOne = true,
		limit = 1
	},
	[200] = {
		id = 200,
		group = 1111,
		desc = 'Giảm 10% HP hiện tại',
		desc_vn = 'Giảm 10% HP hiện tại',
		desc_en = 'Reduces current HP by 10%',
		desc_th = 'ลด HP ปัจจุบัน 10%',
		desc_idn = 'Mengurangi 10% HP saat ini',
		desc_cn = '当前生命值减少10%',
		desc_kr = '현재 HP 10% 감소',
		icon = 'city/adventure/random_tower/icon/icon_qhzl.png',
		buffType = 2,
		supplyType = 4,
		supplyTarget = 2,
		supplyNum = 10
	},
	[201] = {
		id = 201,
		group = 1111,
		desc = 'Giảm 20% HP hiện tại',
		desc_vn = 'Giảm 20% HP hiện tại',
		desc_en = 'Reduces current HP by 20%',
		desc_th = 'ลด HP ปัจจุบัน 20%',
		desc_idn = 'Mengurangi 20% HP saat ini',
		desc_cn = '当前生命值减少20%',
		desc_kr = '현재 HP 20% 감소',
		icon = 'city/adventure/random_tower/icon/icon_qhzl.png',
		buffType = 2,
		supplyType = 4,
		supplyTarget = 2,
		supplyNum = 20
	},
	[202] = {
		id = 202,
		group = 1111,
		desc = 'Giảm 30% HP hiện tại',
		desc_vn = 'Giảm 30% HP hiện tại',
		desc_en = 'Reduces current HP by 30%',
		desc_th = 'ลด HP ปัจจุบัน 30%',
		desc_idn = 'Mengurangi 30% HP saat ini',
		desc_cn = '当前生命值减少30%',
		desc_kr = '현재 HP 30% 감소',
		icon = 'city/adventure/random_tower/icon/icon_qhzl.png',
		buffType = 2,
		supplyType = 4,
		supplyTarget = 2,
		supplyNum = 30
	},
	[203] = {
		id = 203,
		group = 1111,
		desc = 'Giảm 200 nộ hiện tại',
		desc_vn = 'Giảm 200 nộ hiện tại',
		desc_en = 'Reduces current Rage by 200',
		desc_th = 'ลดพลังโกรธปัจจุบัน 200',
		desc_idn = 'Mengurangi 200 kemarahan saat ini',
		desc_cn = '当前怒气减少200',
		desc_kr = '현재 분노 200 감소',
		icon = 'city/adventure/random_tower/icon/icon_js2.png',
		buffType = 2,
		supplyType = 5,
		supplyTarget = 2,
		supplyNum = 20
	},
	[204] = {
		id = 204,
		group = 1111,
		desc = 'Giảm 500 nộ hiện tại',
		desc_vn = 'Giảm 500 nộ hiện tại',
		desc_en = 'Reduces current Rage by 500',
		desc_th = 'ลดพลังโกรธปัจจุบัน 500',
		desc_idn = 'Mengurangi 500 kemarahan saat ini',
		desc_cn = '当前怒气减少500',
		desc_kr = '현재 분노 500 감소',
		icon = 'city/adventure/random_tower/icon/icon_js2.png',
		buffType = 2,
		supplyType = 5,
		supplyTarget = 2,
		supplyNum = 50
	},
	[205] = {
		id = 205,
		group = 1111,
		desc = 'Giảm 1000 nộ hiện tại',
		desc_vn = 'Giảm 1000 nộ hiện tại',
		desc_en = 'Reduces current Rage by 1000',
		desc_th = 'ลดพลังโกรธปัจจุบัน 1000',
		desc_idn = 'Mengurangi 1000 kemarahan saat ini',
		desc_cn = '当前怒气减少1000',
		desc_kr = '현재 분노 1000 감소',
		icon = 'city/adventure/random_tower/icon/icon_js2.png',
		buffType = 2,
		supplyType = 5,
		supplyTarget = 2,
		supplyNum = 100
	},
	[206] = {
		id = 206,
		group = 1111,
		desc = 'Tăng 500 nộ hiện tại',
		desc_vn = 'Tăng 500 nộ hiện tại',
		desc_en = 'Increases current Rage by 500',
		desc_th = 'เพิ่มพลังโกรธปัจจุบัน 500',
		desc_idn = 'Menambah 500 kemarahan saat ini',
		desc_cn = '当前怒气增加500',
		desc_kr = '현재 분노 500 증가',
		icon = 'city/adventure/random_tower/icon/icon_js2.png',
		buffType = 2,
		supplyType = 2,
		supplyTarget = 2,
		supplyNum = 50
	},
	[207] = {
		id = 207,
		group = 1111,
		desc = 'Tốc độ toàn đội -20%',
		desc_vn = 'Tốc độ toàn đội -20%',
		desc_en = 'Team Speed -20%',
		desc_th = 'ความเร็วของทีมทั้งหมดลดลง 20%',
		desc_idn = 'Kecepatan seluruh tim -20%',
		desc_cn = '全队速度 -20%',
		desc_kr = '전대 속도 -20%',
		icon = 'city/adventure/random_tower/icon/icon_xx.png',
		buffColor = 'effect_lan',
		attrType1 = 13,
		attrNum1 = '-20%'
	},
	[208] = {
		id = 208,
		group = 1111,
		desc = 'Tốc độ toàn đội -40%',
		desc_vn = 'Tốc độ toàn đội -40%',
		desc_en = 'Team Speed -40%',
		desc_th = 'ความเร็วของทีมทั้งหมดลดลง 40%',
		desc_idn = 'Kecepatan seluruh tim -40%',
		desc_cn = '全队速度 -40%',
		desc_kr = '전대 속도 -40%',
		icon = 'city/adventure/random_tower/icon/icon_xx.png',
		buffColor = 'effect_lan',
		attrType1 = 13,
		attrNum1 = '-40%'
	},
	[210] = {
		id = 210,
		group = 1111,
		desc = 'HP toàn đội hồi đầy',
		desc_vn = 'HP toàn đội hồi đầy',
		desc_en = 'Fully restores HP for the entire team',
		desc_th = 'ฟื้นฟู HP ของทีมทั้งหมดเต็ม',
		desc_idn = 'HP seluruh tim pulih penuh',
		desc_cn = '全队生命值恢复满',
		desc_kr = '전대 HP 전부 회복',
		icon = 'city/adventure/random_tower/icon/icon_xx.png',
		buffType = 2,
		supplyType = 1,
		supplyTarget = 2,
		supplyNum = 100
	},
	[211] = {
		id = 211,
		group = 1111,
		desc = 'Nộ toàn đội hồi đầy',
		desc_vn = 'Nộ toàn đội hồi đầy',
		desc_en = 'Fully restores Rage for the entire team',
		desc_th = 'ฟื้นฟูพลังโกรธของทีมทั้งหมดเต็ม',
		desc_idn = 'Kemarahan seluruh tim pulih penuh',
		desc_cn = '全队怒气恢复满',
		desc_kr = '전대 분노 전부 회복',
		icon = 'city/adventure/random_tower/icon/icon_xx.png',
		buffType = 2,
		supplyType = 2,
		supplyTarget = 2,
		supplyNum = 100
	},
	[212] = {
		id = 212,
		group = 1111,
		desc = 'Vật Công toàn đội -5%',
		desc_vn = 'Vật Công toàn đội -5%',
		desc_en = 'Team Physical Attack -5%',
		desc_th = 'พลังโจมตีของทีมทั้งหมดลดลง 5%',
		desc_idn = 'Serangan Fisik seluruh tim -5%',
		desc_cn = '全队物理攻击 -5%',
		desc_kr = '전대 물리 공격 -5%',
		icon = 'city/adventure/random_tower/icon/icon_dk.png',
		buffColor = 'effect',
		attrType1 = 7,
		attrNum1 = '-5%'
	},
	[213] = {
		id = 213,
		group = 1111,
		desc = 'Trí Lực toàn đội -5%',
		desc_vn = 'Trí Lực toàn đội -5%',
		desc_en = 'Team Intelligence -5%',
		desc_th = 'ปัญญาของทีมทั้งหมดลดลง 5%',
		desc_idn = 'Kecerdasan seluruh tim -5%',
		desc_cn = '全队智力 -5%',
		desc_kr = '전대 지력 -5%',
		icon = 'city/adventure/random_tower/icon/icon_zl.png',
		buffColor = 'effect',
		attrType1 = 8,
		attrNum1 = '-5%'
	},
	[214] = {
		id = 214,
		group = 1111,
		desc = 'Toàn đội hồi 20% HP',
		desc_vn = 'Toàn đội hồi 20% HP',
		desc_en = 'Team heals 20% HP',
		desc_th = 'ฟื้นฟู HP ทีมทั้งหมด 20%',
		desc_idn = 'Seluruh tim memulihkan 20% HP',
		desc_cn = '全队回复20%生命值',
		desc_kr = '전대 HP 20% 회복',
		icon = 'city/adventure/random_tower/icon/icon_yybm.png',
		buffType = 2,
		supplyType = 1,
		supplyTarget = 2,
		supplyNum = 20
	},
	[215] = {
		id = 215,
		group = 1111,
		desc = 'Nộ toàn đội +200',
		desc_vn = 'Nộ toàn đội +200',
		desc_en = 'Team Rage +200',
		desc_th = 'เพิ่มพลังโกรธของทีมทั้งหมด 200',
		desc_idn = 'Kemarahan seluruh tim +200',
		desc_cn = '全队怒气 +200',
		desc_kr = '전대 분노 +200',
		icon = 'city/adventure/random_tower/icon/icon_yybm3.png',
		buffType = 2,
		supplyType = 2,
		supplyTarget = 2,
		supplyNum = 20
	},
	[1001] = {
		id = 1001,
		group = 103,
		desc = 'Khi HP của toàn đội dưới 40%, mỗi đòn đánh tăng 10% sát thương (có thể cộng dồn)',
		desc_vn = 'Khi HP của toàn đội dưới 40%, mỗi đòn đánh tăng 10% sát thương (có thể cộng dồn)',
		desc_en = 'When team HP is below 40%, each hit increases damage by 10% (stacks)',
		desc_th = 'เมื่อ HP ของทีมทั้งหมดต่ำกว่า 40% ทุกการโจมตีเพิ่มความเสียหาย 10% (สามารถสะสมได้)',
		desc_idn = 'Ketika HP seluruh tim di bawah 40%, setiap serangan menambah 10% kerusakan (dapat menumpuk)',
		desc_cn = '当全队生命低于40%时，每次攻击增加10%伤害（可叠加）',
		desc_kr = '전대 HP가 40% 이하일 때, 공격 시마다 피해 10% 증가 (중첩 가능)',
		icon = 'city/adventure/random_tower/icon/icon_zh.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80001,
		onlyOne = true,
		limit = 1,
		changeLib = 5
	},
	[1002] = {
		id = 1002,
		group = 200,
		desc = 'Cứ mất 10% HP, toàn đội tăng 8% sát thương',
		desc_vn = 'Cứ mất 10% HP, toàn đội tăng 8% sát thương',
		desc_en = 'For every 10% HP lost, team damage increases by 8%',
		desc_th = 'ทุกครั้งที่เสีย HP 10% ทีมทั้งหมดเพิ่มความเสียหาย 8%',
		desc_idn = 'Setiap kehilangan 10% HP, seluruh tim meningkatkan 8% kerusakan',
		desc_cn = '每损失10%生命，全队增加8%伤害',
		desc_kr = 'HP 10% 감소할 때마다, 전대 피해 8% 증가',
		icon = 'city/adventure/random_tower/icon/icon_cxsh.png',
		iconBg = 'city/adventure/random_tower/icon/box_red.png',
		buffColor = 'effect',
		buffType = 4,
		passiveSkill = 80002,
		onlyOne = true,
		belongLib = 5
	},
	[1003] = {
		id = 1003,
		group = 200,
		desc = 'Mỗi lần đánh trúng địch, nhận thêm sát thương chuẩn bằng 8% HP đã mất',
		desc_vn = 'Mỗi lần đánh trúng địch, nhận thêm sát thương chuẩn bằng 8% HP đã mất',
		desc_en = 'Each time an enemy is hit, gain additional true damage equal to 8% of HP lost',
		desc_th = 'ทุกครั้งที่โจมตีโดนศัตรู รับความเสียหายธรรมดาเพิ่มเท่ากับ 8% ของ HP ที่เสียไป',
		desc_idn = 'Setiap serangan yang mengenai musuh, mendapatkan tambahan kerusakan sejati sebesar 8% HP yang hilang',
		desc_cn = '每次攻击命中敌人，额外造成相当于已损失生命8%的真实伤害',
		desc_kr = '적 명중 시, 잃은 HP의 8%에 해당하는 고정 피해 추가 획득',
		icon = 'city/adventure/random_tower/icon/icon_yybm1.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80003,
		onlyOne = true,
		belongLib = 5
	},
	[1004] = {
		id = 1004,
		group = 200,
		desc = 'Mỗi lần đánh trúng địch, gây thêm sát thương chuẩn bằng 5% HP đã mất của địch',
		desc_vn = 'Mỗi lần đánh trúng địch, gây thêm sát thương chuẩn bằng 5% HP đã mất của địch',
		desc_en = 'Each time an enemy is hit, deal extra true damage equal to 5% of enemy\'s HP lost',
		desc_th = 'ทุกครั้งที่โจมตีโดนศัตรู สร้างความเสียหายธรรมดาเพิ่มเท่ากับ 5% ของ HP ที่ศัตรูเสียไป',
		desc_idn = 'Setiap serangan yang mengenai musuh, memberikan tambahan kerusakan sejati sebesar 5% HP yang hilang dari musuh',
		desc_cn = '每次攻击命中敌人，额外造成相当于敌人已损失生命5%的真实伤害',
		desc_kr = '적 명중 시, 적 잃은 HP의 5%에 해당하는 고정 피해 추가 가함',
		icon = 'city/adventure/random_tower/icon/icon_yybm1.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80004,
		onlyOne = true,
		belongLib = 2
	},
	[1005] = {
		id = 1005,
		group = 101,
		desc = 'Tăng sát thương bằng 30% chỉ số Phòng Ngự hiện tại',
		desc_vn = 'Tăng sát thương bằng 30% chỉ số Phòng Ngự hiện tại',
		desc_en = 'Increase damage by 30% of current Defense stat',
		desc_th = 'เพิ่มความเสียหายโดยใช้ 30% ของค่าสถานะการป้องกันปัจจุบัน',
		desc_idn = 'Menambah kerusakan sebesar 30% dari nilai Pertahanan saat ini',
		desc_cn = '伤害提升30%当前防御属性',
		desc_kr = '현재 방어력 수치의 30%만큼 피해 증가',
		icon = 'city/adventure/random_tower/icon/icon_qs.png',
		iconBg = 'city/adventure/random_tower/icon/box_red.png',
		buffColor = 'effect',
		buffType = 4,
		passiveSkill = 80005,
		onlyOne = true,
		limit = 1,
		changeLib = 7
	},
	[1006] = {
		id = 1006,
		group = 999,
		desc = 'Chiêu cuối đầu tiên trong trận được tăng 50% tỷ lệ chí mạng',
		desc_vn = 'Chiêu cuối đầu tiên trong trận được tăng 50% tỷ lệ chí mạng',
		desc_en = 'First ultimate skill in battle gains 50% increased critical rate',
		desc_th = 'สกิลอัลติเมทครั้งแรกในรบนี้เพิ่มอัตราคริติคอล 50%',
		desc_idn = 'Serangan pamungkas pertama dalam pertandingan mendapat tambahan 50% peluang kritikal',
		desc_cn = '战斗首个大招暴击率提升50%',
		desc_kr = '전투 중 첫 번째 궁극기 치명타 확률 50% 증가',
		icon = 'city/adventure/random_tower/icon/icon_nqhs.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80006,
		effectTimes = 1,
		onlyOne = true
	},
	[1007] = {
		id = 1007,
		group = 200,
		desc = 'Mỗi lần tung chiêu cuối, sát thương chiêu đó tăng 10%, duy trì đến hết trận',
		desc_vn = 'Mỗi lần tung chiêu cuối, sát thương chiêu đó tăng 10%, duy trì đến hết trận',
		desc_en = 'Each time the ultimate skill is used, its damage increases by 10%, lasting until the end of battle',
		desc_th = 'ทุกครั้งที่ใช้สกิลอัลติเมท ความเสียหายสกิลนั้นเพิ่ม 10% คงอยู่จนจบการรบ',
		desc_idn = 'Setiap kali menggunakan serangan pamungkas, kerusakan serangan tersebut meningkat 10%, bertahan sampai akhir pertandingan',
		desc_cn = '每释放一次大招，该大招伤害提升10%，持续至战斗结束',
		desc_kr = '궁극기 사용 시마다 해당 스킬 피해 10% 증가, 전투 종료 시까지 유지',
		icon = 'city/adventure/random_tower/icon/icon_nqhs.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80007,
		onlyOne = true,
		belongLib = 2
	},
	[1008] = {
		id = 1008,
		group = 200,
		desc = 'Sau khi dùng kỹ năng thường rồi tung chiêu cuối, sát thương tăng 10%, hiệu ứng mất khi chiêu cuối được tung ra',
		desc_vn = 'Sau khi dùng kỹ năng thường rồi tung chiêu cuối, sát thương tăng 10%, hiệu ứng mất khi chiêu cuối được tung ra',
		desc_en = 'After using a normal skill and then an ultimate, damage increases by 10%, effect ends when ultimate is used',
		desc_th = 'หลังจากใช้สกิลปกติแล้วใช้สกิลอัลติเมท ความเสียหายเพิ่ม 10% เอฟเฟกต์นี้จะหายไปเมื่อใช้สกิลอัลติเมท',
		desc_idn = 'Setelah menggunakan skill biasa lalu serangan pamungkas, kerusakan meningkat 10%, efek hilang saat serangan pamungkas digunakan',
		desc_cn = '普通技能后接大招，伤害提升10%，大招释放后效果消失',
		desc_kr = '일반 스킬 사용 후 궁극기 사용 시 피해 10% 증가, 궁극기 사용 시 효과 해제',
		icon = 'city/adventure/random_tower/icon/icon_nqhs.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80008,
		onlyOne = true,
		belongLib = 2
	},
	[1009] = {
		id = 1009,
		group = 200,
		desc = 'Khi HP tướng dưới 40%, giảm sát thương nhận vào 20%',
		desc_vn = 'Khi HP tướng dưới 40%, giảm sát thương nhận vào 20%',
		desc_en = 'When officer HP is below 40%, damage received is reduced by 20%',
		desc_th = 'เมื่อ HP ขุนพลต่ำกว่า 40% ลดความเสียหายที่ได้รับ 20%',
		desc_idn = 'Ketika HP jenderal di bawah 40%, mengurangi kerusakan yang diterima 20%',
		desc_cn = '当将领生命低于40%，减少受到伤害20%',
		desc_kr = '장수 HP 40% 이하일 때 받는 피해 20% 감소',
		icon = 'city/adventure/random_tower/icon/icon_fy.png',
		iconBg = 'city/adventure/random_tower/icon/box_blue.png',
		buffColor = 'effect_blue',
		buffType = 4,
		passiveSkill = 80009,
		onlyOne = true,
		belongLib = 5
	},
	[1010] = {
		id = 1010,
		group = 200,
		desc = 'Khi HP tướng dưới 10%, tăng 50% hút máu, mất hiệu lực khi HP trên 10%',
		desc_vn = 'Khi HP tướng dưới 10%, tăng 50% hút máu, mất hiệu lực khi HP trên 10%',
		desc_en = 'When officer HP is below 10%, life steal increases by 50%, effect ends when HP is above 10%',
		desc_th = 'เมื่อ HP ขุนพลต่ำกว่า 10% เพิ่มการดูดเลือด 50% เอฟเฟกต์จะหายไปเมื่อ HP มากกว่า 10%',
		desc_idn = 'Ketika HP jenderal di bawah 10%, meningkatkan serap darah 50%, hilang saat HP di atas 10%',
		desc_cn = '当将领生命低于10%，吸血提升50%，生命高于10%时失效',
		desc_kr = '장수 HP 10% 이하일 때 생명력 흡수 50% 증가, HP 10% 초과 시 효과 해제',
		icon = 'city/adventure/random_tower/icon/icon_nqhs.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80010,
		onlyOne = true,
		belongLib = 5
	},
	[1011] = {
		id = 1011,
		group = 200,
		desc = 'Mỗi lần nhận sát thương, tăng Vật Công và Pháp Phòng 15%, cộng dồn 3 lần trong 2 lượt',
		desc_vn = 'Mỗi lần nhận sát thương, tăng Vật Công và Pháp Phòng 15%, cộng dồn 3 lần trong 2 lượt',
		desc_en = 'Each time damage is taken, Physical Attack and Magical Defense increase by 15%, stacks up to 3 times for 2 turns',
		desc_th = 'ทุกครั้งที่ได้รับความเสียหาย เพิ่มพลังโจมตีและพลังป้องกันเวทย์ 15% สะสมได้ 3 ครั้งใน 2 รอบ',
		desc_idn = 'Setiap kali menerima kerusakan, menambah Serangan Fisik dan Pertahanan Magis 15%, dapat menumpuk 3 kali dalam 2 giliran',
		desc_cn = '每次受到伤害，物理攻击和法术防御提升15%，持续2回合可叠加3次',
		desc_kr = '피해 받을 때마다 물리 공격과 법술 방어 15% 증가, 2턴간 3중첩 가능',
		icon = 'city/adventure/random_tower/icon/icon_fy.png',
		iconBg = 'city/adventure/random_tower/icon/box_blue.png',
		buffColor = 'effect_blue',
		buffType = 4,
		passiveSkill = 80011,
		onlyOne = true,
		belongLib = 7
	},
	[1012] = {
		id = 1012,
		group = 200,
		desc = 'Khi HP tướng dưới 10%, tăng 5% hút máu',
		desc_vn = 'Khi HP tướng dưới 10%, tăng 5% hút máu',
		desc_en = 'When officer HP is below 10%, life steal increases by 5%',
		desc_th = 'เมื่อ HP ขุนพลต่ำกว่า 10% เพิ่มการดูดเลือด 5%',
		desc_idn = 'Ketika HP jenderal di bawah 10%, menambah 5% serap darah',
		desc_cn = '当将领生命低于10%，吸血提升5%',
		desc_kr = '장수 HP 10% 이하일 때 생명력 흡수 5% 증가',
		icon = 'city/adventure/random_tower/icon/icon_xx.png',
		iconBg = 'city/adventure/random_tower/icon/box_red.png',
		buffColor = 'effect',
		buffType = 4,
		passiveSkill = 80012,
		onlyOne = true,
		belongLib = 5
	},
	[1013] = {
		id = 1013,
		group = 102,
		desc = 'Chuyển toàn bộ hút máu thành lá chắn (giá trị = Máu tối đa × hút máu × 1.5), kéo dài 2 lượt',
		desc_vn = 'Chuyển toàn bộ hút máu thành lá chắn (giá trị = Máu tối đa × hút máu × 1.5), kéo dài 2 lượt',
		desc_en = 'Converts all life steal into a shield (value = Max HP × life steal × 1.5), lasts 2 turns',
		desc_th = 'แปลงการดูดเลือดทั้งหมดเป็นโล่ (ค่า = HP สูงสุด × การดูดเลือด × 1.5) คงอยู่ 2 รอบ',
		desc_idn = 'Mengubah seluruh serap darah menjadi perisai (nilai = HP maksimal × serap darah × 1.5), bertahan 2 giliran',
		desc_cn = '将全部吸血转化为护盾（数值=最大生命×吸血×1.5），持续2回合',
		desc_kr = '모든 생명력 흡수를 방어막으로 전환 (값 = 최대 HP × 생명력 흡수 × 1.5), 2턴 지속',
		icon = 'city/adventure/random_tower/icon/icon_zh.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80013,
		onlyOne = true,
		limit = 1,
		changeLib = 9
	},
	[1014] = {
		id = 1014,
		group = 200,
		desc = 'Miễn nhiễm hiệu ứng bất lợi khi có lá chắn',
		desc_vn = 'Miễn nhiễm hiệu ứng bất lợi khi có lá chắn',
		desc_en = 'Immune to negative effects while shielded',
		desc_th = 'ป้องกันการติดสถานะผิดปกติเมื่อมีโล่',
		desc_idn = 'Kebal terhadap efek negatif saat memiliki perisai',
		desc_cn = '有护盾时免疫不利效果',
		desc_kr = '방어막 존재 시 불리 효과 면역',
		icon = 'city/adventure/random_tower/icon/icon_qs.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80014,
		onlyOne = true,
		belongLib = 9
	},
	[1015] = {
		id = 1015,
		group = 200,
		desc = 'Khi bị tấn công, toàn đội có 5% cơ hội gây Câm Lặng kẻ địch trong 1 lượt',
		desc_vn = 'Khi bị tấn công, toàn đội có 5% cơ hội gây Câm Lặng kẻ địch trong 1 lượt',
		desc_en = 'When attacked, team has 5% chance to Silence enemies for 1 turn',
		desc_th = 'เมื่อถูกโจมตี ทีมทั้งหมดมีโอกาส 5% ทำให้ศัตรูเงียบ 1 รอบ',
		desc_idn = 'Saat diserang, seluruh tim memiliki 5% peluang membuat musuh diam selama 1 giliran',
		desc_cn = '受到攻击时，全队5%概率沉默敌人1回合',
		desc_kr = '공격 받을 때 전대가 5% 확률로 적을 1턴 침묵 상태로 만듦',
		icon = 'city/adventure/random_tower/icon/icon_kz.png',
		iconBg = 'city/adventure/random_tower/icon/box_green.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80015,
		onlyOne = true,
		belongLib = 7
	},
	[1016] = {
		id = 1016,
		group = 1000,
		desc = 'Vào trận nhận thêm 100 nộ',
		desc_vn = 'Vào trận nhận thêm 100 nộ',
		desc_en = 'Gain 100 Rage at battle start',
		desc_th = 'เมื่อเริ่มรบ รับพลังโกรธเพิ่ม 100',
		desc_idn = 'Mendapat tambahan 100 kemarahan saat memasuki pertandingan',
		desc_cn = '进入战斗时额外获得100怒气',
		desc_kr = '전투 시작 시 분노 100 추가',
		icon = 'city/adventure/random_tower/icon/icon_hn.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80016,
		onlyOne = true
	},
	[1017] = {
		id = 1017,
		group = 1000,
		desc = 'Hạ gục địch nhận thêm 100 nộ',
		desc_vn = 'Hạ gục địch nhận thêm 100 nộ',
		desc_en = 'Gain 100 Rage when defeating an enemy',
		desc_th = 'เมื่อล้มศัตรู รับพลังโกรธเพิ่ม 100',
		desc_idn = 'Mendapat tambahan 100 kemarahan saat mengalahkan musuh',
		desc_cn = '击杀敌人额外获得100怒气',
		desc_kr = '적 처치 시 분노 100 추가',
		icon = 'city/adventure/random_tower/icon/icon_hn.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80017,
		onlyOne = true
	},
	[1018] = {
		id = 1018,
		group = 1000,
		desc = 'Khi bị tấn công nhận thêm 100 nộ',
		desc_vn = 'Khi bị tấn công nhận thêm 100 nộ',
		desc_en = 'Gain 100 Rage when attacked',
		desc_th = 'เมื่อถูกโจมตี รับพลังโกรธเพิ่ม 100',
		desc_idn = 'Mendapat tambahan 100 kemarahan saat diserang',
		desc_cn = '受到攻击时额外获得100怒气',
		desc_kr = '공격 받을 때 분노 100 추가',
		icon = 'city/adventure/random_tower/icon/icon_hn.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80018,
		onlyOne = true
	},
	[1019] = {
		id = 1019,
		group = 101,
		desc = 'Toàn bộ tướng trong trận nhận thêm 5% Vật Công và Trí Lực theo hệ hiện tại (chỉ tính hệ chính)',
		desc_vn = 'Toàn bộ tướng trong trận nhận thêm 5% Vật Công và Trí Lực theo hệ hiện tại (chỉ tính hệ chính)',
		desc_en = 'All officers in battle gain 5% Physical Attack and Intelligence based on their primary affinity',
		desc_th = 'ขุนพลทั้งหมดในรบได้รับพลังโจมตีและปัญญาเพิ่ม 5% ตามธาตุปัจจุบัน (นับเฉพาะธาตุหลัก)',
		desc_idn = 'Semua jenderal dalam pertandingan mendapat tambahan 5% Serangan Fisik dan Kecerdasan sesuai elemen utama mereka',
		desc_cn = '战斗中所有将领根据当前主系获得额外5%物理攻击和智力加成',
		desc_kr = '전장 내 모든 장수가 자신의 주요 속성에 따라 물리 공격과 지력 5% 추가 획득 (주요 속성만 계산)',
		icon = 'city/adventure/random_tower/icon/icon_gj.png',
		iconBg = 'city/adventure/random_tower/icon/box_red.png',
		buffColor = 'effect',
		buffType = 4,
		passiveSkill = 80019,
		onlyOne = true,
		limit = 1,
		changeLib = 1
	},
	[1020] = {
		id = 1020,
		group = 1111,
		desc = 'Tướng thuộc hệ chỉ định (chỉ tính hệ chính) nhận thêm 20% Vật Công và Trí Lực (mỗi hệ chỉ có 1 buff)',
		desc_vn = 'Tướng thuộc hệ chỉ định (chỉ tính hệ chính) nhận thêm 20% Vật Công và Trí Lực (mỗi hệ chỉ có 1 buff)',
		desc_en = 'Officers of the specified affinity (primary only) gain an additional 20% Physical Attack and Intelligence (only one buff per affinity)',
		desc_th = 'ขุนพลที่อยู่ในธาตุกำหนด (นับเฉพาะธาตุหลัก) ได้รับพลังโจมตีและปัญญาเพิ่ม 20% (แต่ละธาตุมีบัฟได้แค่ 1 ครั้ง)',
		desc_idn = 'Jenderal dengan elemen tertentu (hanya hitung elemen utama) mendapat tambahan 20% Serangan Fisik dan Kecerdasan (setiap elemen hanya dapat satu buff)',
		desc_cn = '指定系（仅主系）将领额外获得20%物理攻击和智力加成（每系仅有一个增益）',
		desc_kr = '지정 속성 장수(주요 속성만 계산)는 물리 공격과 지력 20% 추가 획득 (속성별 1개의 버프만 적용)',
		icon = 'city/adventure/random_tower/icon/icon_gj.png',
		iconBg = 'city/adventure/random_tower/icon/box_red.png',
		buffColor = 'effect',
		buffType = 4,
		passiveSkill = 80020,
		onlyOne = true,
		belongLib = 2
	},
	[1021] = {
		id = 1021,
		group = 102,
		desc = 'Tướng có số hệ sử dụng nhiều nhất (chỉ tính hệ chính) nhận thêm 5% Vật Công và Trí Lực',
		desc_vn = 'Tướng có số hệ sử dụng nhiều nhất (chỉ tính hệ chính) nhận thêm 5% Vật Công và Trí Lực',
		desc_en = 'Officers with the most used affinity (primary only) gain an additional 5% Physical Attack and Intelligence',
		desc_th = 'ขุนพลที่มีธาตุใช้งานมากที่สุด (นับเฉพาะธาตุหลัก) ได้รับพลังโจมตีและปัญญาเพิ่ม 5%',
		desc_idn = 'Jenderal dengan jumlah elemen yang paling banyak digunakan (hanya hitung elemen utama) mendapat tambahan 5% Serangan Fisik dan Kecerdasan',
		desc_cn = '拥有最多系数（仅主系）的将领额外获得5%物理攻击和智力加成',
		desc_kr = '가장 많이 사용하는 속성 장수(주요 속성만 계산)는 물리 공격과 지력 5% 추가 획득',
		icon = 'city/adventure/random_tower/icon/icon_gj.png',
		iconBg = 'city/adventure/random_tower/icon/box_red.png',
		buffColor = 'effect',
		buffType = 4,
		passiveSkill = 80021,
		onlyOne = true,
		limit = 1,
		changeLib = 2
	},
	[1022] = {
		id = 1022,
		group = 1000,
		desc = 'Khi gây sát thương Trí Lực, tăng 5% tỷ lệ chí mạng trong 2 lượt, cộng dồn tối đa 4 lần và làm mới thời gian mỗi lần cộng dồn',
		desc_vn = 'Khi gây sát thương Trí Lực, tăng 5% tỷ lệ chí mạng trong 2 lượt, cộng dồn tối đa 4 lần và làm mới thời gian mỗi lần cộng dồn',
		desc_en = 'When dealing Intelligence damage, increase critical rate by 5% for 2 turns, stacks up to 4 times and refreshes duration on each stack',
		desc_th = 'เมื่อสร้างความเสียหายจากปัญญา เพิ่มอัตราคริติคอล 5% เป็นเวลา 2 รอบ สะสมสูงสุด 4 ครั้ง และรีเฟรชเวลาทุกครั้งที่สะสม',
		desc_idn = 'Ketika memberikan kerusakan Kecerdasan, meningkatkan 5% peluang kritikal selama 2 giliran, dapat menumpuk hingga 4 kali dan memperbarui durasi setiap kali menumpuk',
		desc_cn = '造成智力伤害时，提升5%暴击率，持续2回合，可叠加4次且刷新持续时间',
		desc_kr = '지력 피해 시 2턴간 치명타 확률 5% 증가, 최대 4회 중첩, 중첩 시마다 시간 갱신',
		icon = 'city/adventure/random_tower/icon/icon_bj.png',
		iconBg = 'city/adventure/random_tower/icon/box_orange.png',
		buffColor = 'effect',
		buffType = 4,
		passiveSkill = 80022,
		onlyOne = true
	},
	[1023] = {
		id = 1023,
		group = 1000,
		desc = 'Cứ mỗi 20% Vật Công tăng thêm, nhận thêm 10% Trí Lực',
		desc_vn = 'Cứ mỗi 20% Vật Công tăng thêm, nhận thêm 10% Trí Lực',
		desc_en = 'For every 20% Physical Attack increased, gain 10% Intelligence',
		desc_th = 'ทุก 20% ที่พลังโจมตีเพิ่มขึ้น รับปัญญาเพิ่มอีก 10%',
		desc_idn = 'Setiap kenaikan 20% Serangan Fisik, mendapat tambahan 10% Kecerdasan',
		desc_cn = '每增加20%物理攻击，额外获得10%智力',
		desc_kr = '물리 공격 20% 증가마다 지력 10% 추가 획득',
		icon = 'city/adventure/random_tower/icon/icon_zh.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80023,
		onlyOne = true,
		limit = 1
	},
	[1024] = {
		id = 1024,
		group = 1000,
		desc = 'Khi gây chí mạng, tăng 20% sát thương chí mạng và hồi 10% HP tối đa',
		desc_vn = 'Khi gây chí mạng, tăng 20% sát thương chí mạng và hồi 10% HP tối đa',
		desc_en = 'On critical hits, increase critical damage by 20% and heal 10% max HP',
		desc_th = 'เมื่อโจมตีคริติคอล เพิ่มความเสียหายคริติคอล 20% และฟื้นฟู HP สูงสุด 10%',
		desc_idn = 'Saat melakukan serangan kritikal, menambah 20% kerusakan kritikal dan memulihkan 10% HP maksimal',
		desc_cn = '造成暴击时，暴击伤害提升20%，并回复最大生命的10%',
		desc_kr = '치명타 시 치명타 피해 20% 증가 및 최대 HP 10% 회복',
		icon = 'city/adventure/random_tower/icon/icon_bj.png',
		iconBg = 'city/adventure/random_tower/icon/box_orange.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80024,
		onlyOne = true
	},
	[1025] = {
		id = 1025,
		group = 1000,
		desc = 'Cứ mỗi 20% Trí Lực tăng thêm, nhận thêm 10% Vật Công',
		desc_vn = 'Cứ mỗi 20% Trí Lực tăng thêm, nhận thêm 10% Vật Công',
		desc_en = 'For every 20% Intelligence increased, gain 10% Physical Attack',
		desc_th = 'ทุก 20% ที่ปัญญาเพิ่มขึ้น รับพลังโจมตีเพิ่มอีก 10%',
		desc_idn = 'Setiap kenaikan 20% Kecerdasan, mendapat tambahan 10% Serangan Fisik',
		desc_cn = '每增加20%智力，额外获得10%物理攻击',
		desc_kr = '지력 20% 증가마다 물리 공격 10% 추가 획득',
		icon = 'city/adventure/random_tower/icon/icon_bj.png',
		iconBg = 'city/adventure/random_tower/icon/box_orange.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80025,
		onlyOne = true
	},
	[1026] = {
		id = 1026,
		group = 200,
		desc = 'Khi tấn công địch đang bị hiệu ứng bất lợi, xóa hiệu ứng và gây thêm sát thương bằng 50% × số lượt hiệu ứng còn lại',
		desc_vn = 'Khi tấn công địch đang bị hiệu ứng bất lợi, xóa hiệu ứng và gây thêm sát thương bằng 50% × số lượt hiệu ứng còn lại',
		desc_en = 'When attacking enemies with negative effects, remove the effect and deal extra damage equal to 50% × remaining effect turns',
		desc_th = 'เมื่อโจมตีศัตรูที่ติดสถานะผิดปกติ ลบสถานะนั้นและสร้างความเสียหายเพิ่มเท่ากับ 50% × จำนวนรอบของสถานะที่เหลือ',
		desc_idn = 'Saat menyerang musuh yang terkena efek negatif, menghapus efek tersebut dan memberikan tambahan kerusakan sebesar 50% × jumlah giliran efek tersisa',
		desc_cn = '攻击处于不利状态的敌人时，移除其不利状态并造成额外伤害，伤害为50%×剩余回合数',
		desc_kr = '불리 효과가 걸린 적 공격 시 효과 제거 및 남은 턴 수 × 50% 피해 추가',
		icon = 'city/adventure/random_tower/icon/icon_zh.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80026,
		onlyOne = true,
		limit = 1,
		belongLib = 6
	},
	[1027] = {
		id = 1027,
		group = 1000,
		desc = 'Khi gây chí mạng, sát thương chí mạng tăng thêm 20% và hồi máu bằng 5% sát thương gây ra',
		desc_vn = 'Khi gây chí mạng, sát thương chí mạng tăng thêm 20% và hồi máu bằng 5% sát thương gây ra',
		desc_en = 'On critical hits, critical damage increases by 20% and heal HP equal to 5% of damage dealt',
		desc_th = 'เมื่อโจมตีคริติคอล ความเสียหายคริติคอลเพิ่มอีก 20% และฟื้นฟูเลือดเท่ากับ 5% ของความเสียหายที่สร้าง',
		desc_idn = 'Saat melakukan serangan kritikal, kerusakan kritikal bertambah 20% dan memulihkan HP sebesar 5% dari kerusakan yang diberikan',
		desc_cn = '造成暴击时，暴击伤害额外提升20%，并按造成伤害的5%回复生命',
		desc_kr = '치명타 시 치명타 피해 20% 추가 증가 및 피해량의 5% 회복',
		icon = 'city/adventure/random_tower/icon/icon_bj.png',
		iconBg = 'city/adventure/random_tower/icon/box_orange.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80027,
		onlyOne = true
	},
	[1028] = {
		id = 1028,
		group = 1000,
		desc = 'Ở lượt cuối mỗi trận, xóa 1 hiệu ứng bất lợi trên toàn bộ tướng phe ta',
		desc_vn = 'Ở lượt cuối mỗi trận, xóa 1 hiệu ứng bất lợi trên toàn bộ tướng phe ta',
		desc_en = 'On the last turn of each battle, removes 1 negative effect from all allied officers',
		desc_th = 'ในรอบสุดท้ายของการรบ ลบสถานะผิดปกติ 1 อันจากขุนพลฝ่ายเรา',
		desc_idn = 'Di giliran terakhir setiap pertandingan, menghapus 1 efek negatif dari semua jenderal pihak kita',
		desc_cn = '每场最后一回合，移除我方全体1个不利效果',
		desc_kr = '마지막 턴에 아군 전원 불리 효과 1개 해제',
		icon = 'city/adventure/random_tower/icon/icon_yybm1.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80028,
		onlyOne = true,
		limit = 1
	},
	[1029] = {
		id = 1029,
		group = 1111,
		desc = 'Tổng sát thương duy trì sẽ được cộng dồn và gây ngay khi tấn công mục tiêu đang chịu hiệu ứng đó',
		desc_vn = 'Tổng sát thương duy trì sẽ được cộng dồn và gây ngay khi tấn công mục tiêu đang chịu hiệu ứng đó',
		desc_en = 'Damage over time stacks and triggers immediately when attacking a target affected by it',
		desc_th = 'ความเสียหายแบบต่อเนื่องจะสะสมและเกิดขึ้นทันทีเมื่อโจมตีเป้าหมายที่ติดสถานะนั้น',
		desc_idn = 'Total kerusakan berkelanjutan akan menumpuk dan langsung diberikan saat menyerang target yang terkena efek tersebut',
		desc_cn = '持续伤害效果叠加，并在攻击处于该效果的目标时立即触发',
		desc_kr = '유지 피해 총합이 중첩되어 해당 불리 효과가 걸린 적 공격 시 즉시 피해 발생',
		icon = 'city/adventure/random_tower/icon/icon_yybm1.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80029,
		onlyOne = true,
		changeLib = 6
	},
	[1030] = {
		id = 1030,
		group = 200,
		desc = 'Mỗi đầu lượt, xóa toàn bộ hiệu ứng bất lợi. Mỗi hiệu ứng được xóa sẽ tăng 5% sát thương lượt đó',
		desc_vn = 'Mỗi đầu lượt, xóa toàn bộ hiệu ứng bất lợi. Mỗi hiệu ứng được xóa sẽ tăng 5% sát thương lượt đó',
		desc_en = 'At the start of each turn, removes all negative effects. Each removed effect increases damage this turn by 5%',
		desc_th = 'ทุกต้นรอบ ลบสถานะผิดปกติทั้งหมด สถานะที่ถูกลบแต่ละอันเพิ่มความเสียหายในรอบนั้น 5%',
		desc_idn = 'Di awal setiap giliran, menghapus semua efek negatif. Setiap efek yang dihapus meningkatkan kerusakan giliran itu sebesar 5%',
		desc_cn = '每回合开始时，移除全部不利效果。每移除一个效果，该回合伤害提升5%',
		desc_kr = '매 턴 시작 시 모든 불리 효과 해제, 해제한 효과 1개당 해당 턴 피해 5% 증가',
		icon = 'city/adventure/random_tower/icon/icon_nqhs.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80030,
		onlyOne = true,
		belongLib = 6
	},
	[1031] = {
		id = 1031,
		group = 103,
		desc = 'Mỗi tầng sát thương duy trì trên địch sẽ khiến địch nhận thêm 5% sát thương',
		desc_vn = 'Mỗi tầng sát thương duy trì trên địch sẽ khiến địch nhận thêm 5% sát thương',
		desc_en = 'Each stack of damage over time on enemies causes them to take 5% additional damage',
		desc_th = 'แต่ละชั้นของความเสียหายแบบต่อเนื่องบนศัตรู ทำให้ศัตรูได้รับความเสียหายเพิ่มขึ้น 5%',
		desc_idn = 'Setiap tumpukan kerusakan berkelanjutan pada musuh membuat musuh menerima tambahan 5% kerusakan',
		desc_cn = '每层持续伤害使敌人受到额外5%伤害',
		desc_kr = '적에게 쌓인 유지 피해 1스택당 적이 받는 피해 5% 증가',
		icon = 'city/adventure/random_tower/icon/icon_yybm1.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80031,
		onlyOne = true,
		changeLib = 6
	},
	[1032] = {
		id = 1032,
		group = 200,
		desc = 'Cứ mỗi 2% hút máu, tăng thêm 1% sát thương gây ra',
		desc_vn = 'Cứ mỗi 2% hút máu, tăng thêm 1% sát thương gây ra',
		desc_en = 'For every 2% life steal, increase damage dealt by 1%',
		desc_th = 'ทุก 2% ของการดูดเลือด เพิ่มความเสียหายที่สร้างขึ้นอีก 1%',
		desc_idn = 'Setiap 2% serap darah, menambah 1% kerusakan yang diberikan',
		desc_cn = '每2%吸血，额外提升1%造成的伤害',
		desc_kr = '생명력 흡수 2%마다 피해 1% 추가 증가',
		icon = 'city/adventure/random_tower/icon/icon_zh.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80032,
		onlyOne = true,
		belongLib = 6
	},
	[1033] = {
		id = 1033,
		group = 9999,
		desc = 'Phản đòn gây giảm 5% Vật Công và Trí Lực của địch trong 2 lượt, cộng dồn tối đa 4 lần',
		desc_vn = 'Phản đòn gây giảm 5% Vật Công và Trí Lực của địch trong 2 lượt, cộng dồn tối đa 4 lần',
		desc_en = 'Counterattack reduces enemy Physical Attack and Intelligence by 5% for 2 turns, stacks up to 4 times',
		desc_th = 'การโจมตีกลับทำให้ศัตรูลดพลังโจมตีและปัญญา 5% เป็นเวลา 2 รอบ สะสมสูงสุด 4 ครั้ง',
		desc_idn = 'Serangan balik mengurangi Serangan Fisik dan Kecerdasan musuh sebesar 5% selama 2 giliran, dapat menumpuk hingga 4 kali',
		desc_cn = '反击时使敌人物理攻击和智力降低5%，持续2回合，可叠加4次',
		desc_kr = '반격 시 적 물리 공격과 지력 5% 감소, 2턴간 최대 4중첩',
		icon = 'city/adventure/random_tower/icon/icon_js1.png',
		iconBg = 'city/adventure/random_tower/icon/box_red.png',
		buffColor = 'effect',
		buffType = 4,
		passiveSkill = 80033,
		onlyOne = true,
		belongLib = 7
	},
	[1034] = {
		id = 1034,
		group = 200,
		desc = 'Cứ mỗi 2% hút máu, tăng thêm 1% sát thương gây ra',
		desc_vn = 'Cứ mỗi 2% hút máu, tăng thêm 1% sát thương gây ra',
		desc_en = 'For every 2% life steal, increase damage dealt by 1%',
		desc_th = 'ทุก 2% ของการดูดเลือด เพิ่มความเสียหายที่สร้างขึ้นอีก 1%',
		desc_idn = 'Setiap 2% serap darah, menambah 1% kerusakan yang diberikan',
		desc_cn = '每2%吸血，额外提升1%造成的伤害',
		desc_kr = '생명력 흡수 2%마다 피해 1% 추가 증가',
		icon = 'city/adventure/random_tower/icon/icon_zh.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80034,
		onlyOne = true,
		belongLib = 8
	},
	[1035] = {
		id = 1035,
		group = 1111,
		desc = 'Khi gây sát thương, hiệu ứng hút máu nhận thêm phần sát thương cộng thêm',
		desc_vn = 'Khi gây sát thương, hiệu ứng hút máu nhận thêm phần sát thương cộng thêm',
		desc_en = 'When dealing damage, life steal effect gains extra damage based on added damage',
		desc_th = 'เมื่อสร้างความเสียหาย เอฟเฟกต์ดูดเลือดได้รับความเสียหายพิเศษเพิ่ม',
		desc_idn = 'Saat memberikan kerusakan, efek serap darah mendapatkan tambahan dari kerusakan ekstra',
		desc_cn = '造成伤害时，吸血效果额外按附加伤害部分计算',
		desc_kr = '피해 입힐 때 생명력 흡수 효과가 추가 피해에 적용',
		icon = 'city/adventure/random_tower/icon/icon_gj.png',
		iconBg = 'city/adventure/random_tower/icon/box_red.png',
		buffColor = 'effect',
		buffType = 4,
		passiveSkill = 80035,
		onlyOne = true,
		changeLib = 8
	},
	[1036] = {
		id = 1036,
		group = 200,
		desc = 'Mỗi đòn tấn công gây thêm sát thương bằng giá trị lá chắn hiện có',
		desc_vn = 'Mỗi đòn tấn công gây thêm sát thương bằng giá trị lá chắn hiện có',
		desc_en = 'Each attack deals extra damage equal to current shield value',
		desc_th = 'ทุกการโจมตีสร้างความเสียหายเพิ่มเท่ากับค่าของโล่ที่มีอยู่',
		desc_idn = 'Setiap serangan memberikan tambahan kerusakan sebesar nilai perisai saat ini',
		desc_cn = '每次攻击额外造成等同当前护盾值的伤害',
		desc_kr = '공격 시 현재 방어막 값만큼 추가 피해 부여',
		icon = 'city/adventure/random_tower/icon/icon_yybm1.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80036,
		onlyOne = true,
		belongLib = 9
	},
	[1037] = {
		id = 1037,
		group = 200,
		desc = 'Khi gây sát thương, lượng sát thương tăng theo lá chắn hiện có và xóa toàn bộ lá chắn hiện tại',
		desc_vn = 'Khi gây sát thương, lượng sát thương tăng theo lá chắn hiện có và xóa toàn bộ lá chắn hiện tại',
		desc_en = 'When dealing damage, damage increases based on current shield and then removes all shields',
		desc_th = 'เมื่อสร้างความเสียหาย จำนวนความเสียหายเพิ่มขึ้นตามโล่ที่มีและลบโล่ทั้งหมดที่มีอยู่',
		desc_idn = 'Saat memberikan kerusakan, jumlah kerusakan bertambah sesuai dengan perisai saat ini dan menghapus seluruh perisai yang ada',
		desc_cn = '造成伤害时伤害随护盾值提升，并清除当前所有护盾',
		desc_kr = '피해 입힐 때 현재 방어막 수치에 따라 피해 증가, 방어막 모두 해제',
		icon = 'city/adventure/random_tower/icon/icon_yybm1.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80037,
		onlyOne = true,
		belongLib = 9
	},
	[1038] = {
		id = 1038,
		group = 999,
		desc = 'Ở trận kế tiếp, tướng phe ta nhận 100% sát thương cộng thêm sau khi hạ gục địch, kéo dài 2 lượt',
		desc_vn = 'Ở trận kế tiếp, tướng phe ta nhận 100% sát thương cộng thêm sau khi hạ gục địch, kéo dài 2 lượt',
		desc_en = 'In the next battle, allied officers receive 100% of extra damage after defeating enemies, lasting 2 turns',
		desc_th = 'ในการรบถัดไป ขุนพลฝ่ายเราจะได้รับความเสียหายเพิ่มเติม 100% หลังจากล้มศัตรู คงอยู่ 2 รอบ',
		desc_idn = 'Dalam pertandingan berikutnya, jenderal pihak kita menerima 100% kerusakan tambahan setelah mengalahkan musuh, bertahan 2 giliran',
		desc_cn = '下一场战斗中，我方将领击杀敌人后，受到的额外伤害增加100%，持续2回合',
		desc_kr = '다음 전투에서 아군 장수가 적 처치 후 입히는 추가 피해 100% 증가, 2턴 지속',
		icon = 'city/adventure/random_tower/icon/icon_gj.png',
		iconBg = 'city/adventure/random_tower/icon/box_red.png',
		buffColor = 'effect',
		buffType = 4,
		passiveSkill = 80038,
		effectTimes = 1
	},
	[1039] = {
		id = 1039,
		group = 999,
		desc = 'Tướng phe ta ra đòn đầu tiên ở trận kế tiếp gây sát thương gấp đôi',
		desc_vn = 'Tướng phe ta ra đòn đầu tiên ở trận kế tiếp gây sát thương gấp đôi',
		desc_en = 'Allied officers\' first attack in the next battle deals double damage',
		desc_th = 'ขุนพลฝ่ายเราโจมตีครั้งแรกในรบถัดไป สร้างความเสียหายเป็นสองเท่า',
		desc_idn = 'Jenderal pihak kita yang menyerang pertama kali di pertandingan berikutnya memberikan kerusakan dua kali lipat',
		desc_cn = '下一场战斗中，我方将领首发攻击造成双倍伤害',
		desc_kr = '다음 전투에서 아군 장수가 첫 공격 시 피해 2배',
		icon = 'city/adventure/random_tower/icon/icon_gj.png',
		iconBg = 'city/adventure/random_tower/icon/box_red.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80039,
		effectTimes = 1
	},
	[1040] = {
		id = 1040,
		group = 9999,
		desc = 'Đầu trận kế tiếp, có 15% xác suất giảm một nửa HP hiện tại của địch (chỉ kích hoạt ở trận đầu, không áp dụng cho các trận sau)',
		desc_vn = 'Đầu trận kế tiếp, có 15% xác suất giảm một nửa HP hiện tại của địch (chỉ kích hoạt ở trận đầu, không áp dụng cho các trận sau)',
		desc_en = 'At the start of the next battle, 15% chance to halve enemy current HP (only triggers in first battle, not subsequent ones)',
		desc_th = 'ต้นรบถัดไป มีโอกาส 15% ที่จะลด HP ปัจจุบันของศัตรูลงครึ่งหนึ่ง (เปิดใช้งานเฉพาะรบแรก ไม่รวมรบถัดไป)',
		desc_idn = 'Di awal pertandingan berikutnya, ada 15% peluang mengurangi setengah HP musuh saat ini (hanya aktif pada pertandingan pertama, tidak berlaku di pertandingan berikutnya)',
		desc_cn = '下一场战斗开始时，15%概率使敌人当前生命值减半（仅首场战斗触发，之后不生效）',
		desc_kr = '다음 전투 시작 시 15% 확률로 적 현재 HP 절반 감소 (첫 전투에만 적용, 이후 전투 제외)',
		icon = 'city/adventure/random_tower/icon/icon_yybm1.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect',
		buffType = 4,
		passiveSkill = 80040,
		effectTimes = 1
	},
	[1041] = {
		id = 1041,
		group = 9999,
		desc = 'Đầu trận kế tiếp, mỗi kẻ địch có 50% xác suất ngất ngay lập tức (xác suất tính riêng từng địch)',
		desc_vn = 'Đầu trận kế tiếp, mỗi kẻ địch có 50% xác suất ngất ngay lập tức (xác suất tính riêng từng địch)',
		desc_en = 'At the start of the next battle, each enemy has 50% chance to be stunned immediately (chance calculated individually)',
		desc_th = 'ต้นรบถัดไป ศัตรูแต่ละตัวมีโอกาส 50% หมดสติทันที (คำนวณแยกแต่ละศัตรู)',
		desc_idn = 'Di awal pertandingan berikutnya, setiap musuh memiliki 50% peluang langsung pingsan (peluang dihitung secara terpisah untuk setiap musuh)',
		desc_cn = '下一场战斗开始时，每个敌人有50%概率直接昏迷（概率独立计算）',
		desc_kr = '다음 전투 시작 시 적 각자 50% 확률 즉시 기절 (확률은 적별로 계산)',
		icon = 'city/adventure/random_tower/icon/icon_yybm1.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80041,
		effectTimes = 1
	},
	[1042] = {
		id = 1042,
		group = 200,
		desc = 'Mỗi hệ tướng sử dụng tăng thêm 2% giảm sát thương',
		desc_vn = 'Mỗi hệ tướng sử dụng tăng thêm 2% giảm sát thương',
		desc_en = 'Each affinity of officers used increases damage reduction by 2%',
		desc_th = 'ทุกธาตุขุนพลที่ใช้ เพิ่มการลดความเสียหาย 2%',
		desc_idn = 'Setiap elemen jenderal yang digunakan menambah 2% pengurangan kerusakan',
		desc_cn = '每个系别将领使用时，额外增加2%伤害减免',
		desc_kr = '각 속성 장수 사용 시 피해 감소 2% 추가 획득',
		icon = 'city/adventure/random_tower/icon/icon_fy.png',
		iconBg = 'city/adventure/random_tower/icon/box_blue.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80042,
		onlyOne = true,
		belongLib = 1
	},
	[1043] = {
		id = 1043,
		group = 1111,
		desc = 'Miễn nhiễm hiệu ứng bất lợi của phe ta tăng 30%',
		desc_vn = 'Miễn nhiễm hiệu ứng bất lợi của phe ta tăng 30%',
		desc_en = 'Allied immunity to negative effects increased by 30%',
		desc_th = 'ป้องกันสถานะผิดปกติของฝ่ายเราเพิ่มขึ้น 30%',
		desc_idn = 'Imunitas efek negatif pihak kita meningkat 30%',
		desc_cn = '我方免疫不利效果提升30%',
		desc_kr = '아군 불리 효과 면역 30% 증가',
		icon = 'city/adventure/random_tower/icon/icon_qhzl.png',
		iconBg = 'city/adventure/random_tower/icon/box_green.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80043,
		onlyOne = true,
		belongLib = 6
	},
	[1044] = {
		id = 1044,
		group = 1000,
		desc = 'Mỗi 5% Phòng Ngự tăng thêm (do Thí Luyện Tướng, hiệu ứng đặc biệt, hiệu ứng tăng cường) tăng thêm 5% sát thương phản đòn',
		desc_vn = 'Mỗi 5% Phòng Ngự tăng thêm (do Thí Luyện Tướng, hiệu ứng đặc biệt, hiệu ứng tăng cường) tăng thêm 5% sát thương phản đòn',
		desc_en = 'For every 5% Defense increased (from Officer Trial, special effects, buffs), increase counterattack damage by 5%',
		desc_th = 'ทุก 5% การป้องกันที่เพิ่มขึ้น (จากการฝึกขุนพล, เอฟเฟกต์พิเศษ, เอฟเฟกต์เสริม) เพิ่มความเสียหายการโจมตีกลับ 5%',
		desc_idn = 'Setiap kenaikan 5% Pertahanan (dari Latihan Jenderal, efek khusus, efek peningkatan) menambah 5% kerusakan serangan balik',
		desc_cn = '每增加5%防御（通过试炼将、特殊效果、增益效果），反击伤害提升5%',
		desc_kr = '방어력 5% 증가(시련 장수, 특수 효과, 강화 효과 포함)마다 반격 피해 5% 증가',
		icon = 'city/adventure/random_tower/icon/icon_zh.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80044,
		onlyOne = true
	},
	[1045] = {
		id = 1045,
		group = 200,
		desc = 'Mỗi lần gây sát thương sẽ nhận được lá chắn bằng 5% sát thương nhận vào',
		desc_vn = 'Mỗi lần gây sát thương sẽ nhận được lá chắn bằng 5% sát thương nhận vào',
		desc_en = 'Each time damage is taken, gain a shield equal to 5% of damage received',
		desc_th = 'ทุกครั้งที่สร้างความเสียหาย จะได้รับโล่เท่ากับ 5% ของความเสียหายที่ได้รับ',
		desc_idn = 'Setiap kali memberikan kerusakan, mendapatkan perisai sebesar 5% dari kerusakan yang diterima',
		desc_cn = '造成伤害时获得相当于受到伤害5%的护盾',
		desc_kr = '피해 입을 때마다 입는 피해의 5%만큼 방어막 획득',
		icon = 'city/adventure/random_tower/icon/icon_zh.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80045,
		onlyOne = true,
		belongLib = 9
	},
	[1046] = {
		id = 1046,
		group = 200,
		desc = 'Mỗi điểm hồi máu vượt mức chuyển thành 2 điểm lá chắn',
		desc_vn = 'Mỗi điểm hồi máu vượt mức chuyển thành 2 điểm lá chắn',
		desc_en = 'Each point of overhealed HP converts to 2 points of shield',
		desc_th = 'ทุกจุดการฟื้นฟู HP เกินเปลี่ยนเป็นโล่ 2 จุด',
		desc_idn = 'Setiap poin pemulihan HP yang berlebih diubah menjadi 2 poin perisai',
		desc_cn = '超出治疗上限的生命回复转化为双倍护盾',
		desc_kr = '초과 치유량 1당 방어막 2 획득',
		icon = 'city/adventure/random_tower/icon/icon_zh.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80046,
		onlyOne = true,
		belongLib = 9
	},
	[1047] = {
		id = 1047,
		group = 9999,
		desc = 'Tướng phe ta đầu tiên bị hạ gục sẽ được hồi sinh ngay trước lượt tiếp theo, hồi 50% HP và 100% nộ (chỉ kích hoạt 1 lần)',
		desc_vn = 'Tướng phe ta đầu tiên bị hạ gục sẽ được hồi sinh ngay trước lượt tiếp theo, hồi 50% HP và 100% nộ (chỉ kích hoạt 1 lần)',
		desc_en = 'The first allied officer to be defeated immediately revives before the next turn with 50% HP and 100% Rage (only triggers once)',
		desc_th = 'ขุนพลฝ่ายเรา ตัวแรกที่ถูกล้มจะได้รับการชุบชีวิตทันที ก่อนรอบถัดไป ฟื้นฟู HP 50% และพลังโกรธ 100% (ทำงานได้ครั้งเดียว)',
		desc_idn = 'Jenderal pihak kita yang pertama kali gugur akan dihidupkan kembali tepat sebelum giliran berikutnya, memulihkan 50% HP dan 100% kemarahan (hanya aktif 1 kali)',
		desc_cn = '我方首位被击败的将领将在下一回合前复活，回复50%生命和100%怒气（仅触发1次）',
		desc_kr = '아군 첫 장수 사망 시 다음 턴 직전에 즉시 부활, HP 50%, 분노 100% 회복 (1회 한정)',
		icon = 'city/adventure/random_tower/icon/icon_hn.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80047,
		effectTimes = 1
	},
	[1048] = {
		id = 1048,
		group = 1111,
		desc = 'Tăng thời gian hiệu lực hiệu ứng bất lợi do phe ta gây ra thêm 1 lượt',
		desc_vn = 'Tăng thời gian hiệu lực hiệu ứng bất lợi do phe ta gây ra thêm 1 lượt',
		desc_en = 'Increases the duration of negative effects caused by allies by 1 turn',
		desc_th = 'เพิ่มระยะเวลาของสถานะผิดปกติที่ฝ่ายเราสร้างขึ้นอีก 1 รอบ',
		desc_idn = 'Memperpanjang durasi efek negatif yang diberikan pihak kita selama 1 giliran',
		desc_cn = '延长我方造成的不利效果持续时间1回合',
		desc_kr = '아군 불리 효과 지속 시간 1턴 증가',
		icon = 'city/adventure/random_tower/icon/icon_js1.png',
		iconBg = 'city/adventure/random_tower/icon/box_red.png',
		buffColor = 'effect',
		buffType = 4,
		passiveSkill = 80048,
		onlyOne = true,
		belongLib = 6
	},
	[1049] = {
		id = 1049,
		group = 200,
		desc = 'Phe ta tăng 20% khả năng gây hiệu ứng bất lợi',
		desc_vn = 'Phe ta tăng 20% khả năng gây hiệu ứng bất lợi',
		desc_en = 'Allies increase chance to inflict negative effects by 20%',
		desc_th = 'ฝ่ายเราเพิ่มโอกาสสร้างสถานะผิดปกติ 20%',
		desc_idn = 'Pihak kita meningkatkan 20% peluang memberikan efek negatif',
		desc_cn = '我方增加20%造成不利效果的概率',
		desc_kr = '아군 불리 효과 발동 확률 20% 증가',
		icon = 'city/adventure/random_tower/icon/icon_zh.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80049,
		onlyOne = true,
		belongLib = 6
	},
	[1050] = {
		id = 1050,
		group = 1111,
		desc = 'Tăng thời gian Trúng độc, Chảy máu, Thiêu đốt thêm 1 lượt',
		desc_vn = 'Tăng thời gian Trúng độc, Chảy máu, Thiêu đốt thêm 1 lượt',
		desc_en = 'Increases duration of Poison, Bleeding, and Burning by 1 turn',
		desc_th = 'เพิ่มระยะเวลาของสถานะพิษ, เลือดไหล, ไฟลุกลามอีก 1 รอบ',
		desc_idn = 'Memperpanjang durasi racun, pendarahan, dan terbakar selama 1 giliran',
		desc_cn = '延长中毒、流血、灼烧效果1回合',
		desc_kr = '중독, 출혈, 화상 지속 시간 1턴 증가',
		icon = 'city/adventure/random_tower/icon/icon_js1.png',
		iconBg = 'city/adventure/random_tower/icon/box_red.png',
		buffColor = 'effect',
		buffType = 4,
		passiveSkill = 80050,
		onlyOne = true,
		belongLib = 6
	},
	[1051] = {
		id = 1051,
		group = 200,
		desc = 'Tăng 10% sát thương gây ra bởi Trúng độc, Chảy máu, Thiêu đốt',
		desc_vn = 'Tăng 10% sát thương gây ra bởi Trúng độc, Chảy máu, Thiêu đốt',
		desc_en = 'Increases damage caused by Poison, Bleeding, and Burning by 10%',
		desc_th = 'เพิ่มความเสียหายจากสถานะพิษ, เลือดไหล, ไฟลุกลาม 10%',
		desc_idn = 'Menambah 10% kerusakan yang diberikan oleh racun, pendarahan, dan terbakar',
		desc_cn = '由中毒、流血、灼烧造成的伤害提升10%',
		desc_kr = '중독, 출혈, 화상 피해 10% 증가',
		icon = 'city/adventure/random_tower/icon/icon_js1.png',
		iconBg = 'city/adventure/random_tower/icon/box_red.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80051,
		onlyOne = true,
		belongLib = 6
	},
	[1052] = {
		id = 1052,
		group = 999,
		desc = 'Toàn bộ địch bị Câm Lặng 1 lượt ở trận kế tiếp',
		desc_vn = 'Toàn bộ địch bị Câm Lặng 1 lượt ở trận kế tiếp',
		desc_en = 'All enemies are Silenced for 1 turn in the next battle',
		desc_th = 'ศัตรูทั้งหมดติดสถานะเงียบ 1 รอบในการรบถัดไป',
		desc_idn = 'Semua musuh terkena diam selama 1 giliran di pertandingan berikutnya',
		desc_cn = '下一场战斗中，全体敌人沉默1回合',
		desc_kr = '다음 전투에서 모든 적 1턴 침묵',
		icon = 'city/adventure/random_tower/icon/icon_kz.png',
		iconBg = 'city/adventure/random_tower/icon/box_green.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80052,
		effectTimes = 1
	},
	[1053] = {
		id = 1053,
		group = 999,
		desc = 'Toàn bộ địch bị Ngủ 1 lượt ở trận kế tiếp',
		desc_vn = 'Toàn bộ địch bị Ngủ 1 lượt ở trận kế tiếp',
		desc_en = 'All enemies are put to Sleep for 1 turn in the next battle',
		desc_th = 'ศัตรูทั้งหมดติดสถานะหลับ 1 รอบในการรบถัดไป',
		desc_idn = 'Semua musuh tertidur selama 1 giliran di pertandingan berikutnya',
		desc_cn = '下一场战斗中，全体敌人沉睡1回合',
		desc_kr = '다음 전투에서 모든 적 1턴 수면',
		icon = 'city/adventure/random_tower/icon/icon_kz.png',
		iconBg = 'city/adventure/random_tower/icon/box_green.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80053,
		effectTimes = 1
	},
	[1054] = {
		id = 1054,
		group = 200,
		desc = 'Đầu trận, toàn bộ tướng nhận thêm 50 nộ theo hệ chính của mình',
		desc_vn = 'Đầu trận, toàn bộ tướng nhận thêm 50 nộ theo hệ chính của mình',
		desc_en = 'At battle start, all officers gain 50 Rage based on their primary affinity',
		desc_th = 'เมื่อต้นรบ ขุนพลทั้งหมดได้รับพลังโกรธเพิ่ม 50 ตามธาตุหลักของตน',
		desc_idn = 'Di awal pertandingan, semua jenderal mendapat tambahan 50 kemarahan sesuai elemen utama mereka',
		desc_cn = '战斗开始时，全体将领根据主系额外获得50怒气',
		desc_kr = '전투 시작 시 모든 장수 자신의 주요 속성에 따라 분노 50 추가',
		icon = 'city/adventure/random_tower/icon/icon_hn.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80054,
		onlyOne = true,
		belongLib = 1
	},
	[1055] = {
		id = 1055,
		group = 200,
		desc = 'Nếu đội hình có hơn 4 tướng cùng hệ chính, các tướng đó nhận thêm 500 nộ khi vào trận',
		desc_vn = 'Nếu đội hình có hơn 4 tướng cùng hệ chính, các tướng đó nhận thêm 500 nộ khi vào trận',
		desc_en = 'If the formation has more than 4 officers of the same primary affinity, those officers gain an additional 500 Rage at battle start',
		desc_th = 'หากทีมมีขุนพลมากกว่า 4 คนที่มีธาตุหลักเดียวกัน ขุนพลเหล่านั้นจะได้รับพลังโกรธเพิ่ม 500 เมื่อลงสนาม',
		desc_idn = 'Jika formasi memiliki lebih dari 4 jenderal dengan elemen utama yang sama, jenderal tersebut mendapat tambahan 500 kemarahan saat memasuki pertandingan',
		desc_cn = '队伍中有超过4名同主系将领时，这些将领进入战斗时额外获得500怒气',
		desc_kr = '전대에 동일 주요 속성 장수가 4명 초과 시 해당 장수들 전투 시작 시 분노 500 추가',
		icon = 'city/adventure/random_tower/icon/icon_hn.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80055,
		onlyOne = true,
		belongLib = 2
	},
	[1056] = {
		id = 1056,
		group = 200,
		desc = 'Khi HP giảm dưới 40%, tốc độ hồi nộ tăng gấp đôi',
		desc_vn = 'Khi HP giảm dưới 40%, tốc độ hồi nộ tăng gấp đôi',
		desc_en = 'When HP falls below 40%, Rage regeneration speed doubles',
		desc_th = 'เมื่อ HP ลดลงต่ำกว่า 40% ความเร็วการฟื้นฟูพลังโกรธเพิ่มเป็นสองเท่า',
		desc_idn = 'Ketika HP turun di bawah 40%, kecepatan pemulihan kemarahan meningkat dua kali lipat',
		desc_cn = '当生命值低于40%时，怒气回复速度加倍',
		desc_kr = 'HP 40% 이하일 때 분노 회복 속도 2배 증가',
		icon = 'city/adventure/random_tower/icon/icon_hn.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80056,
		onlyOne = true,
		belongLib = 5
	},
	[1057] = {
		id = 1057,
		group = 999,
		desc = 'Khi trận kế tiếp bắt đầu, toàn bộ tướng phe ta nhận trước 1000 nộ',
		desc_vn = 'Khi trận kế tiếp bắt đầu, toàn bộ tướng phe ta nhận trước 1000 nộ',
		desc_en = 'At the start of the next battle, all allied officers gain 1000 Rage instantly',
		desc_th = 'เมื่อต้นรบถัดไป ขุนพลฝ่ายเราได้รับพลังโกรธล่วงหน้า 1000',
		desc_idn = 'Saat pertandingan berikutnya dimulai, semua jenderal pihak kita langsung mendapatkan 1000 kemarahan',
		desc_cn = '下一场战斗开始时，我方全体将领提前获得1000怒气',
		desc_kr = '다음 전투 시작 시 아군 전원 분노 1000 선회복',
		icon = 'city/adventure/random_tower/icon/icon_hn.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80057,
		effectTimes = 1
	},
	[1100] = {
		id = 1100,
		group = 200,
		desc = 'Tướng phe ta nhận lá chắn bằng lượng HP đã mất khi bắt đầu trận, duy trì 2 lượt',
		desc_vn = 'Tướng phe ta nhận lá chắn bằng lượng HP đã mất khi bắt đầu trận, duy trì 2 lượt',
		desc_en = 'Allied officers gain a shield equal to lost HP at battle start, lasting 2 turns',
		desc_th = 'ขุนพลฝ่ายเราได้รับโล่เท่ากับ HP ที่เสียไปเมื่อเริ่มรบ คงอยู่ 2 รอบ',
		desc_idn = 'Jenderal pihak kita mendapatkan perisai sebesar HP yang hilang saat pertandingan dimulai, bertahan 2 giliran',
		desc_cn = '我方将领进入战斗时获得相当于已损失生命的护盾，持续2回合',
		desc_kr = '전투 시작 시 아군 장수가 잃은 HP만큼 방어막 획득, 2턴 지속',
		icon = 'city/adventure/random_tower/icon/icon_fy.png',
		iconBg = 'city/adventure/random_tower/icon/box_blue.png',
		buffColor = 'effect_lan',
		buffType = 4,
		passiveSkill = 80058,
		onlyOne = true,
		belongLib = 5
	},
	[1101] = {
		id = 1101,
		group = 200,
		desc = 'Mỗi lần mất 10% HP, tướng phe ta nhận thêm 8% giảm sát thương (chỉ áp dụng trong lượt đầu)',
		desc_vn = 'Mỗi lần mất 10% HP, tướng phe ta nhận thêm 8% giảm sát thương (chỉ áp dụng trong lượt đầu)',
		desc_en = 'For every 10% HP lost, allied officers gain 8% damage reduction (only applies on first turn)',
		desc_th = 'ทุกครั้งที่เสีย HP 10% ขุนพลฝ่ายเราได้รับการลดความเสียหายเพิ่ม 8% (ใช้ได้ในรอบแรกเท่านั้น)',
		desc_idn = 'Setiap kehilangan 10% HP, jenderal pihak kita mendapat tambahan 8% pengurangan kerusakan (hanya berlaku pada giliran pertama)',
		desc_cn = '每损失10%生命，我方将领额外获得8%减伤（仅限首回合）',
		desc_kr = 'HP 10% 당 아군 장수 피해 감소 8% 추가 획득 (첫 턴 한정)',
		icon = 'city/adventure/random_tower/icon/icon_fy.png',
		iconBg = 'city/adventure/random_tower/icon/box_blue.png',
		buffColor = 'effect_lan',
		buffType = 4,
		passiveSkill = 80059,
		onlyOne = true,
		belongLib = 5
	},
	[1102] = {
		id = 1102,
		group = 9999,
		desc = 'Đặt lại toàn bộ nộ của địch về 0 ở lượt thứ hai của trận',
		desc_vn = 'Đặt lại toàn bộ nộ của địch về 0 ở lượt thứ hai của trận',
		desc_en = 'Resets all enemy Rage to 0 on turn two of the battle',
		desc_th = 'รีเซ็ตพลังโกรธของศัตรูทั้งหมดเป็น 0 ในรอบที่สองของการรบ',
		desc_idn = 'Mengatur ulang seluruh kemarahan musuh menjadi 0 pada giliran kedua pertandingan',
		desc_cn = '战斗第二回合时，将敌人所有怒气重置为0',
		desc_kr = '전투 2턴째 적 분노 전부 0으로 초기화',
		icon = 'city/adventure/random_tower/icon/icon_hn.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffType = 4,
		passiveSkill = 80060,
		onlyOne = true,
		limit = 1
	},
	[1103] = {
		id = 1103,
		group = 1000,
		desc = 'Khi một tướng phe ta bị hạ gục, các tướng còn lại hồi 20% HP tối đa và 500 nộ',
		desc_vn = 'Khi một tướng phe ta bị hạ gục, các tướng còn lại hồi 20% HP tối đa và 500 nộ',
		desc_en = 'When an allied officer is defeated, remaining officers heal 20% max HP and gain 500 Rage',
		desc_th = 'เมื่อขุนพลฝ่ายเราถูกล้ม ขุนพลที่เหลือฟื้นฟู HP 20% สูงสุดและพลังโกรธ 500',
		desc_idn = 'Ketika satu jenderal pihak kita gugur, jenderal lain memulihkan 20% HP maksimal dan 500 kemarahan',
		desc_cn = '我方将领被击败时，其他将领回复20%最大生命值和500怒气',
		desc_kr = '아군 장수 사망 시 나머지 장수 HP 최대 20% 회복 및 분노 500 회복',
		icon = 'city/adventure/random_tower/icon/icon_qhzl.png',
		iconBg = 'city/adventure/random_tower/icon/box_green.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80061,
		onlyOne = true
	},
	[1104] = {
		id = 1104,
		group = 1000,
		desc = 'Cuối mỗi lượt, toàn bộ tướng phe ta hồi 15% lượng HP đã mất',
		desc_vn = 'Cuối mỗi lượt, toàn bộ tướng phe ta hồi 15% lượng HP đã mất',
		desc_en = 'At the end of each turn, all allied officers heal 15% of lost HP',
		desc_th = 'ทุกสิ้นรอบ ขุนพลฝ่ายเราฟื้นฟู 15% ของ HP ที่เสียไปทั้งหมด',
		desc_idn = 'Di akhir setiap giliran, semua jenderal pihak kita memulihkan 15% HP yang hilang',
		desc_cn = '每回合结束时，我方全体将领回复15%已损失生命值',
		desc_kr = '매 턴 종료 시 아군 전원 잃은 HP의 15% 회복',
		icon = 'city/adventure/random_tower/icon/icon_hx.png',
		iconBg = 'city/adventure/random_tower/icon/box_pink.png',
		buffType = 4,
		passiveSkill = 80062,
		onlyOne = true
	},
	[1105] = {
		id = 1105,
		group = 1000,
		desc = 'Có 10% xác suất hồi đầy nộ khi tướng phe ta tung chiêu cuối',
		desc_vn = 'Có 10% xác suất hồi đầy nộ khi tướng phe ta tung chiêu cuối',
		desc_en = '10% chance to fully restore Rage when an allied officer uses their ultimate',
		desc_th = 'มีโอกาส 10% ที่จะฟื้นฟูพลังโกรธเต็มเมื่อขุนพลฝ่ายเราใช้สกิลอัลติเมท',
		desc_idn = 'Ada 10% peluang untuk memulihkan kemarahan penuh saat jenderal pihak kita menggunakan serangan pamungkas',
		desc_cn = '将领释放大招时有10%概率怒气回复满',
		desc_kr = '아군 장수가 궁극기 사용 시 10% 확률로 분노 전부 회복',
		icon = 'city/adventure/random_tower/icon/icon_hn.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80063,
		onlyOne = true
	},
	[1106] = {
		id = 1106,
		group = 1000,
		desc = 'Mỗi lượt, đánh dấu 1 tướng địch ngẫu nhiên để tăng 50% sát thương nhận vào',
		desc_vn = 'Mỗi lượt, đánh dấu 1 tướng địch ngẫu nhiên để tăng 50% sát thương nhận vào',
		desc_en = 'Each turn, marks a random enemy to receive 50% increased damage taken',
		desc_th = 'ทุกรอบ ทำเครื่องหมายขุนพลศัตรู 1 คนแบบสุ่ม เพื่อเพิ่มความเสียหายที่ได้รับ 50%',
		desc_idn = 'Setiap giliran, menandai 1 jenderal musuh secara acak untuk menerima 50% kerusakan tambahan',
		desc_cn = '每回合标记一名随机敌将，使其承受伤害提升50%',
		desc_kr = '매 턴 무작위 적 1명 표시하여 받는 피해 50% 증가',
		icon = 'city/adventure/random_tower/icon/icon_js1.png',
		iconBg = 'city/adventure/random_tower/icon/box_red.png',
		buffColor = 'effect',
		buffType = 4,
		passiveSkill = 80064,
		onlyOne = true
	},
	[1107] = {
		id = 1107,
		group = 1000,
		desc = 'Khi tướng phe ta dùng chiêu cuối, tăng 20% Tốc độ trong 2 lượt',
		desc_vn = 'Khi tướng phe ta dùng chiêu cuối, tăng 20% Tốc độ trong 2 lượt',
		desc_en = 'When an allied officer uses their ultimate, increase Speed by 20% for 2 turns',
		desc_th = 'เมื่อขุนพลฝ่ายเราใช้สกิลอัลติเมท เพิ่มความเร็ว 20% เป็นเวลา 2 รอบ',
		desc_idn = 'Saat jenderal pihak kita menggunakan serangan pamungkas, menambah 20% kecepatan selama 2 giliran',
		desc_cn = '我方将领释放大招时，提升20%速度，持续2回合',
		desc_kr = '아군 장수 궁극기 사용 시 2턴간 속도 20% 증가',
		icon = 'city/adventure/random_tower/icon/icon_yybm.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80065,
		onlyOne = true
	},
	[1108] = {
		id = 1108,
		group = 1000,
		desc = 'Tăng hiệu quả tương khắc hệ, gây thêm 20% sát thương lên hệ bị khắc',
		desc_vn = 'Tăng hiệu quả tương khắc hệ, gây thêm 20% sát thương lên hệ bị khắc',
		desc_en = 'Increases affinity counter effectiveness, dealing 20% extra damage to countered affinities',
		desc_th = 'เพิ่มประสิทธิภาพการต้านธาตุ เพิ่มความเสียหายเพิ่มเติม 20% ต่อธาตุที่ถูกต้าน',
		desc_idn = 'Meningkatkan efektivitas saling mengalahkan elemen, memberikan tambahan 20% kerusakan terhadap elemen yang dikalahkan',
		desc_cn = '提高克制系效果，对被克制系造成额外20%伤害',
		desc_kr = '상성 효과 증가, 상성 대상에 추가 피해 20%',
		icon = 'city/adventure/random_tower/icon/icon_cxsh.png',
		iconBg = 'city/adventure/random_tower/icon/box_red.png',
		buffColor = 'effect',
		buffType = 4,
		passiveSkill = 80066,
		onlyOne = true
	},
	[1109] = {
		id = 1109,
		group = 1000,
		desc = 'Khi hạ gục tướng địch, phe ta hồi 10% HP và 200 nộ',
		desc_vn = 'Khi hạ gục tướng địch, phe ta hồi 10% HP và 200 nộ',
		desc_en = 'When an enemy officer is defeated, allies heal 10% HP and gain 200 Rage',
		desc_th = 'เมื่อฆ่าขุนพลศัตรู ฝ่ายเราฟื้นฟู HP 10% และพลังโกรธ 200',
		desc_idn = 'Saat mengalahkan jenderal musuh, pihak kita memulihkan 10% HP dan 200 kemarahan',
		desc_cn = '击杀敌将时，我方回复10%生命和200怒气',
		desc_kr = '적 장수 처치 시 아군 HP 10% 및 분노 200 회복',
		icon = 'city/adventure/random_tower/icon/icon_hx.png',
		iconBg = 'city/adventure/random_tower/icon/box_pink.png',
		buffType = 4,
		passiveSkill = 80067,
		onlyOne = true
	},
	[1110] = {
		id = 1110,
		group = 9999,
		desc = 'Tướng địch còn dưới 10% HP sau lượt cuối sẽ bị Thần Chết xử tử ngay lập tức',
		desc_vn = 'Tướng địch còn dưới 10% HP sau lượt cuối sẽ bị Thần Chết xử tử ngay lập tức',
		desc_en = 'Enemy officers below 10% HP after the last turn are instantly executed by Death',
		desc_th = 'ขุนพลศัตรูที่เหลือ HP ต่ำกว่า 10% หลังรอบสุดท้าย จะถูกพระเจ้าแห่งความตายประหารทันที',
		desc_idn = 'Jenderal musuh dengan HP di bawah 10% setelah giliran terakhir akan langsung dieksekusi oleh Malaikat Maut',
		desc_cn = '敌将生命低于10%且回合结束时，将被死神立即处决',
		desc_kr = '마지막 턴 후 HP 10% 이하 적은 즉시 사신 처형',
		icon = 'city/adventure/random_tower/icon/icon_js.png',
		iconBg = 'city/adventure/random_tower/icon/box_red.png',
		buffColor = 'effect',
		buffType = 4,
		passiveSkill = 80068,
		onlyOne = true,
		limit = 1
	},
	[1111] = {
		id = 1111,
		group = 1000,
		desc = 'Tăng 20% Tốc độ cho toàn đội ở lượt đầu trận',
		desc_vn = 'Tăng 20% Tốc độ cho toàn đội ở lượt đầu trận',
		desc_en = 'Increase team Speed by 20% on the first turn of battle',
		desc_th = 'เพิ่มความเร็ว 20% ให้ทีมทั้งหมดในรอบแรกของการรบ',
		desc_idn = 'Menambah 20% kecepatan seluruh tim pada giliran pertama pertandingan',
		desc_cn = '战斗首回合全队速度提升20%',
		desc_kr = '전투 첫 턴 전대 속도 20% 증가',
		icon = 'city/adventure/random_tower/icon/icon_yybm.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80069,
		onlyOne = true
	},
	[1112] = {
		id = 1112,
		group = 200,
		desc = 'Tăng x4% Tốc độ cho tướng theo hệ chỉ định (chỉ tính hệ chính)',
		desc_vn = 'Tăng x4% Tốc độ cho tướng theo hệ chỉ định (chỉ tính hệ chính)',
		desc_en = 'Increase Speed by x4% for officers of the specified affinity (primary only)',
		desc_th = 'เพิ่มความเร็ว 4% ให้ขุนพลตามธาตุกำหนด (นับเฉพาะธาตุหลัก)',
		desc_idn = 'Menambah x4% kecepatan untuk jenderal dengan elemen tertentu (hanya hitung elemen utama)',
		desc_cn = '指定系（仅主系）将领速度提升4%',
		desc_kr = '지정 속성 장수(주요 속성만 계산) 속도 4% 증가',
		icon = 'city/adventure/random_tower/icon/icon_yybm.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80070,
		onlyOne = true,
		belongLib = 1
	},
	[1113] = {
		id = 1113,
		group = 200,
		desc = 'Có 50% xác suất giúp hàng trước miễn nhiễm hiệu ứng bất lợi',
		desc_vn = 'Có 50% xác suất giúp hàng trước miễn nhiễm hiệu ứng bất lợi',
		desc_en = '50% chance to grant front row immunity to negative effects',
		desc_th = 'มีโอกาส 50% ช่วยให้แถวหน้าได้รับการป้องกันจากสถานะผิดปกติ',
		desc_idn = 'Ada 50% peluang melindungi barisan depan dari efek negatif',
		desc_cn = '50%概率使前排免疫不利效果',
		desc_kr = '50% 확률로 전열 아군 불리 효과 면역',
		icon = 'city/adventure/random_tower/icon/icon_fy.png',
		iconBg = 'city/adventure/random_tower/icon/box_blue.png',
		buffColor = 'effect_lan',
		buffType = 4,
		passiveSkill = 80071,
		onlyOne = true,
		belongLib = 7
	},
	[1114] = {
		id = 1114,
		group = 9999,
		desc = 'Tướng địch nhận giảm vĩnh viễn 10% HP tối đa',
		desc_vn = 'Tướng địch nhận giảm vĩnh viễn 10% HP tối đa',
		desc_en = 'Enemy officers permanently lose 10% max HP',
		desc_th = 'ขุนพลศัตรูได้รับการลด HP สูงสุดถาวร 10%',
		desc_idn = 'Jenderal musuh menerima pengurangan permanen 10% HP maksimal',
		desc_cn = '敌将永久减少10%最大生命值',
		desc_kr = '적 장수 최대 HP 영구 10% 감소',
		icon = 'city/adventure/random_tower/icon/icon_js.png',
		iconBg = 'city/adventure/random_tower/icon/box_red.png',
		buffColor = 'effect',
		buffType = 4,
		passiveSkill = 80072,
		onlyOne = true,
		limit = 1
	},
	[1115] = {
		id = 1115,
		group = 1000,
		desc = 'Khi một tướng phe ta bị hạ gục, sẽ gây sát thương bằng với lượng sát thương nhận vào lên kẻ đã hạ nó',
		desc_vn = 'Khi một tướng phe ta bị hạ gục, sẽ gây sát thương bằng với lượng sát thương nhận vào lên kẻ đã hạ nó',
		desc_en = 'When an allied officer is defeated, deal damage equal to damage received to the enemy that defeated them',
		desc_th = 'เมื่อขุนพลฝ่ายเราถูกล้ม จะสร้างความเสียหายเท่ากับความเสียหายที่ได้รับแก่ศัตรูที่ฆ่าขุนพลนั้น',
		desc_idn = 'Ketika satu jenderal pihak kita gugur, akan memberikan kerusakan sama dengan kerusakan yang diterimanya kepada musuh yang mengalahkannya',
		desc_cn = '我方将领被击败时，对击杀者造成等同所受伤害的伤害',
		desc_kr = '아군 장수 사망 시 자신을 죽인 적에게 받은 피해만큼 피해 부여',
		icon = 'city/adventure/random_tower/icon/icon_js1.png',
		iconBg = 'city/adventure/random_tower/icon/box_red.png',
		buffColor = 'effect',
		buffType = 4,
		passiveSkill = 80073,
		onlyOne = true
	},
	[1116] = {
		id = 1116,
		group = 9999,
		desc = 'Khi hạ gục địch, gây thêm 40% sát thương lên các mục tiêu xung quanh',
		desc_vn = 'Khi hạ gục địch, gây thêm 40% sát thương lên các mục tiêu xung quanh',
		desc_en = 'When defeating an enemy, deal an additional 40% damage to surrounding targets',
		desc_th = 'เมื่อฆ่าศัตรู สร้างความเสียหายเพิ่ม 40% แก่เป้าหมายรอบข้าง',
		desc_idn = 'Saat mengalahkan musuh, memberikan tambahan 40% kerusakan ke target di sekitarnya',
		desc_cn = '击杀敌人时，对周围目标造成额外40%伤害',
		desc_kr = '적 처치 시 주변 적에게 피해 40% 추가 가함',
		icon = 'city/adventure/random_tower/icon/icon_fs.png',
		iconBg = 'city/adventure/random_tower/icon/box_red.png',
		buffColor = 'effect',
		buffType = 4,
		passiveSkill = 80074,
		onlyOne = true
	},
	[1117] = {
		id = 1117,
		group = 1000,
		desc = 'Tướng phe ta có 20% xác suất cướp 50 nộ khi tấn công',
		desc_vn = 'Tướng phe ta có 20% xác suất cướp 50 nộ khi tấn công',
		desc_en = 'Allied officers have 20% chance to steal 50 Rage when attacking',
		desc_th = 'ขุนพลฝ่ายเรามีโอกาส 20% ที่จะขโมยพลังโกรธ 50 เมื่อโจมตี',
		desc_idn = 'Jenderal pihak kita memiliki 20% peluang mencuri 50 kemarahan saat menyerang',
		desc_cn = '我方将领攻击时有20%概率抢夺50怒气',
		desc_kr = '아군 장수 20% 확률로 공격 시 분노 50 탈취',
		icon = 'city/adventure/random_tower/icon/icon_hn.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80075,
		onlyOne = true
	},
	[1118] = {
		id = 1118,
		group = 1000,
		desc = 'Tỷ lệ hồi máu của phe ta tăng 30%',
		desc_vn = 'Tỷ lệ hồi máu của phe ta tăng 30%',
		desc_en = 'Allies\' healing rate increased by 30%',
		desc_th = 'อัตราการฟื้นฟูเลือดของฝ่ายเราเพิ่ม 30%',
		desc_idn = 'Tingkat pemulihan HP pihak kita meningkat 30%',
		desc_cn = '我方回血效果提升30%',
		desc_kr = '아군 회복 효과 30% 증가',
		icon = 'city/adventure/random_tower/icon/icon_qhzl.png',
		iconBg = 'city/adventure/random_tower/icon/box_green.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80076,
		onlyOne = true
	},
	[1119] = {
		id = 1119,
		group = 9999,
		desc = 'Sau khi dùng chiêu cuối, tướng phe ta xóa toàn bộ hiệu ứng có lợi của kẻ địch',
		desc_vn = 'Sau khi dùng chiêu cuối, tướng phe ta xóa toàn bộ hiệu ứng có lợi của kẻ địch',
		desc_en = 'After using an ultimate, allied officers remove all beneficial effects from enemies',
		desc_th = 'หลังใช้สกิลอัลติเมท ขุนพลฝ่ายเราจะลบสถานะบัฟทั้งหมดของศัตรู',
		desc_idn = 'Setelah menggunakan serangan pamungkas, jenderal pihak kita menghapus semua efek menguntungkan musuh',
		desc_cn = '释放大招后，我方将领移除敌方全部增益效果',
		desc_kr = '궁극기 사용 후 아군 장수가 적 모든 버프 해제',
		icon = 'city/adventure/random_tower/icon/icon_yybm1.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80077,
		onlyOne = true
	},
	[1120] = {
		id = 1120,
		group = 1000,
		desc = 'Sát thương chiêu cuối của phe ta tăng 30% ở lượt lẻ, giảm 15% ở lượt chẵn',
		desc_vn = 'Sát thương chiêu cuối của phe ta tăng 30% ở lượt lẻ, giảm 15% ở lượt chẵn',
		desc_en = 'Allies\' ultimate damage increases by 30% on odd turns, decreases by 15% on even turns',
		desc_th = 'ความเสียหายสกิลอัลติเมทของฝ่ายเราเพิ่ม 30% ในรอบคี่ ลด 15% ในรอบคู่',
		desc_idn = 'Kerusakan serangan pamungkas pihak kita meningkat 30% pada giliran ganjil, berkurang 15% pada giliran genap',
		desc_cn = '我方大招在奇数回合伤害提升30%，偶数回合伤害降低15%',
		desc_kr = '아군 궁극기 피해 홀수 턴 30% 증가, 짝수 턴 15% 감소',
		icon = 'city/adventure/random_tower/icon/icon_zh.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80078,
		onlyOne = true
	},
	[1121] = {
		id = 1121,
		group = 9999,
		desc = 'Cuối lượt cuối cùng, gây sát thương chuẩn bằng 20% Công của tướng phe ta có Công cao nhất lên toàn bộ kẻ địch',
		desc_vn = 'Cuối lượt cuối cùng, gây sát thương chuẩn bằng 20% Công của tướng phe ta có Công cao nhất lên toàn bộ kẻ địch',
		desc_en = 'At the end of the last turn, deal true damage equal to 20% of the highest Physical Attack among allied officers to all enemies',
		desc_th = 'ในรอบสุดท้าย สร้างความเสียหายธรรมดาเท่ากับ 20% ของพลังโจมตีของขุนพลฝ่ายเราที่มีพลังโจมตีสูงสุด แก่ศัตรูทั้งหมด',
		desc_idn = 'Di akhir giliran terakhir, memberikan kerusakan sejati sebesar 20% Serangan dari jenderal pihak kita dengan Serangan tertinggi ke semua musuh',
		desc_cn = '最后一回合结束时，对所有敌人造成相当于我方攻击最高将领20%攻击力的真实伤害',
		desc_kr = '마지막 턴 종료 시, 아군 중 가장 높은 공격력을 보유한 장수가 공격력 20%에 해당하는 고정 피해를 모든 적에게 입힘',
		icon = 'city/adventure/random_tower/icon/icon_fs.png',
		iconBg = 'city/adventure/random_tower/icon/box_red.png',
		buffColor = 'effect',
		buffType = 4,
		passiveSkill = 80079,
		onlyOne = true
	},
	[1122] = {
		id = 1122,
		group = 9999,
		desc = 'Khi vào trận, phe ta nhận lá chắn hấp thụ toàn bộ sát thương bằng với HP của tướng có HP cao nhất phe ta',
		desc_vn = 'Khi vào trận, phe ta nhận lá chắn hấp thụ toàn bộ sát thương bằng với HP của tướng có HP cao nhất phe ta',
		desc_en = 'Upon entering battle, allies gain a shield absorbing damage equal to the HP of the highest HP allied officer',
		desc_th = 'เมื่อลงสนาม ฝ่ายเราได้รับโล่ดูดซับความเสียหายเท่ากับ HP ของขุนพลฝ่ายเราที่มี HP สูงสุด',
		desc_idn = 'Saat memasuki pertandingan, pihak kita mendapatkan perisai yang menyerap seluruh kerusakan sebesar HP jenderal dengan HP tertinggi di pihak kita',
		desc_cn = '进入战斗时，我方获得一个护盾，吸收量等于我方最大生命最高将领的生命值',
		desc_kr = '전투 시작 시 아군 최대 HP가 가장 높은 장수 HP만큼 피해 흡수 방어막 획득',
		icon = 'city/adventure/random_tower/icon/icon_fy.png',
		iconBg = 'city/adventure/random_tower/icon/box_blue.png',
		buffColor = 'effect_lan',
		buffType = 4,
		passiveSkill = 80080,
		onlyOne = true
	},
	[1123] = {
		id = 1123,
		group = 200,
		desc = 'Gây gấp đôi sát thương Thiêu đốt từ phe ta',
		desc_vn = 'Gây gấp đôi sát thương Thiêu đốt từ phe ta',
		desc_en = 'Deals double Burning damage from allies',
		desc_th = 'สร้างความเสียหายจากสถานะไฟลุกลามของฝ่ายเราเป็นสองเท่า',
		desc_idn = 'Memberikan kerusakan terbakar dua kali lipat dari pihak kita',
		desc_cn = '我方灼烧伤害翻倍',
		desc_kr = '아군 화상 피해 2배 증가',
		icon = 'city/adventure/random_tower/icon/icon_cxsh.png',
		iconBg = 'city/adventure/random_tower/icon/box_red.png',
		buffColor = 'effect',
		buffType = 4,
		passiveSkill = 80081,
		onlyOne = true,
		belongLib = 6
	},
	[1124] = {
		id = 1124,
		group = 1000,
		desc = 'Toàn đội đầy nộ ở lượt đầu, nhưng chiêu cuối bị giảm 20% sát thương trong 1 lượt',
		desc_vn = 'Toàn đội đầy nộ ở lượt đầu, nhưng chiêu cuối bị giảm 20% sát thương trong 1 lượt',
		desc_en = 'Entire team starts with full Rage on first turn, but ultimate damage is reduced by 20% for 1 turn',
		desc_th = 'ทีมเต็มพลังโกรธในรอบแรก แต่สกิลอัลติเมทลดความเสียหายลง 20% ใน 1 รอบ',
		desc_idn = 'Seluruh tim memiliki kemarahan penuh pada giliran pertama, namun serangan pamungkas dikurangi 20% kerusakan selama 1 giliran',
		desc_cn = '首回合全队怒气满，但大招伤害降低20%，持续1回合',
		desc_kr = '전대 분노 전부 충전된 상태로 시작, 단 1턴 동안 궁극기 피해 20% 감소',
		icon = 'city/adventure/random_tower/icon/icon_zh.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80082,
		onlyOne = true
	},
	[2001] = {
		id = 2001,
		group = 101,
		desc = 'Vật Công và Trí Lực toàn đội +10%',
		desc_vn = 'Vật Công và Trí Lực toàn đội +10%',
		desc_en = 'Team Physical Attack and Intelligence +10%',
		desc_th = 'พลังโจมตีและปัญญาของทีมทั้งหมด +10%',
		desc_idn = 'Serangan Fisik dan Kecerdasan seluruh tim +10%',
		desc_cn = '全队物理攻击和智力 +10%',
		desc_kr = '전대의 물리 공격과 지력 +10%',
		weight = 1,
		icon = 'city/adventure/random_tower/icon/icon_gj.png',
		iconBg = 'city/adventure/random_tower/icon/box_red.png',
		buffColor = 'effect',
		attrType1 = 8,
		attrNum1 = '10%',
		attrType2 = 7,
		attrNum2 = '10%'
	},
	[2002] = {
		id = 2002,
		group = 102,
		desc = 'Vật Công và Trí Lực toàn đội +10%',
		desc_vn = 'Vật Công và Trí Lực toàn đội +10%',
		desc_en = 'Team Physical Attack and Intelligence +10%',
		desc_th = 'พลังโจมตีและปัญญาของทีมทั้งหมด +10%',
		desc_idn = 'Serangan Fisik dan Kecerdasan seluruh tim +10%',
		desc_cn = '全队物理攻击和智力 +10%',
		desc_kr = '전대의 물리 공격과 지력 +10%',
		weight = 1,
		icon = 'city/adventure/random_tower/icon/icon_gj.png',
		iconBg = 'city/adventure/random_tower/icon/box_red.png',
		buffColor = 'effect',
		attrType1 = 8,
		attrNum1 = '10%',
		attrType2 = 7,
		attrNum2 = '10%'
	},
	[2003] = {
		id = 2003,
		group = 103,
		desc = 'Vật Công và Trí Lực toàn đội +10%',
		desc_vn = 'Vật Công và Trí Lực toàn đội +10%',
		desc_en = 'Team Physical Attack and Intelligence +10%',
		desc_th = 'พลังโจมตีและปัญญาของทีมทั้งหมด +10%',
		desc_idn = 'Serangan Fisik dan Kecerdasan seluruh tim +10%',
		desc_cn = '全队物理攻击和智力 +10%',
		desc_kr = '전대의 물리 공격과 지력 +10%',
		weight = 1,
		icon = 'city/adventure/random_tower/icon/icon_gj.png',
		iconBg = 'city/adventure/random_tower/icon/box_red.png',
		buffColor = 'effect',
		attrType1 = 8,
		attrNum1 = '10%',
		attrType2 = 7,
		attrNum2 = '10%'
	},
	[2004] = {
		id = 2004,
		group = 104,
		desc = 'Vật Công và Trí Lực toàn đội +5%',
		desc_vn = 'Vật Công và Trí Lực toàn đội +5%',
		desc_en = 'Team Physical Attack and Intelligence +5%',
		desc_th = 'พลังโจมตีและปัญญาของทีมทั้งหมด +5%',
		desc_idn = 'Serangan Fisik dan Kecerdasan seluruh tim +5%',
		desc_cn = '全队物理攻击和智力 +5%',
		desc_kr = '전대의 물리 공격과 지력 +5%',
		weight = 1,
		icon = 'city/adventure/random_tower/icon/icon_gj.png',
		iconBg = 'city/adventure/random_tower/icon/box_red.png',
		buffColor = 'effect',
		attrType1 = 8,
		attrNum1 = '5%',
		attrType2 = 7,
		attrNum2 = '5%'
	},
	[2005] = {
		id = 2005,
		group = 105,
		desc = 'Vật Công và Trí Lực toàn đội +5%',
		desc_vn = 'Vật Công và Trí Lực toàn đội +5%',
		desc_en = 'Team Physical Attack and Intelligence +5%',
		desc_th = 'พลังโจมตีและปัญญาของทีมทั้งหมด +5%',
		desc_idn = 'Serangan Fisik dan Kecerdasan seluruh tim +5%',
		desc_cn = '全队物理攻击和智力 +5%',
		desc_kr = '전대의 물리 공격과 지력 +5%',
		weight = 1,
		icon = 'city/adventure/random_tower/icon/icon_gj.png',
		iconBg = 'city/adventure/random_tower/icon/box_red.png',
		buffColor = 'effect',
		attrType1 = 8,
		attrNum1 = '5%',
		attrType2 = 7,
		attrNum2 = '5%'
	},
	[2006] = {
		id = 2006,
		group = 106,
		desc = 'Vật Công và Trí Lực toàn đội +5%',
		desc_vn = 'Vật Công và Trí Lực toàn đội +5%',
		desc_en = 'Team Physical Attack and Intelligence +5%',
		desc_th = 'พลังโจมตีและปัญญาของทีมทั้งหมด +5%',
		desc_idn = 'Serangan Fisik dan Kecerdasan seluruh tim +5%',
		desc_cn = '全队物理攻击和智力 +5%',
		desc_kr = '전대의 물리 공격과 지력 +5%',
		weight = 1,
		icon = 'city/adventure/random_tower/icon/icon_gj.png',
		iconBg = 'city/adventure/random_tower/icon/box_red.png',
		buffColor = 'effect',
		attrType1 = 8,
		attrNum1 = '5%',
		attrType2 = 7,
		attrNum2 = '5%'
	},
	[2007] = {
		id = 2007,
		group = 107,
		desc = 'Vật Công và Trí Lực toàn đội +5%',
		desc_vn = 'Vật Công và Trí Lực toàn đội +5%',
		desc_en = 'Team Physical Attack and Intelligence +5%',
		desc_th = 'พลังโจมตีและปัญญาของทีมทั้งหมด +5%',
		desc_idn = 'Serangan Fisik dan Kecerdasan seluruh tim +5%',
		desc_cn = '全队物理攻击和智力 +5%',
		desc_kr = '전대의 물리 공격과 지력 +5%',
		weight = 1,
		icon = 'city/adventure/random_tower/icon/icon_gj.png',
		iconBg = 'city/adventure/random_tower/icon/box_red.png',
		buffColor = 'effect',
		attrType1 = 8,
		attrNum1 = '5%',
		attrType2 = 7,
		attrNum2 = '5%'
	},
	[2008] = {
		id = 2008,
		group = 108,
		desc = 'Vật Công và Trí Lực toàn đội +5%',
		desc_vn = 'Vật Công và Trí Lực toàn đội +5%',
		desc_en = 'Team Physical Attack and Intelligence +5%',
		desc_th = 'พลังโจมตีและปัญญาของทีมทั้งหมด +5%',
		desc_idn = 'Serangan Fisik dan Kecerdasan seluruh tim +5%',
		desc_cn = '全队物理攻击和智力 +5%',
		desc_kr = '전대의 물리 공격과 지력 +5%',
		weight = 1,
		icon = 'city/adventure/random_tower/icon/icon_gj.png',
		iconBg = 'city/adventure/random_tower/icon/box_red.png',
		buffColor = 'effect',
		attrType1 = 8,
		attrNum1 = '5%',
		attrType2 = 7,
		attrNum2 = '5%'
	},
	[2009] = {
		id = 2009,
		group = 109,
		desc = 'Vật Công và Trí Lực toàn đội +5%',
		desc_vn = 'Vật Công và Trí Lực toàn đội +5%',
		desc_en = 'Team Physical Attack and Intelligence +5%',
		desc_th = 'พลังโจมตีและปัญญาของทีมทั้งหมด +5%',
		desc_idn = 'Serangan Fisik dan Kecerdasan seluruh tim +5%',
		desc_cn = '全队物理攻击和智力 +5%',
		desc_kr = '전대의 물리 공격과 지력 +5%',
		weight = 1,
		icon = 'city/adventure/random_tower/icon/icon_gj.png',
		iconBg = 'city/adventure/random_tower/icon/box_red.png',
		buffColor = 'effect',
		attrType1 = 8,
		attrNum1 = '5%',
		attrType2 = 7,
		attrNum2 = '5%'
	},
	[2010] = {
		id = 2010,
		group = 110,
		desc = 'Vật Công và Trí Lực toàn đội +5%',
		desc_vn = 'Vật Công và Trí Lực toàn đội +5%',
		desc_en = 'Team Physical Attack and Intelligence +5%',
		desc_th = 'พลังโจมตีและปัญญาของทีมทั้งหมด +5%',
		desc_idn = 'Serangan Fisik dan Kecerdasan seluruh tim +5%',
		desc_cn = '全队物理攻击和智力 +5%',
		desc_kr = '전대의 물리 공격과 지력 +5%',
		weight = 1,
		icon = 'city/adventure/random_tower/icon/icon_gj.png',
		iconBg = 'city/adventure/random_tower/icon/box_red.png',
		buffColor = 'effect',
		attrType1 = 8,
		attrNum1 = '5%',
		attrType2 = 7,
		attrNum2 = '5%'
	},
	[2011] = {
		id = 2011,
		group = 200,
		desc = 'Vật Công và Trí Lực toàn đội +5%',
		desc_vn = 'Vật Công và Trí Lực toàn đội +5%',
		desc_en = 'Team Physical Attack and Intelligence +5%',
		desc_th = 'พลังโจมตีและปัญญาของทีมทั้งหมด +5%',
		desc_idn = 'Serangan Fisik dan Kecerdasan seluruh tim +5%',
		desc_cn = '全队物理攻击和智力 +5%',
		desc_kr = '전대의 물리 공격과 지력 +5%',
		weight = 1,
		icon = 'city/adventure/random_tower/icon/icon_gj.png',
		iconBg = 'city/adventure/random_tower/icon/box_red.png',
		buffColor = 'effect',
		attrType1 = 8,
		attrNum1 = '5%',
		attrType2 = 7,
		attrNum2 = '5%'
	},
	[2012] = {
		id = 2012,
		group = 1111,
		desc = 'Vật Công và Trí Lực toàn đội +5%',
		desc_vn = 'Vật Công và Trí Lực toàn đội +5%',
		desc_en = 'Team Physical Attack and Intelligence +5%',
		desc_th = 'พลังโจมตีและปัญญาของทีมทั้งหมด +5%',
		desc_idn = 'Serangan Fisik dan Kecerdasan seluruh tim +5%',
		desc_cn = '全队物理攻击和智力 +5%',
		desc_kr = '전대의 물리 공격과 지력 +5%',
		weight = 1,
		icon = 'city/adventure/random_tower/icon/icon_gj.png',
		iconBg = 'city/adventure/random_tower/icon/box_red.png',
		buffColor = 'effect',
		attrType1 = 8,
		attrNum1 = '5%',
		attrType2 = 7,
		attrNum2 = '5%'
	},
	[2013] = {
		id = 2013,
		group = 1000,
		desc = 'Vật Công và Trí Lực toàn đội +5%',
		desc_vn = 'Vật Công và Trí Lực toàn đội +5%',
		desc_en = 'Team Physical Attack and Intelligence +5%',
		desc_th = 'พลังโจมตีและปัญญาของทีมทั้งหมด +5%',
		desc_idn = 'Serangan Fisik dan Kecerdasan seluruh tim +5%',
		desc_cn = '全队物理攻击和智力 +5%',
		desc_kr = '전대의 물리 공격과 지력 +5%',
		weight = 1,
		icon = 'city/adventure/random_tower/icon/icon_gj.png',
		iconBg = 'city/adventure/random_tower/icon/box_red.png',
		buffColor = 'effect',
		attrType1 = 8,
		attrNum1 = '5%',
		attrType2 = 7,
		attrNum2 = '5%'
	},
	[2014] = {
		id = 2014,
		group = 9999,
		desc = 'Vật Công và Trí Lực toàn đội +5%',
		desc_vn = 'Vật Công và Trí Lực toàn đội +5%',
		desc_en = 'Team Physical Attack and Intelligence +5%',
		desc_th = 'พลังโจมตีและปัญญาของทีมทั้งหมด +5%',
		desc_idn = 'Serangan Fisik dan Kecerdasan seluruh tim +5%',
		desc_cn = '全队物理攻击和智力 +5%',
		desc_kr = '전대의 물리 공격과 지력 +5%',
		weight = 1,
		icon = 'city/adventure/random_tower/icon/icon_gj.png',
		iconBg = 'city/adventure/random_tower/icon/box_red.png',
		buffColor = 'effect',
		attrType1 = 8,
		attrNum1 = '5%',
		attrType2 = 7,
		attrNum2 = '5%'
	},
	[2015] = {
		id = 2015,
		group = 999,
		desc = 'Vật Công và Trí Lực toàn đội +5%',
		desc_vn = 'Vật Công và Trí Lực toàn đội +5%',
		desc_en = 'Team Physical Attack and Intelligence +5%',
		desc_th = 'พลังโจมตีและปัญญาของทีมทั้งหมด +5%',
		desc_idn = 'Serangan Fisik dan Kecerdasan seluruh tim +5%',
		desc_cn = '全队物理攻击和智力 +5%',
		desc_kr = '전대의 물리 공격과 지력 +5%',
		weight = 1,
		icon = 'city/adventure/random_tower/icon/icon_gj.png',
		iconBg = 'city/adventure/random_tower/icon/box_red.png',
		buffColor = 'effect',
		attrType1 = 8,
		attrNum1 = '5%',
		attrType2 = 7,
		attrNum2 = '5%'
	},
	[2221] = {
		id = 2221,
		group = 12221,
		desc = '【Hỏa Chi Lực】: Mỗi lần gây sát thương sẽ giảm 8% Vật Công và Trí Lực của địch, tối đa bằng 10% Vật Công và Trí Lực bản thân (【Tà Chi Lực】 tăng cường hiệu ứng này)',
		desc_vn = '【Hỏa Chi Lực】: Mỗi lần gây sát thương sẽ giảm 8% Vật Công và Trí Lực của địch, tối đa bằng 10% Vật Công và Trí Lực bản thân (【Tà Chi Lực】 tăng cường hiệu ứng này)',
		desc_en = '【Fire Power】: Each time damage is dealt, reduces enemy Physical Attack and Intelligence by 8%, up to 10% of own Physical Attack and Intelligence (【Dark Power】 enhances this effect)',
		desc_th = '【พลังไฟ】: ทุกครั้งที่สร้างความเสียหาย ลดพลังโจมตีและปัญญาของศัตรู 8% สูงสุดเท่ากับ 10% ของพลังโจมตีและปัญญาของตน (【พลังอธรรม】เสริมประสิทธิภาพนี้)',
		desc_idn = '【Hỏa Chi Lực】: Setiap kali memberikan kerusakan, mengurangi 8% Serangan Fisik dan Kecerdasan musuh, maksimum 10% Serangan Fisik dan Kecerdasan diri (【Tà Chi Lực】 memperkuat efek ini)',
		desc_cn = '【火之力】：每次造成伤害时，减少敌人物理攻击和智力8%，最多减少到自身物理攻击和智力的10%（【邪之力】强化该效果）',
		desc_kr = '【화기력】: 피해 시 적 물리 공격과 지력 8% 감소, 최대 자신 물리 공격과 지력 10%까지 감소 (【사기력】이 효과 강화)',
		icon = 'city/adventure/random_tower/icon/icon_cxsh.png',
		iconBg = 'city/adventure/random_tower/icon/box_red.png',
		buffColor = 'effect',
		buffType = 4,
		passiveSkill = 80083
	},
	[2222] = {
		id = 2222,
		group = 2221,
		desc = '【Thủy Chi Lực】: Mỗi lần gây sát thương sẽ giảm 8% Vật Phòng và Pháp Phòng của địch, tối đa bằng 10% Vật Phòng và Pháp Phòng bản thân (【Tà Chi Lực】 tăng cường hiệu ứng này)',
		desc_vn = '【Thủy Chi Lực】: Mỗi lần gây sát thương sẽ giảm 8% Vật Phòng và Pháp Phòng của địch, tối đa bằng 10% Vật Phòng và Pháp Phòng bản thân (【Tà Chi Lực】 tăng cường hiệu ứng này)',
		desc_en = '【Water Power】: Each time damage is dealt, reduces enemy Physical and Magical Defense by 8%, up to 10% of own Physical and Magical Defense (【Dark Power】 enhances this effect)',
		desc_th = '【พลังน้ำ】: ทุกครั้งที่สร้างความเสียหาย ลดพลังป้องกันกายภาพและเวทย์ของศัตรู 8% สูงสุดเท่ากับ 10% ของพลังป้องกันกายภาพและเวทย์ของตน (【พลังอธรรม】เสริมประสิทธิภาพนี้)',
		desc_idn = '【Thủy Chi Lực】: Setiap kali memberikan kerusakan, mengurangi 8% Pertahanan Fisik dan Pertahanan Magis musuh, maksimum 10% Pertahanan Fisik dan Pertahanan Magis diri (【Tà Chi Lực】 memperkuat efek ini)',
		desc_cn = '【水之力】：每次造成伤害时，减少敌人物理防御和法术防御8%，最多减少到自身物理防御和法术防御的10%（【邪之力】强化该效果）',
		desc_kr = '【수기력】: 피해 시 적 물리 방어와 법술 방어 8% 감소, 최대 자신 물리 방어와 법술 방어 10%까지 감소 (【사기력】이 효과 강화)',
		icon = 'city/adventure/random_tower/icon/icon_gj.png',
		iconBg = 'city/adventure/random_tower/icon/box_red.png',
		buffColor = 'effect',
		buffType = 4,
		passiveSkill = 80084
	},
	[2223] = {
		id = 2223,
		group = 2221,
		desc = '【Tà Chi Lực】: Cường hóa hiệu ứng 【Hỏa Chi Lực, Thủy Chi Lực】: Mỗi đòn đánh sẽ cướp Vật Công, Trí Lực, Vật Phòng và Pháp Phòng từ mục tiêu. Khi sở hữu đồng thời 【Tà Chi Lực, Hỏa Chi Lực, Thủy Chi Lực】, toàn đội nhận thêm 10% Hút Máu',
		desc_vn = '【Tà Chi Lực】: Cường hóa hiệu ứng 【Hỏa Chi Lực, Thủy Chi Lực】: Mỗi đòn đánh sẽ cướp Vật Công, Trí Lực, Vật Phòng và Pháp Phòng từ mục tiêu. Khi sở hữu đồng thời 【Tà Chi Lực, Hỏa Chi Lực, Thủy Chi Lực】, toàn đội nhận thêm 10% Hút Máu',
		desc_en = '【Dark Power】: Enhances effects of 【Fire Power, Water Power】: Each attack steals Physical Attack, Intelligence, Physical Defense, and Magical Defense from the target. When possessing 【Dark Power, Fire Power, Water Power】 simultaneously, the entire team gains an additional 10% Life Steal',
		desc_th = '【พลังอธรรม】: เสริมประสิทธิภาพ 【พลังไฟ, พลังน้ำ】: ทุกการโจมตีจะขโมยพลังโจมตี, ปัญญา, พลังป้องกันกายภาพ และพลังป้องกันเวทย์จากเป้าหมาย เมื่อมีครบ 【พลังอธรรม, พลังไฟ, พลังน้ำ】 ทีมทั้งหมดได้รับการดูดเลือดเพิ่ม 10%',
		desc_idn = '【Tà Chi Lực】: Memperkuat efek 【Hỏa Chi Lực, Thủy Chi Lực】: Setiap serangan mencuri Serangan Fisik, Kecerdasan, Pertahanan Fisik, dan Pertahanan Magis dari target. Saat memiliki 【Tà Chi Lực, Hỏa Chi Lực, Thủy Chi Lực】 sekaligus, seluruh tim menerima tambahan 10% Serap Darah',
		desc_cn = '【邪之力】：强化【火之力，水之力】效果：每次攻击盗取目标的物理攻击、智力、物理防御和法术防御；同时拥有【邪之力，火之力，水之力】时，全队额外获得10%吸血',
		desc_kr = '【사기력】: 【화기력, 수기력】 효과 강화: 공격 시 적에게서 물리 공격, 지력, 물리 방어, 법술 방어를 훔침. 【사기력, 화기력, 수기력】 모두 보유 시 전대 생명력 흡수 10% 추가 획득',
		icon = 'city/adventure/random_tower/icon/icon_xx.png',
		iconBg = 'city/adventure/random_tower/icon/box_red.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80085
	},
	[2224] = {
		id = 2224,
		group = 12222,
		desc = '【Thái Dương Thạch】: Hồi 200 Nộ trước khi hành động (Nếu có 【Thái Dương - Nguyệt Thạch】, tăng 30% tốc độ hồi Nộ)',
		desc_vn = '【Thái Dương Thạch】: Hồi 200 Nộ trước khi hành động (Nếu có 【Thái Dương - Nguyệt Thạch】, tăng 30% tốc độ hồi Nộ)',
		desc_en = '【Solar Stone】: Restores 200 Rage before acting (If possessing 【Solar - Lunar Stone】, increases Rage regeneration speed by 30%)',
		desc_th = '【หินแสงอาทิตย์】: ฟื้นฟูพลังโกรธ 200 ก่อนเริ่มกระทำ (ถ้ามี 【หินแสงอาทิตย์-พระจันทร์】 เพิ่มความเร็วการฟื้นฟูพลังโกรธ 30%)',
		desc_idn = '【Thái Dương Thạch】: Memulihkan 200 Kemarahan sebelum bertindak (Jika memiliki 【Thái Dương - Nguyệt Thạch】, meningkatkan kecepatan pemulihan Kemarahan sebesar 30%)',
		desc_cn = '【太阳石】：行动前回复200怒气（拥有【太阴-月石】时，怒气回复速度提升30%）',
		desc_kr = '【태양석】: 행동 전 분노 200 회복 (【태양-월석】 보유 시 분노 회복 속도 30% 증가)',
		icon = 'city/adventure/random_tower/icon/icon_qhzl.png',
		iconBg = 'city/adventure/random_tower/icon/box_green.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80086
	},
	[2225] = {
		id = 2225,
		group = 2222,
		desc = '【Nguyệt Thạch】: Mỗi vòng lớn hồi 10% HP đã mất (Nếu có 【Thái Dương - Nguyệt Thạch】, tăng 30% tốc độ hồi Nộ)',
		desc_vn = '【Nguyệt Thạch】: Mỗi vòng lớn hồi 10% HP đã mất (Nếu có 【Thái Dương - Nguyệt Thạch】, tăng 30% tốc độ hồi Nộ)',
		desc_en = '【Lunar Stone】: Restores 10% lost HP each major round (If possessing 【Solar - Lunar Stone】, increases Rage regeneration speed by 30%)',
		desc_th = '【หินพระจันทร์】: ทุกรอบใหญ่ ฟื้นฟู HP ที่เสียไป 10% (ถ้ามี 【หินแสงอาทิตย์-พระจันทร์】 เพิ่มความเร็วการฟื้นฟูพลังโกรธ 30%)',
		desc_idn = '【Nguyệt Thạch】: Setiap babak besar memulihkan 10% HP yang hilang (Jika memiliki 【Thái Dương - Nguyệt Thạch】, meningkatkan kecepatan pemulihan Kemarahan sebesar 30%)',
		desc_cn = '【月石】：每大回合回复已损失生命10%（拥有【太阴-月石】时，怒气回复速度提升30%）',
		desc_kr = '【월석】: 매 큰 라운드마다 잃은 HP 10% 회복 (【태양-월석】 보유 시 분노 회복 속도 30% 증가)',
		icon = 'city/adventure/random_tower/icon/icon_qhzl.png',
		iconBg = 'city/adventure/random_tower/icon/box_green.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80087
	},
	[2226] = {
		id = 2226,
		group = 12223,
		desc = '【Phẫn Nộ】: Khi bắt đầu trận, toàn đội nhận 100 Nộ (Mỗi hiệu ứng 【Phẫn Uất, Nộ Kích, Hút Nộ, Khiêu Khích, Phẫn Nộ】 sẽ tăng thêm 100 Nộ)',
		desc_vn = '【Phẫn Nộ】: Khi bắt đầu trận, toàn đội nhận 100 Nộ (Mỗi hiệu ứng 【Phẫn Uất, Nộ Kích, Hút Nộ, Khiêu Khích, Phẫn Nộ】 sẽ tăng thêm 100 Nộ)',
		desc_en = '【Rage】: At battle start, the entire team gains 100 Rage (Each effect from 【Wrath, Rage Boost, Rage Steal, Taunt, Rage】 adds 100 Rage)',
		desc_th = '【ความโกรธ】: เมื่อเริ่มการรบ ทีมทั้งหมดได้รับพลังโกรธ 100 (แต่ละเอฟเฟกต์ 【ความโกรธแค้น, การโจมตีพลังโกรธ, ดูดพลังโกรธ, ยั่วยุ, ความโกรธ】 เพิ่มพลังโกรธอีก 100)',
		desc_idn = '【Phẫn Nộ】: Saat memulai pertandingan, seluruh tim menerima 100 Kemarahan (Setiap efek 【Phẫn Uất, Nộ Kích, Hút Nộ, Khiêu Khích, Phẫn Nộ】 menambah 100 Kemarahan)',
		desc_cn = '【愤怒】：战斗开始时，全队获得100怒气（每个【愤慨，怒击，吸怒，挑衅，愤怒】效果额外增加100怒气）',
		desc_kr = '【분노】: 전투 시작 시 전대 분노 100 추가 (각 【분개, 분격, 흡분, 도발, 분노】 효과마다 분노 100 추가)',
		icon = 'city/adventure/random_tower/icon/icon_nqhs.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80088
	},
	[2227] = {
		id = 2227,
		group = 2223,
		desc = '【Phẫn Uất】: Khi nhận sát thương, nhận 100 Nộ (Tăng Nộ cho hiệu ứng 【Cuồng Nộ】)',
		desc_vn = '【Phẫn Uất】: Khi nhận sát thương, nhận 100 Nộ (Tăng Nộ cho hiệu ứng 【Cuồng Nộ】)',
		desc_en = '【Wrath】: When taking damage, gain 100 Rage (Increases Rage for 【Frenzy】 effect)',
		desc_th = '【ความโกรธแค้น】: เมื่อได้รับความเสียหาย รับพลังโกรธ 100 (เพิ่มพลังโกรธสำหรับเอฟเฟกต์ 【ความโกรธจัด】)',
		desc_idn = '【Phẫn Uất】: Saat menerima kerusakan, menerima 100 Kemarahan (Menambah Kemarahan untuk efek 【Cuồng Nộ】)',
		desc_cn = '【愤慨】：受到伤害时获得100怒气（增加【狂怒】怒气）',
		desc_kr = '【분개】: 피해 받을 때 분노 100 추가 (【광분】 효과 분노 증가)',
		icon = 'city/adventure/random_tower/icon/icon_nqhs.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect',
		buffType = 4,
		passiveSkill = 80089,
		onlyOne = true
	},
	[2228] = {
		id = 2228,
		group = 2223,
		desc = '【Nộ Kích】: Khi đồng đội hạ địch, hồi 10% HP và 200 Nộ (Tăng Nộ cho hiệu ứng 【Phẫn Nộ】)',
		desc_vn = '【Nộ Kích】: Khi đồng đội hạ địch, hồi 10% HP và 200 Nộ (Tăng Nộ cho hiệu ứng 【Phẫn Nộ】)',
		desc_en = '【Rage Boost】: When an ally defeats an enemy, heals 10% HP and gains 200 Rage (Increases Rage for 【Rage】 effect)',
		desc_th = '【การโจมตีพลังโกรธ】: เมื่อเพื่อนร่วมทีมล้มศัตรู ฟื้นฟู HP 10% และพลังโกรธ 200 (เพิ่มพลังโกรธสำหรับเอฟเฟกต์ 【ความโกรธ】)',
		desc_idn = '【Nộ Kích】: Saat rekan mengalahkan musuh, memulihkan 10% HP dan 200 Kemarahan (Menambah Kemarahan untuk efek 【Phẫn Nộ】)',
		desc_cn = '【怒击】：队友击杀敌人时回复10%生命和200怒气（增加【愤怒】怒气）',
		desc_kr = '【분격】: 아군 적 처치 시 HP 10% 회복 및 분노 200 회복 (【분노】 효과 분노 증가)',
		icon = 'city/adventure/random_tower/icon/icon_nqhs.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect',
		buffType = 4,
		passiveSkill = 80090,
		onlyOne = true
	},
	[2229] = {
		id = 2229,
		group = 2223,
		desc = '【Hút Nộ】: Mỗi tướng phe ta có 30% xác suất cướp 100 Nộ mỗi khi hành động (Tăng Nộ cho hiệu ứng 【Phẫn Nộ】)',
		desc_vn = '【Hút Nộ】: Mỗi tướng phe ta có 30% xác suất cướp 100 Nộ mỗi khi hành động (Tăng Nộ cho hiệu ứng 【Phẫn Nộ】)',
		desc_en = '【Rage Steal】: Each allied officer has 30% chance to steal 100 Rage each action (Increases Rage for 【Rage】 effect)',
		desc_th = '【ดูดพลังโกรธ】: ขุนพลฝ่ายเราทุกคนมีโอกาส 30% ที่จะขโมยพลังโกรธ 100 ทุกครั้งที่ทำการกระทำ (เพิ่มพลังโกรธสำหรับเอฟเฟกต์ 【ความโกรธ】)',
		desc_idn = '【Hút Nộ】: Setiap jenderal pihak kita memiliki 30% peluang mencuri 100 Kemarahan setiap kali bertindak (Menambah Kemarahan untuk efek 【Phẫn Nộ】)',
		desc_cn = '【吸怒】：我方每名将领有30%概率在行动时抢夺100怒气（增加【愤怒】怒气）',
		desc_kr = '【흡분】: 아군 장수 행동 시 30% 확률로 분노 100 탈취 (【분노】 효과 분노 증가)',
		icon = 'city/adventure/random_tower/icon/icon_nqhs.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80091,
		onlyOne = true
	},
	[2230] = {
		id = 2230,
		group = 2223,
		desc = '【Khiêu Khích】: Dùng kỹ năng thường hoặc đòn đánh cơ bản nhận thêm 100 Nộ (Tăng Nộ cho hiệu ứng 【Phẫn Nộ】)',
		desc_vn = '【Khiêu Khích】: Dùng kỹ năng thường hoặc đòn đánh cơ bản nhận thêm 100 Nộ (Tăng Nộ cho hiệu ứng 【Phẫn Nộ】)',
		desc_en = '【Taunt】: Using normal skills or basic attacks grants an additional 100 Rage (Increases Rage for 【Rage】 effect)',
		desc_th = '【ยั่วยุ】: ใช้สกิลปกติหรือโจมตีพื้นฐาน รับพลังโกรธเพิ่ม 100 (เพิ่มพลังโกรธสำหรับเอฟเฟกต์ 【ความโกรธ】)',
		desc_idn = '【Khiêu Khích】: Menggunakan skill biasa atau serangan dasar menerima tambahan 100 Kemarahan (Menambah Kemarahan untuk efek 【Phẫn Nộ】)',
		desc_cn = '【挑衅】：使用普通技能或普攻时额外获得100怒气（增加【愤怒】怒气）',
		desc_kr = '【도발】: 기본 공격 또는 일반 스킬 사용 시 분노 100 추가 (【분노】 효과 분노 증가)',
		icon = 'city/adventure/random_tower/icon/icon_nqhs.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80092,
		onlyOne = true
	},
	[2231] = {
		id = 2231,
		group = 2223,
		desc = '【Cuồng Nộ】: Dùng kỹ năng (trừ chiêu cuối) sẽ tăng sát thương gây ra bằng 1% với mỗi 1% Nộ đã tiêu (Tăng Nộ cho hiệu ứng 【Phẫn Nộ】)',
		desc_vn = '【Cuồng Nộ】: Dùng kỹ năng (trừ chiêu cuối) sẽ tăng sát thương gây ra bằng 1% với mỗi 1% Nộ đã tiêu (Tăng Nộ cho hiệu ứng 【Phẫn Nộ】)',
		desc_en = '【Frenzy】: Using skills (except ultimate) increases damage dealt by 1% for each 1% Rage spent (Increases Rage for 【Rage】 effect)',
		desc_th = '【ความโกรธจัด】: ใช้สกิล (ไม่รวมสกิลอัลติเมท) จะเพิ่มความเสียหายที่สร้างขึ้น 1% ต่อพลังโกรธที่ใช้ไป 1% (เพิ่มพลังโกรธสำหรับเอฟเฟกต์ 【ความโกรธ】)',
		desc_idn = '【Cuồng Nộ】: Menggunakan skill (kecuali serangan pamungkas) meningkatkan kerusakan yang diberikan sebesar 1% untuk setiap 1% Kemarahan yang digunakan (Menambah Kemarahan untuk efek 【Phẫn Nộ】)',
		desc_cn = '【狂怒】：使用技能（除大招外）时，造成伤害提升1%，每消耗1%怒气提升1%（增加【愤怒】怒气）',
		desc_kr = '【광분】: 스킬(궁극기 제외) 사용 시 소비한 분노 1%당 피해 1% 증가 (【분노】 효과 분노 증가)',
		icon = 'city/adventure/random_tower/icon/icon_nqhs.png',
		iconBg = 'city/adventure/random_tower/icon/box_yellow.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80093,
		onlyOne = true
	},
	[2232] = {
		id = 2232,
		group = 12224,
		desc = '【Sinh Khiên】: Khi bắt đầu trận, toàn đội nhận lá chắn bằng 10% HP tối đa (Mỗi hiệu ứng 【Khiên Phòng, Khiên Miễn, Khiên Trị, Khiên Công, Khiên Phá, Khiên Thủ】 sẽ tăng thêm 4% hiệu quả Sinh Khiên)',
		desc_vn = '【Sinh Khiên】: Khi bắt đầu trận, toàn đội nhận lá chắn bằng 10% HP tối đa (Mỗi hiệu ứng 【Khiên Phòng, Khiên Miễn, Khiên Trị, Khiên Công, Khiên Phá, Khiên Thủ】 sẽ tăng thêm 4% hiệu quả Sinh Khiên)',
		desc_en = '【Shielding】: At battle start, the entire team gains a shield equal to 10% max HP (Each effect from 【Defense Shield, Immunity Shield, Healing Shield, Attack Shield, Shield Break, Defense Shield】 increases Shielding effect by 4%)',
		desc_th = '【เกราะชีวิต】: เมื่อเริ่มการรบ ทีมทั้งหมดได้รับโล่เท่ากับ 10% ของ HP สูงสุด (แต่ละเอฟเฟกต์ 【เกราะป้องกัน, เกราะป้องกันสถานะ, เกราะรักษา, เกราะโจมตี, เกราะทำลาย, เกราะรับ】 เพิ่มประสิทธิภาพเกราะชีวิต 4%)',
		desc_idn = '【Sinh Khiên】: Saat memulai pertandingan, seluruh tim mendapatkan perisai sebesar 10% HP maksimal (Setiap efek 【Khiên Phòng, Khiên Miễn, Khiên Trị, Khiên Công, Khiên Phá, Khiên Thủ】 menambah 4% efektivitas Sinh Khiên)',
		desc_cn = '【生盾】：战斗开始时，全队获得相当于最大生命10%的护盾（每个【盾防，盾免，盾疗，盾攻，盾破，盾守】效果额外增加4%生盾效果）',
		desc_kr = '【생천】: 전투 시작 시 전대 최대 HP의 10% 방어막 획득 (각 【방패방어, 방패면역, 방패치유, 방패공격, 방패파괴, 방패수호】 효과마다 생천 효과 4% 추가 증가)',
		icon = 'city/adventure/random_tower/icon/icon_fy.png',
		iconBg = 'city/adventure/random_tower/icon/box_blue.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80094
	},
	[2233] = {
		id = 2233,
		group = 2224,
		desc = '【Khiên Phòng】: Khi có khiên, tăng 20% Vật Phòng và Pháp Phòng (Tăng hiệu quả cho 【Sinh Khiên】)',
		desc_vn = '【Khiên Phòng】: Khi có khiên, tăng 20% Vật Phòng và Pháp Phòng (Tăng hiệu quả cho 【Sinh Khiên】)',
		desc_en = '【Defense Shield】: While shielded, increase Physical and Magical Defense by 20% (Enhances 【Shielding】 effect)',
		desc_th = '【เกราะป้องกัน】: เมื่อมีโล่ เพิ่มพลังป้องกันกายภาพและเวทย์ 20% (เพิ่มประสิทธิภาพสำหรับ 【เกราะชีวิต】)',
		desc_idn = '【Khiên Phòng】: Saat memiliki perisai, menambah 20% Pertahanan Fisik dan Pertahanan Magis (Meningkatkan efektivitas untuk 【Sinh Khiên】)',
		desc_cn = '【盾防】：有护盾时，物理防御和法术防御提升20%（增强【生盾】效果）',
		desc_kr = '【방패방어】: 방패 보유 시 물리 방어와 법술 방어 20% 증가 (【생천】 효과 강화)',
		icon = 'city/adventure/random_tower/icon/icon_fy.png',
		iconBg = 'city/adventure/random_tower/icon/box_blue.png',
		buffColor = 'effect',
		buffType = 4,
		passiveSkill = 80095,
		onlyOne = true
	},
	[2234] = {
		id = 2234,
		group = 2224,
		desc = '【Khiên Miễn】: Khi có khiên, miễn nhiễm khống chế và hiệu ứng xấu (Tăng hiệu quả cho 【Sinh Khiên】)',
		desc_vn = '【Khiên Miễn】: Khi có khiên, miễn nhiễm khống chế và hiệu ứng xấu (Tăng hiệu quả cho 【Sinh Khiên】)',
		desc_en = '【Immunity Shield】: While shielded, immune to control and negative effects (Enhances 【Shielding】 effect)',
		desc_th = '【เกราะป้องกันสถานะ】: เมื่อมีโล่ ป้องกันการควบคุมและสถานะลบ (เพิ่มประสิทธิภาพสำหรับ 【เกราะชีวิต】)',
		desc_idn = '【Khiên Miễn】: Saat memiliki perisai, kebal terhadap kontrol dan efek buruk (Meningkatkan efektivitas untuk 【Sinh Khiên】)',
		desc_cn = '【盾免】：有护盾时，免疫控制和负面效果（增强【生盾】效果）',
		desc_kr = '【방패면역】: 방패 보유 시 군중 제어 및 불리 효과 면역 (【생천】 효과 강화)',
		icon = 'city/adventure/random_tower/icon/icon_fy.png',
		iconBg = 'city/adventure/random_tower/icon/box_blue.png',
		buffColor = 'effect',
		buffType = 4,
		passiveSkill = 80096,
		onlyOne = true
	},
	[2235] = {
		id = 2235,
		group = 2224,
		desc = '【Khiên Công】: Mỗi đòn đánh gây thêm sát thương chuẩn bằng 20% giá trị khiên hiện tại (Tăng hiệu quả cho 【Sinh Khiên】)',
		desc_vn = '【Khiên Công】: Mỗi đòn đánh gây thêm sát thương chuẩn bằng 20% giá trị khiên hiện tại (Tăng hiệu quả cho 【Sinh Khiên】)',
		desc_en = '【Attack Shield】: Each attack deals additional true damage equal to 20% of current shield value (Enhances 【Shielding】 effect)',
		desc_th = '【เกราะโจมตี】: ทุกการโจมตีสร้างความเสียหายเพิ่มเติมเท่ากับ 20% ของค่าของโล่ปัจจุบัน (เพิ่มประสิทธิภาพสำหรับ 【เกราะชีวิต】)',
		desc_idn = '【Khiên Công】: Setiap serangan memberikan tambahan kerusakan sejati sebesar 20% nilai perisai saat ini (Meningkatkan efektivitas untuk 【Sinh Khiên】)',
		desc_cn = '【盾攻】：每次攻击额外造成相当于当前护盾20%的真实伤害（增强【生盾】效果）',
		desc_kr = '【방패공격】: 공격 시 현재 방패 값의 20%만큼 고정 피해 추가 (【생천】 효과 강화)',
		icon = 'city/adventure/random_tower/icon/icon_fy.png',
		iconBg = 'city/adventure/random_tower/icon/box_blue.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80097,
		onlyOne = true
	},
	[2236] = {
		id = 2236,
		group = 2224,
		desc = '【Khiên Thủ】: Mỗi lần chịu chí mạng, toàn đội nhận thêm lá chắn bằng 10% HP tối đa của bản thân (Tăng hiệu quả cho 【Sinh Khiên】)',
		desc_vn = '【Khiên Thủ】: Mỗi lần chịu chí mạng, toàn đội nhận thêm lá chắn bằng 10% HP tối đa của bản thân (Tăng hiệu quả cho 【Sinh Khiên】)',
		desc_en = '【Defense Shield】: Each time receiving a critical hit, the entire team gains an additional shield equal to 10% of their max HP (Enhances 【Shielding】 effect)',
		desc_th = '【เกราะรับ】: ทุกครั้งที่ได้รับคริติคอล ทีมทั้งหมดได้รับโล่เพิ่มเท่ากับ 10% ของ HP สูงสุดของตน (เพิ่มประสิทธิภาพสำหรับ 【เกราะชีวิต】)',
		desc_idn = '【Khiên Thủ】: Setiap kali menerima serangan kritikal, seluruh tim mendapatkan perisai sebesar 10% HP maksimal masing-masing (Meningkatkan efektivitas untuk 【Sinh Khiên】)',
		desc_cn = '【盾守】：每次受到暴击，全队获得相当于自身最大生命10%的护盾（增强【生盾】效果）',
		desc_kr = '【방패수호】: 치명타 받을 때마다 전대가 자신의 최대 HP 10%에 해당하는 방어막 획득 (【생천】 효과 강화)',
		icon = 'city/adventure/random_tower/icon/icon_fy.png',
		iconBg = 'city/adventure/random_tower/icon/box_blue.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80098,
		onlyOne = true
	},
	[2237] = {
		id = 2237,
		group = 2224,
		desc = '【Khiên Phá】: Khi khiên bị phá, toàn bộ địch mất 6% HP tối đa (Tăng hiệu quả cho 【Sinh Khiên】)',
		desc_vn = '【Khiên Phá】: Khi khiên bị phá, toàn bộ địch mất 6% HP tối đa (Tăng hiệu quả cho 【Sinh Khiên】)',
		desc_en = '【Shield Break】: When the shield breaks, all enemies lose 6% max HP (Enhances 【Shielding】 effect)',
		desc_th = '【เกราะทำลาย】: เมื่อโล่ถูกทำลาย ศัตรูทั้งหมดเสีย HP สูงสุด 6% (เพิ่มประสิทธิภาพสำหรับ 【เกราะชีวิต】)',
		desc_idn = '【Khiên Phá】: Saat perisai hancur, semua musuh kehilangan 6% HP maksimal (Meningkatkan efektivitas untuk 【Sinh Khiên】)',
		desc_cn = '【盾破】：护盾被破时，全体敌人减少6%最大生命（增强【生盾】效果）',
		desc_kr = '【방패파괴】: 방패 파괴 시 적 전체 최대 HP 6% 감소 (【생천】 효과 강화)',
		icon = 'city/adventure/random_tower/icon/icon_fy.png',
		iconBg = 'city/adventure/random_tower/icon/box_blue.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80099,
		onlyOne = true
	},
	[2238] = {
		id = 2238,
		group = 2224,
		desc = '【Khiên Trị】: Khi khiên bị phá, hồi 5% HP tối đa (Tăng hiệu quả cho 【Sinh Khiên】)',
		desc_vn = '【Khiên Trị】: Khi khiên bị phá, hồi 5% HP tối đa (Tăng hiệu quả cho 【Sinh Khiên】)',
		desc_en = '【Healing Shield】: When the shield breaks, restores 5% max HP (Enhances 【Shielding】 effect)',
		desc_th = '【เกราะรักษา】: เมื่อโล่ถูกทำลาย ฟื้นฟู HP สูงสุด 5% (เพิ่มประสิทธิภาพสำหรับ 【เกราะชีวิต】)',
		desc_idn = '【Khiên Trị】: Saat perisai hancur, memulihkan 5% HP maksimal (Meningkatkan efektivitas untuk 【Sinh Khiên】)',
		desc_cn = '【盾疗】：护盾被破时，回复5%最大生命（增强【生盾】效果）',
		desc_kr = '【방패치유】: 방패 파괴 시 최대 HP 5% 회복 (【생천】 효과 강화)',
		icon = 'city/adventure/random_tower/icon/icon_fy.png',
		iconBg = 'city/adventure/random_tower/icon/box_blue.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80100,
		onlyOne = true
	},
	[2241] = {
		id = 2241,
		group = 2227,
		desc = '【Truy Kích】: Gây thêm 30% sát thương lên mục tiêu có trên 70% HP (Khi sở hữu đồng thời 【Truy Kích】 và 【Thu Phong】, tăng thêm 20% Tỷ lệ Chí Mạng)',
		desc_vn = '【Truy Kích】: Gây thêm 30% sát thương lên mục tiêu có trên 70% HP (Khi sở hữu đồng thời 【Truy Kích】 và 【Thu Phong】, tăng thêm 20% Tỷ lệ Chí Mạng)',
		desc_en = '【Pursuit】: Deals 30% extra damage to targets above 70% HP (When possessing both 【Pursuit】 and 【Whirlwind】, critical rate increases by 20%)',
		desc_th = '【ไล่ล่า】: สร้างความเสียหายเพิ่ม 30% แก่เป้าหมายที่มี HP เกิน 70% (เมื่อมีทั้ง 【ไล่ล่า】 และ 【ลมหนาว】 เพิ่มอัตราคริติคอลอีก 20%)',
		desc_idn = '【Truy Kích】: Memberikan tambahan 30% kerusakan pada target dengan HP lebih dari 70% (Saat memiliki 【Truy Kích】 dan 【Thu Phong】 sekaligus, menambah 20% Peluang Kritikal)',
		desc_cn = '【追击】：对生命高于70%的目标造成额外30%伤害（同时拥有【追击】和【逐风】时，暴击率额外提升20%）',
		desc_kr = '【추격】: HP 70% 이상 대상에게 피해 30% 추가 (【추격】과 【취풍】 모두 보유 시 치명타 확률 20% 추가)',
		icon = 'city/adventure/random_tower/icon/icon_js.png',
		iconBg = 'city/adventure/random_tower/icon/box_red.png',
		buffColor = 'effect',
		buffType = 4,
		passiveSkill = 80102,
		onlyOne = true
	},
	[2242] = {
		id = 2242,
		group = 2227,
		desc = '【Thu Phong】: Gây thêm 40% sát thương lên mục tiêu có dưới 30% HP (Khi sở hữu đồng thời 【Truy Kích】 và 【Thu Phong】, tăng thêm 20% Tỷ lệ Chí Mạng)',
		desc_vn = '【Thu Phong】: Gây thêm 40% sát thương lên mục tiêu có dưới 30% HP (Khi sở hữu đồng thời 【Truy Kích】 và 【Thu Phong】, tăng thêm 20% Tỷ lệ Chí Mạng)',
		desc_en = '【Whirlwind】: Deals 40% extra damage to targets below 30% HP (When possessing both 【Pursuit】 and 【Whirlwind】, critical rate increases by 20%)',
		desc_th = '【ลมหนาว】: สร้างความเสียหายเพิ่ม 40% แก่เป้าหมายที่มี HP ต่ำกว่า 30% (เมื่อมีทั้ง 【ไล่ล่า】 และ 【ลมหนาว】 เพิ่มอัตราคริติคอลอีก 20%)',
		desc_idn = '【Thu Phong】: Memberikan tambahan 40% kerusakan pada target dengan HP kurang dari 30% (Saat memiliki 【Truy Kích】 dan 【Thu Phong】 sekaligus, menambah 20% Peluang Kritikal)',
		desc_cn = '【逐风】：对生命低于30%的目标造成额外40%伤害（同时拥有【追击】和【逐风】时，暴击率额外提升20%）',
		desc_kr = '【취풍】: HP 30% 이하 대상에게 피해 40% 추가 (【추격】과 【취풍】 모두 보유 시 치명타 확률 20% 추가)',
		icon = 'city/adventure/random_tower/icon/icon_fs.png',
		iconBg = 'city/adventure/random_tower/icon/box_red.png',
		buffColor = 'effect',
		buffType = 4,
		passiveSkill = 80103,
		onlyOne = true
	},
	[2243] = {
		id = 2243,
		group = 2228,
		desc = '【Huyết Kích】: Cứ mỗi 1% Hút Máu, tăng 5% sát thương cộng thêm (Khi sở hữu đồng thời 【Huyết Kích】 và 【Dơi Dực】, cướp 1% Vật Phòng và Pháp Phòng của địch với mỗi 1% Hút Máu bản thân có)',
		desc_vn = '【Huyết Kích】: Cứ mỗi 1% Hút Máu, tăng 5% sát thương cộng thêm (Khi sở hữu đồng thời 【Huyết Kích】 và 【Dơi Dực】, cướp 1% Vật Phòng và Pháp Phòng của địch với mỗi 1% Hút Máu bản thân có)',
		desc_en = '【Blood Strike】: For every 1% Life Steal, increases extra damage by 5% (When possessing both 【Blood Strike】 and 【Bat Wings】, steals 1% Physical and Magical Defense from enemies per 1% Life Steal owned)',
		desc_th = '【การโจมตีด้วยเลือด】: ทุก 1% การดูดเลือด เพิ่มความเสียหายพิเศษ 5% (เมื่อมีทั้ง 【การโจมตีด้วยเลือด】 และ 【ปีกค้างคาว】 ขโมยพลังป้องกันกายภาพและเวทย์ 1% ของศัตรูต่อ 1% การดูดเลือดของตน)',
		desc_idn = '【Huyết Kích】: Setiap 1% Serap Darah, menambah 5% kerusakan tambahan (Saat memiliki 【Huyết Kích】 dan 【Dơi Dực】 sekaligus, mencuri 1% Pertahanan Fisik dan Pertahanan Magis musuh untuk setiap 1% Serap Darah diri)',
		desc_cn = '【血击】：每1%吸血，额外增加5%伤害（同时拥有【血击】和【蝠翼】时，每1%吸血盗取敌人物理防御和法术防御1%）',
		desc_kr = '【혈격】: 생명력 흡수 1%마다 추가 피해 5% 증가 (【혈격】과 【배익】 모두 보유 시 자신의 생명력 흡수 1%당 적 물리 방어와 법술 방어 1% 탈취)',
		icon = 'city/adventure/random_tower/icon/icon_xx.png',
		iconBg = 'city/adventure/random_tower/icon/box_red.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80104,
		onlyOne = true
	},
	[2244] = {
		id = 2244,
		group = 2228,
		desc = '【Dơi Dực】: Cứ mỗi 1% Hút Máu, tăng 5% Tốc độ (Khi sở hữu đồng thời 【Huyết Kích】 và 【Dơi Dực】, cướp 1% Vật Phòng và Pháp Phòng của địch với mỗi 1% Hút Máu bản thân có)',
		desc_vn = '【Dơi Dực】: Cứ mỗi 1% Hút Máu, tăng 5% Tốc độ (Khi sở hữu đồng thời 【Huyết Kích】 và 【Dơi Dực】, cướp 1% Vật Phòng và Pháp Phòng của địch với mỗi 1% Hút Máu bản thân có)',
		desc_en = '【Bat Wings】: For every 1% Life Steal, increases Speed by 5% (When possessing both 【Blood Strike】 and 【Bat Wings】, steals 1% Physical and Magical Defense from enemies per 1% Life Steal owned)',
		desc_th = '【ปีกค้างคาว】: ทุก 1% การดูดเลือด เพิ่มความเร็ว 5% (เมื่อมีทั้ง 【การโจมตีด้วยเลือด】 และ 【ปีกค้างคาว】 ขโมยพลังป้องกันกายภาพและเวทย์ 1% ของศัตรูต่อ 1% การดูดเลือดของตน)',
		desc_idn = '【Dơi Dực】: Setiap 1% Serap Darah, menambah 5% Kecepatan (Saat memiliki 【Huyết Kích】 dan 【Dơi Dực】 sekaligus, mencuri 1% Pertahanan Fisik dan Pertahanan Magis musuh untuk setiap 1% Serap Darah diri)',
		desc_cn = '【蝠翼】：每1%吸血，提升5%速度（同时拥有【血击】和【蝠翼】时，每1%吸血盗取敌人物理防御和法术防御1%）',
		desc_kr = '【배익】: 생명력 흡수 1%마다 속도 5% 증가 (【혈격】과 【배익】 모두 보유 시 자신의 생명력 흡수 1%당 적 물리 방어와 법술 방어 1% 탈취)',
		icon = 'city/adventure/random_tower/icon/icon_xx.png',
		iconBg = 'city/adventure/random_tower/icon/box_red.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80105,
		onlyOne = true
	},
	[2245] = {
		id = 2245,
		group = 2231,
		desc = '【Ăn Mòn Kịch Độc】: Mỗi đòn đánh có 50% xác suất gây Trúng Độc (cộng dồn tối đa 5 lần. Khi sở hữu đồng thời 【Ăn Mòn Kịch Độc】, 【Máu Nhuốm】 và 【Hỏa Tốc】, toàn đội tăng 100% sát thương duy trì)',
		desc_vn = '【Ăn Mòn Kịch Độc】: Mỗi đòn đánh có 50% xác suất gây Trúng Độc (cộng dồn tối đa 5 lần. Khi sở hữu đồng thời 【Ăn Mòn Kịch Độc】, 【Máu Nhuốm】 và 【Hỏa Tốc】, toàn đội tăng 100% sát thương duy trì)',
		desc_en = '【Corrosive Poison】: Each attack has 50% chance to inflict Poison (stacks up to 5 times. When possessing 【Corrosive Poison】, 【Bloodstained】, and 【Fire Speed】 simultaneously, the entire team’s damage over time doubles)',
		desc_th = '【พิษกัดกร่อนรุนแรง】: ทุกการโจมตีมีโอกาส 50% ทำให้ติดพิษ (สะสมได้สูงสุด 5 ครั้ง เมื่อมีทั้ง 【พิษกัดกร่อนรุนแรง】, 【เลือดไหล】 และ 【ไฟเร็ว】 ทีมทั้งหมดเพิ่มความเสียหายแบบต่อเนื่อง 100%)',
		desc_idn = '【Ăn Mòn Kịch Độc】: Setiap serangan memiliki 50% peluang menyebabkan Racun (menumpuk hingga 5 kali. Saat memiliki 【Ăn Mòn Kịch Độc】, 【Máu Nhuốm】 dan 【Hỏa Tốc】 sekaligus, seluruh tim meningkatkan 100% kerusakan berkelanjutan)',
		desc_cn = '【腐蚀剧毒】：每次攻击有50%概率造成中毒（最多叠加5层，同时拥有【腐蚀剧毒】，【血染】，和【火速】时，全队持续伤害加成100%）',
		desc_kr = '【부식극독】: 공격 시 50% 확률로 중독 부여 (최대 5중첩, 【부식극독】,【혈염】,【화속】 모두 보유 시 전대 유지 피해 100% 증가)',
		icon = 'city/adventure/random_tower/icon/icon_js1.png',
		iconBg = 'city/adventure/random_tower/icon/box_red.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80106,
		onlyOne = true
	},
	[2246] = {
		id = 2246,
		group = 2231,
		desc = '【Máu Nhuốm】: Mỗi đòn đánh có 50% xác suất gây Chảy Máu (cộng dồn tối đa 5 lần. Khi sở hữu đồng thời 【Ăn Mòn Kịch Độc】, 【Máu Nhuốm】 và 【Hỏa Tốc】, toàn đội tăng 100% sát thương duy trì)',
		desc_vn = '【Máu Nhuốm】: Mỗi đòn đánh có 50% xác suất gây Chảy Máu (cộng dồn tối đa 5 lần. Khi sở hữu đồng thời 【Ăn Mòn Kịch Độc】, 【Máu Nhuốm】 và 【Hỏa Tốc】, toàn đội tăng 100% sát thương duy trì)',
		desc_en = '【Bloodstained】: Each attack has 50% chance to inflict Bleeding (stacks up to 5 times. When possessing 【Corrosive Poison】, 【Bloodstained】, and 【Fire Speed】 simultaneously, the entire team’s damage over time doubles)',
		desc_th = '【เลือดไหล】: ทุกการโจมตีมีโอกาส 50% ทำให้เลือดไหล (สะสมได้สูงสุด 5 ครั้ง เมื่อมีทั้ง 【พิษกัดกร่อนรุนแรง】, 【เลือดไหล】 และ 【ไฟเร็ว】 ทีมทั้งหมดเพิ่มความเสียหายแบบต่อเนื่อง 100%)',
		desc_idn = '【Máu Nhuốm】: Setiap serangan memiliki 50% peluang menyebabkan Pendarahan (menumpuk hingga 5 kali. Saat memiliki 【Ăn Mòn Kịch Độc】, 【Máu Nhuốm】 dan 【Hỏa Tốc】 sekaligus, seluruh tim meningkatkan 100% kerusakan berkelanjutan)',
		desc_cn = '【血染】：每次攻击有50%概率造成流血（最多叠加5层，同时拥有【腐蚀剧毒】，【血染】，和【火速】时，全队持续伤害加成100%）',
		desc_kr = '【혈염】: 공격 시 50% 확률로 출혈 부여 (최대 5중첩, 【부식극독】,【혈염】,【화속】 모두 보유 시 전대 유지 피해 100% 증가)',
		icon = 'city/adventure/random_tower/icon/icon_js.png',
		iconBg = 'city/adventure/random_tower/icon/box_red.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80107,
		onlyOne = true
	},
	[2247] = {
		id = 2247,
		group = 2231,
		desc = '【Hỏa Tốc】: Mỗi đòn đánh có 50% xác suất gây Thiêu Đốt (cộng dồn tối đa 5 lần. Khi sở hữu đồng thời 【Ăn Mòn Kịch Độc】, 【Máu Nhuốm】 và 【Hỏa Tốc】, toàn đội tăng 100% sát thương duy trì)',
		desc_vn = '【Hỏa Tốc】: Mỗi đòn đánh có 50% xác suất gây Thiêu Đốt (cộng dồn tối đa 5 lần. Khi sở hữu đồng thời 【Ăn Mòn Kịch Độc】, 【Máu Nhuốm】 và 【Hỏa Tốc】, toàn đội tăng 100% sát thương duy trì)',
		desc_en = '【Fire Speed】: Each attack has 50% chance to inflict Burning (stacks up to 5 times. When possessing 【Corrosive Poison】, 【Bloodstained】, and 【Fire Speed】 simultaneously, the entire team’s damage over time doubles)',
		desc_th = '【ไฟเร็ว】: ทุกการโจมตีมีโอกาส 50% ทำให้ไฟลุกลาม (สะสมได้สูงสุด 5 ครั้ง เมื่อมีทั้ง 【พิษกัดกร่อนรุนแรง】, 【เลือดไหล】 และ 【ไฟเร็ว】 ทีมทั้งหมดเพิ่มความเสียหายแบบต่อเนื่อง 100%)',
		desc_idn = '【Hỏa Tốc】: Setiap serangan memiliki 50% peluang menyebabkan Terbakar (menumpuk hingga 5 kali. Saat memiliki 【Ăn Mòn Kịch Độc】, 【Máu Nhuốm】 dan 【Hỏa Tốc】 sekaligus, seluruh tim meningkatkan 100% kerusakan berkelanjutan)',
		desc_cn = '【火速】：每次攻击有50%概率造成灼烧（最多叠加5层，同时拥有【腐蚀剧毒】，【血染】，和【火速】时，全队持续伤害加成100%）',
		desc_kr = '【화속】: 공격 시 50% 확률로 화상 부여 (최대 5중첩, 【부식극독】,【혈염】,【화속】 모두 보유 시 전대 유지 피해 100% 증가)',
		icon = 'city/adventure/random_tower/icon/icon_cxsh.png',
		iconBg = 'city/adventure/random_tower/icon/box_red.png',
		buffColor = 'effect_huang',
		buffType = 4,
		passiveSkill = 80108,
		onlyOne = true
	},
	[2249] = {
		id = 2249,
		group = 2221,
		desc = 'Vật Công và Trí Lực toàn đội +10%',
		desc_vn = 'Vật Công và Trí Lực toàn đội +10%',
		desc_en = 'Team Physical Attack and Intelligence +10%',
		desc_th = 'พลังโจมตีและปัญญาของทีมทั้งหมด +10%',
		desc_idn = 'Serangan Fisik dan Kecerdasan seluruh tim +10%',
		desc_cn = '全队物理攻击和智力 +10%',
		desc_kr = '전대의 물리 공격과 지력 +10%',
		weight = 1,
		icon = 'city/adventure/random_tower/icon/icon_gj.png',
		iconBg = 'city/adventure/random_tower/icon/box_red.png',
		buffColor = 'effect',
		attrType1 = 8,
		attrNum1 = '10%',
		attrType2 = 7,
		attrNum2 = '10%'
	},
	[2250] = {
		id = 2250,
		group = 2222,
		desc = 'Vật Công và Trí Lực toàn đội +10%',
		desc_vn = 'Vật Công và Trí Lực toàn đội +10%',
		desc_en = 'Team Physical Attack and Intelligence +10%',
		desc_th = 'พลังโจมตีและปัญญาของทีมทั้งหมด +10%',
		desc_idn = 'Serangan Fisik dan Kecerdasan seluruh tim +10%',
		desc_cn = '全队物理攻击和智力 +10%',
		desc_kr = '전대의 물리 공격과 지력 +10%',
		weight = 1,
		icon = 'city/adventure/random_tower/icon/icon_gj.png',
		iconBg = 'city/adventure/random_tower/icon/box_red.png',
		buffColor = 'effect',
		attrType1 = 8,
		attrNum1 = '10%',
		attrType2 = 7,
		attrNum2 = '10%'
	},
	[2251] = {
		id = 2251,
		group = 2223,
		desc = 'Vật Công và Trí Lực toàn đội +5%',
		desc_vn = 'Vật Công và Trí Lực toàn đội +5%',
		desc_en = 'Team Physical Attack and Intelligence +5%',
		desc_th = 'พลังโจมตีและปัญญาของทีมทั้งหมด +5%',
		desc_idn = 'Serangan Fisik dan Kecerdasan seluruh tim +5%',
		desc_cn = '全队物理攻击和智力 +5%',
		desc_kr = '전대의 물리 공격과 지력 +5%',
		weight = 1,
		icon = 'city/adventure/random_tower/icon/icon_gj.png',
		iconBg = 'city/adventure/random_tower/icon/box_red.png',
		buffColor = 'effect',
		attrType1 = 8,
		attrNum1 = '5%',
		attrType2 = 7,
		attrNum2 = '5%'
	},
	[2252] = {
		id = 2252,
		group = 2224,
		desc = 'Vật Công và Trí Lực toàn đội +5%',
		desc_vn = 'Vật Công và Trí Lực toàn đội +5%',
		desc_en = 'Team Physical Attack and Intelligence +5%',
		desc_th = 'พลังโจมตีและปัญญาของทีมทั้งหมด +5%',
		desc_idn = 'Serangan Fisik dan Kecerdasan seluruh tim +5%',
		desc_cn = '全队物理攻击和智力 +5%',
		desc_kr = '전대의 물리 공격과 지력 +5%',
		weight = 1,
		icon = 'city/adventure/random_tower/icon/icon_gj.png',
		iconBg = 'city/adventure/random_tower/icon/box_red.png',
		buffColor = 'effect',
		attrType1 = 8,
		attrNum1 = '5%',
		attrType2 = 7,
		attrNum2 = '5%'
	},
	[2253] = {
		id = 2253,
		group = 2227,
		desc = 'Vật Công và Trí Lực toàn đội +10%',
		desc_vn = 'Vật Công và Trí Lực toàn đội +10%',
		desc_en = 'Team Physical Attack and Intelligence +10%',
		desc_th = 'พลังโจมตีและปัญญาของทีมทั้งหมด +10%',
		desc_idn = 'Serangan Fisik dan Kecerdasan seluruh tim +10%',
		desc_cn = '全队物理攻击和智力 +10%',
		desc_kr = '전대의 물리 공격과 지력 +10%',
		weight = 1,
		icon = 'city/adventure/random_tower/icon/icon_gj.png',
		iconBg = 'city/adventure/random_tower/icon/box_red.png',
		buffColor = 'effect',
		attrType1 = 8,
		attrNum1 = '10%',
		attrType2 = 7,
		attrNum2 = '10%'
	},
	[2254] = {
		id = 2254,
		group = 2228,
		desc = 'Vật Công và Trí Lực toàn đội +10%',
		desc_vn = 'Vật Công và Trí Lực toàn đội +10%',
		desc_en = 'Team Physical Attack and Intelligence +10%',
		desc_th = 'พลังโจมตีและปัญญาของทีมทั้งหมด +10%',
		desc_idn = 'Serangan Fisik dan Kecerdasan seluruh tim +10%',
		desc_cn = '全队物理攻击和智力 +10%',
		desc_kr = '전대의 물리 공격과 지력 +10%',
		weight = 1,
		icon = 'city/adventure/random_tower/icon/icon_gj.png',
		iconBg = 'city/adventure/random_tower/icon/box_red.png',
		buffColor = 'effect',
		attrType1 = 8,
		attrNum1 = '10%',
		attrType2 = 7,
		attrNum2 = '10%'
	},
	[2255] = {
		id = 2255,
		group = 2231,
		desc = 'Vật Công và Trí Lực toàn đội +10%',
		desc_vn = 'Vật Công và Trí Lực toàn đội +10%',
		desc_en = 'Team Physical Attack and Intelligence +10%',
		desc_th = 'พลังโจมตีและปัญญาของทีมทั้งหมด +10%',
		desc_idn = 'Serangan Fisik dan Kecerdasan seluruh tim +10%',
		desc_cn = '全队物理攻击和智力 +10%',
		desc_kr = '전대의 물리 공격과 지력 +10%',
		weight = 1,
		icon = 'city/adventure/random_tower/icon/icon_gj.png',
		iconBg = 'city/adventure/random_tower/icon/box_red.png',
		buffColor = 'effect',
		attrType1 = 8,
		attrNum1 = '10%',
		attrType2 = 7,
		attrNum2 = '10%'
	},

	__size = 210,
	__default = {
		__index = {
			group = 1,
			desc = '',
			desc_vn = '',
			desc_en = '',
			desc_th = '',
			desc_idn = '',
			desc_cn = '',
			desc_kr = '',
			weight = 999,
			icon = '',
			iconBg = '',
			buffColor = '',
			buffType = 1,
			attrNatureType1 = 0,
			attrType1 = 0,
			attrNum1 = '',
			attrType2 = 0,
			attrNum2 = '',
			attrNatureType3 = 0,
			attrType3 = 0,
			attrNum3 = '',
			supplyType = 0,
			supplyTarget = 0,
			supplyNum = 0,
			pointType = 0,
			pointValue = 0,
			passiveSkill = 0,
			effectTimes = 0,
			skillgroup = {},
			onlyOne = false,
			limit = 99,
			changeLib = 0,
			belongLib = 0,
			condition = 0
		}
	}
}
return csv.random_tower.buffs