
csv['cross']['service'] = {
	[2] = {
		id = 2,
		service = 'crosscraft',
		servers = {'game.en.1', 'game.en.2', 'game.en.3', 'game.en.4', 'game.en.5', 'game.en.6', 'game.en.7', 'game.en.8', 'game.en.9', 'game.en.10'},
		date = 20220923,
		cross = 'crosscraft.en.1'
	},
	[3] = {
		id = 3,
		service = 'crossmine',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8'},
		date = 20230704,
		cross = 'crossmine.en.1'
	},
	[5] = {
		id = 5,
		service = 'onlinefight',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8', 'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16', 'gamemerge.en.17', 'gamemerge.en.18', 'game.en.181', 'game.en.182', 'game.en.183', 'game.en.184', 'game.en.185', 'game.en.186', 'game.en.187', 'game.en.188', 'game.en.189', 'game.en.190', 'game.en.191', 'game.en.192', 'game.en.193', 'game.en.194', 'game.en.195', 'game.en.196', 'game.en.197', 'game.en.198', 'game.en.199', 'game.en.200', 'game.en.201', 'game.en.202', 'game.en.203', 'game.en.204', 'game.en.205', 'game.en.206', 'game.en.207', 'game.en.208', 'game.en.209', 'game.en.210', 'game.en.211', 'game.en.212', 'game.en.213', 'game.en.214', 'game.en.215', 'game.en.216', 'game.en.217', 'game.en.218', 'game.en.219', 'game.en.220', 'game.en.221', 'game.en.222', 'game.en.223', 'game.en.224', 'game.en.225', 'game.en.226', 'game.en.227', 'game.en.228', 'game.en.229', 'game.en.230', 'game.en.231', 'game.en.232', 'game.en.233', 'game.en.234', 'game.en.235', 'game.en.236', 'game.en.237', 'game.en.238', 'game.en.239', 'game.en.240', 'game.en.241', 'game.en.242', 'game.en.243', 'game.en.244', 'game.en.245', 'game.en.246', 'game.en.247', 'game.en.248', 'game.en.249', 'game.en.250', 'game.en.251', 'game.en.252', 'game.en.253', 'game.en.254', 'game.en.255', 'game.en.256', 'game.en.257', 'game.en.258', 'game.en.259', 'game.en.260', 'game.en.261', 'game.en.262', 'game.en.263', 'game.en.264', 'game.en.265', 'game.en.266', 'game.en.267', 'game.en.268', 'game.en.269', 'game.en.270', 'game.en.272', 'game.en.273', 'game.en.274', 'game.en.275', 'game.en.276', 'game.en.277', 'game.en.278', 'game.en.279', 'game.en.280', 'game.en.281', 'game.en.282', 'game.en.283', 'game.en.284', 'game.en.285', 'game.en.286', 'game.en.287', 'game.en.288', 'game.en.289', 'game.en.290', 'game.en.291', 'game.en.292', 'game.en.293', 'game.en.294', 'game.en.295', 'game.en.296', 'game.en.297', 'game.en.298'},
		date = 20230624,
		cross = 'onlinefight.en.1',
		endDate = 20230720
	},
	[6] = {
		id = 6,
		service = 'crossgym',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8', 'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16', 'gamemerge.en.17', 'gamemerge.en.18', 'game.en.181', 'game.en.182', 'game.en.183', 'game.en.184', 'game.en.185', 'game.en.186', 'game.en.187', 'game.en.188', 'game.en.189', 'game.en.190', 'game.en.191', 'game.en.192', 'game.en.193', 'game.en.194', 'game.en.195', 'game.en.196', 'game.en.197', 'game.en.198', 'game.en.199', 'game.en.200', 'game.en.201', 'game.en.202', 'game.en.203', 'game.en.204', 'game.en.205', 'game.en.206', 'game.en.207', 'game.en.208', 'game.en.209', 'game.en.210', 'game.en.211', 'game.en.212', 'game.en.213', 'game.en.214', 'game.en.215', 'game.en.216', 'game.en.217', 'game.en.218', 'game.en.219', 'game.en.220', 'game.en.221', 'game.en.222', 'game.en.223', 'game.en.224', 'game.en.225', 'game.en.226', 'game.en.227', 'game.en.228', 'game.en.229', 'game.en.230', 'game.en.231', 'game.en.232', 'game.en.233', 'game.en.234', 'game.en.235', 'game.en.236', 'game.en.237', 'game.en.238', 'game.en.239', 'game.en.240', 'game.en.241', 'game.en.242', 'game.en.243', 'game.en.244', 'game.en.245', 'game.en.246', 'game.en.247', 'game.en.248', 'game.en.249', 'game.en.250', 'game.en.251', 'game.en.252', 'game.en.253', 'game.en.254', 'game.en.255', 'game.en.256', 'game.en.257', 'game.en.258', 'game.en.259', 'game.en.260', 'game.en.261', 'game.en.262', 'game.en.263', 'game.en.264', 'game.en.265', 'game.en.266', 'game.en.267', 'game.en.268', 'game.en.269', 'game.en.270', 'game.en.272', 'game.en.273', 'game.en.274', 'game.en.275', 'game.en.276', 'game.en.277', 'game.en.278', 'game.en.279', 'game.en.280', 'game.en.281', 'game.en.282', 'game.en.283', 'game.en.284', 'game.en.285', 'game.en.286', 'game.en.287', 'game.en.288', 'game.en.289', 'game.en.290', 'game.en.291', 'game.en.292', 'game.en.293', 'game.en.294', 'game.en.295', 'game.en.296', 'game.en.297', 'game.en.298'},
		date = 20230703,
		cross = 'crossgym.en.1',
		endDate = 20230709
	},
	[7] = {
		id = 7,
		service = 'crossunionfight',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8', 'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16', 'gamemerge.en.17', 'gamemerge.en.18'},
		date = 20230624,
		cross = 'crossunionfight.en.1'
	},
	[8] = {
		id = 8,
		service = 'crossarena',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8', 'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16', 'gamemerge.en.17', 'gamemerge.en.18', 'game.en.181', 'game.en.182', 'game.en.183', 'game.en.184', 'game.en.185', 'game.en.186', 'game.en.187', 'game.en.188', 'game.en.189', 'game.en.190', 'game.en.191', 'game.en.192', 'game.en.193', 'game.en.194', 'game.en.195', 'game.en.196', 'game.en.197', 'game.en.198', 'game.en.199', 'game.en.200', 'game.en.201', 'game.en.202', 'game.en.203', 'game.en.204', 'game.en.205', 'game.en.206', 'game.en.207', 'game.en.208', 'game.en.209', 'game.en.210', 'game.en.211', 'game.en.212', 'game.en.213', 'game.en.214', 'game.en.215', 'game.en.216', 'game.en.217', 'game.en.218', 'game.en.219', 'game.en.220', 'game.en.221', 'game.en.222', 'game.en.223', 'game.en.224', 'game.en.225', 'game.en.226', 'game.en.227', 'game.en.228', 'game.en.229', 'game.en.230', 'game.en.231', 'game.en.232', 'game.en.233', 'game.en.234', 'game.en.235', 'game.en.236', 'game.en.237', 'game.en.238', 'game.en.239', 'game.en.240', 'game.en.241', 'game.en.242', 'game.en.243', 'game.en.244', 'game.en.245', 'game.en.246', 'game.en.247', 'game.en.248', 'game.en.249', 'game.en.250', 'game.en.251', 'game.en.252', 'game.en.253', 'game.en.254', 'game.en.255', 'game.en.256', 'game.en.257', 'game.en.258', 'game.en.259', 'game.en.260', 'game.en.261', 'game.en.262', 'game.en.263', 'game.en.264', 'game.en.265', 'game.en.266', 'game.en.267', 'game.en.268', 'game.en.269', 'game.en.270', 'game.en.272', 'game.en.273', 'game.en.274', 'game.en.275', 'game.en.276', 'game.en.277', 'game.en.278', 'game.en.279', 'game.en.280', 'game.en.281', 'game.en.282', 'game.en.283', 'game.en.284', 'game.en.285', 'game.en.286', 'game.en.287', 'game.en.288', 'game.en.289', 'game.en.290', 'game.en.291', 'game.en.292', 'game.en.293', 'game.en.294', 'game.en.295', 'game.en.296', 'game.en.297', 'game.en.298'},
		date = 20230224,
		cross = 'crossarena.en.1'
	},
	[9] = {
		id = 9,
		service = 'crossunionqa',
		date = 20230708,
		cross = 'en',
		endDate = 20230709
	},
	[10] = {
		id = 10,
		service = 'crossfishing',
		date = 20230709,
		cross = 'en'
	},
	[172] = {
		id = 172,
		service = 'crosshorse',
		servers = {'all'},
		date = 20230823,
		cross = 'crosshorse.en.3',
		endDate = 20230830
	},
	[173] = {
		id = 173,
		service = 'crosshorse',
		servers = {'all'},
		date = 20230815,
		cross = 'crosshorse.vn.1',
		endDate = 20230830
	},
	[174] = {
		id = 174,
		service = 'horseraceranking',
		date = 20230824,
		cross = 'en',
		endDate = 20230830
	},
	[2001] = {
		id = 2001,
		service = 'crosscraft',
		servers = {'game.en.1', 'game.en.2', 'game.en.3', 'game.en.4', 'game.en.5', 'game.en.6', 'game.en.7', 'game.en.8', 'game.en.9', 'game.en.10'},
		date = 20220930,
		cross = 'crosscraft.en.1'
	},
	[2002] = {
		id = 2002,
		service = 'crosscraft',
		servers = {'game.en.1', 'game.en.2', 'game.en.3', 'game.en.4', 'game.en.5', 'game.en.6', 'game.en.7', 'game.en.8', 'game.en.9', 'game.en.10'},
		date = 20221003,
		cross = 'crosscraft.en.1'
	},
	[2003] = {
		id = 2003,
		service = 'crosscraft',
		servers = {'game.en.1', 'game.en.2', 'game.en.3', 'game.en.4', 'game.en.5', 'game.en.6', 'game.en.7', 'game.en.8', 'game.en.9', 'game.en.10'},
		date = 20221007,
		cross = 'crosscraft.en.1'
	},
	[2004] = {
		id = 2004,
		service = 'crosscraft',
		servers = {'game.en.1', 'game.en.2', 'game.en.3', 'game.en.4', 'game.en.5', 'game.en.6', 'game.en.7', 'game.en.8', 'game.en.9', 'game.en.10'},
		date = 20221010,
		cross = 'crosscraft.en.1'
	},
	[2005] = {
		id = 2005,
		service = 'crosscraft',
		servers = {'game.en.1', 'game.en.2', 'game.en.3', 'game.en.4', 'game.en.5', 'game.en.6', 'game.en.7', 'game.en.8', 'game.en.9', 'game.en.10'},
		date = 20221014,
		cross = 'crosscraft.en.1'
	},
	[2101] = {
		id = 2101,
		service = 'crosscraft',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8', 'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16', 'gamemerge.en.17', 'gamemerge.en.18'},
		date = 20230703,
		cross = 'crosscraft.en.1'
	},
	[2102] = {
		id = 2102,
		service = 'crosscraft',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8', 'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16', 'gamemerge.en.17', 'gamemerge.en.18'},
		date = 20230707,
		cross = 'crosscraft.en.1'
	},
	[2103] = {
		id = 2103,
		service = 'crosscraft',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8', 'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16', 'gamemerge.en.17', 'gamemerge.en.18'},
		date = 20230711,
		cross = 'crosscraft.en.1'
	},
	[2104] = {
		id = 2104,
		service = 'crosscraft',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8', 'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16', 'gamemerge.en.17', 'gamemerge.en.18'},
		date = 20230714,
		cross = 'crosscraft.en.1'
	},
	[2105] = {
		id = 2105,
		service = 'crosscraft',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8', 'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16', 'gamemerge.en.17', 'gamemerge.en.18'},
		date = 20230717,
		cross = 'crosscraft.en.1'
	},
	[2106] = {
		id = 2106,
		service = 'crosscraft',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8', 'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16', 'gamemerge.en.17', 'gamemerge.en.18'},
		date = 20230721,
		cross = 'crosscraft.en.1'
	},
	[2107] = {
		id = 2107,
		service = 'crosscraft',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8', 'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16', 'gamemerge.en.17', 'gamemerge.en.18'},
		date = 20230724,
		cross = 'crosscraft.en.1'
	},
	[2108] = {
		id = 2108,
		service = 'crosscraft',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8', 'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16', 'gamemerge.en.17', 'gamemerge.en.18'},
		date = 20230728,
		cross = 'crosscraft.en.1'
	},
	[2109] = {
		id = 2109,
		service = 'crosscraft',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8', 'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16', 'gamemerge.en.17', 'gamemerge.en.18'},
		date = 20230731,
		cross = 'crosscraft.en.1'
	},
	[2110] = {
		id = 2110,
		service = 'crosscraft',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8', 'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16', 'gamemerge.en.17', 'gamemerge.en.18'},
		date = 20230804,
		cross = 'crosscraft.en.1'
	},
	[2111] = {
		id = 2111,
		service = 'crosscraft',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8', 'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16', 'gamemerge.en.17', 'gamemerge.en.18'},
		date = 20230807,
		cross = 'crosscraft.en.1'
	},
	[2112] = {
		id = 2112,
		service = 'crosscraft',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8', 'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16', 'gamemerge.en.17', 'gamemerge.en.18'},
		date = 20230811,
		cross = 'crosscraft.en.1'
	},
	[2113] = {
		id = 2113,
		service = 'crosscraft',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8', 'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16', 'gamemerge.en.17', 'gamemerge.en.18'},
		date = 20230814,
		cross = 'crosscraft.en.1'
	},
	[2114] = {
		id = 2114,
		service = 'crosscraft',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8', 'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16', 'gamemerge.en.17', 'gamemerge.en.18'},
		date = 20230818,
		cross = 'crosscraft.en.1'
	},
	[2115] = {
		id = 2115,
		service = 'crosscraft',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8', 'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16', 'gamemerge.en.17', 'gamemerge.en.18'},
		date = 20230821,
		cross = 'crosscraft.en.1'
	},
	[2116] = {
		id = 2116,
		service = 'crosscraft',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8', 'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16', 'gamemerge.en.17', 'gamemerge.en.18'},
		date = 20230825,
		cross = 'crosscraft.en.1'
	},
	[2117] = {
		id = 2117,
		service = 'crosscraft',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8', 'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16', 'gamemerge.en.17', 'gamemerge.en.18'},
		date = 20230828,
		cross = 'crosscraft.en.1'
	},
	[2118] = {
		id = 2118,
		service = 'crosscraft',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8', 'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16', 'gamemerge.en.17', 'gamemerge.en.18'},
		date = 20230901,
		cross = 'crosscraft.en.1'
	},
	[2119] = {
		id = 2119,
		service = 'crosscraft',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8', 'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16', 'gamemerge.en.17', 'gamemerge.en.18'},
		date = 20230904,
		cross = 'crosscraft.en.1'
	},
	[2120] = {
		id = 2120,
		service = 'crosscraft',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8', 'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16', 'gamemerge.en.17', 'gamemerge.en.18'},
		date = 20230908,
		cross = 'crosscraft.en.1'
	},
	[2121] = {
		id = 2121,
		service = 'crosscraft',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8', 'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16', 'gamemerge.en.17', 'gamemerge.en.18'},
		date = 20230911,
		cross = 'crosscraft.en.1'
	},
	[2122] = {
		id = 2122,
		service = 'crosscraft',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8', 'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16', 'gamemerge.en.17', 'gamemerge.en.18'},
		date = 20230918,
		cross = 'crosscraft.en.1'
	},
	[2123] = {
		id = 2123,
		service = 'crosscraft',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8', 'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16', 'gamemerge.en.17', 'gamemerge.en.18'},
		date = 20230922,
		cross = 'crosscraft.en.1'
	},
	[2124] = {
		id = 2124,
		service = 'crosscraft',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8', 'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16', 'gamemerge.en.17', 'gamemerge.en.18'},
		date = 20230925,
		cross = 'crosscraft.en.1'
	},
	[2125] = {
		id = 2125,
		service = 'crosscraft',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8', 'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16', 'gamemerge.en.17', 'gamemerge.en.18'},
		date = 20230929,
		cross = 'crosscraft.en.1'
	},
	[2126] = {
		id = 2126,
		service = 'crosscraft',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8', 'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16', 'gamemerge.en.17', 'gamemerge.en.18'},
		date = 20231002,
		cross = 'crosscraft.en.1'
	},
	[3001] = {
		id = 3001,
		service = 'crossmine',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8'},
		date = 20230711,
		cross = 'crossmine.en.1'
	},
	[3002] = {
		id = 3002,
		service = 'crossmine',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8'},
		date = 20230718,
		cross = 'crossmine.en.1'
	},
	[3003] = {
		id = 3003,
		service = 'crossmine',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8'},
		date = 20230725,
		cross = 'crossmine.en.1'
	},
	[3004] = {
		id = 3004,
		service = 'crossmine',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8'},
		date = 20230801,
		cross = 'crossmine.en.1'
	},
	[3005] = {
		id = 3005,
		service = 'crossmine',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8'},
		date = 20230808,
		cross = 'crossmine.en.1'
	},
	[3006] = {
		id = 3006,
		service = 'crossmine',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8'},
		date = 20230815,
		cross = 'crossmine.en.1'
	},
	[3007] = {
		id = 3007,
		service = 'crossmine',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8'},
		date = 20230822,
		cross = 'crossmine.en.1'
	},
	[3008] = {
		id = 3008,
		service = 'crossmine',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8'},
		date = 20230829,
		cross = 'crossmine.en.1'
	},
	[3009] = {
		id = 3009,
		service = 'crossmine',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8'},
		date = 20230905,
		cross = 'crossmine.en.1'
	},
	[3010] = {
		id = 3010,
		service = 'crossmine',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8'},
		date = 20230919,
		cross = 'crossmine.en.1'
	},
	[3011] = {
		id = 3011,
		service = 'crossmine',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8'},
		date = 20230926,
		cross = 'crossmine.en.1'
	},
	[3012] = {
		id = 3012,
		service = 'crossmine',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8'},
		date = 20231003,
		cross = 'crossmine.en.1'
	},
	[3101] = {
		id = 3101,
		service = 'crossmine',
		servers = {'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16'},
		date = 20230704,
		cross = 'crossmine.en.2'
	},
	[3102] = {
		id = 3102,
		service = 'crossmine',
		servers = {'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16'},
		date = 20230711,
		cross = 'crossmine.en.2'
	},
	[3103] = {
		id = 3103,
		service = 'crossmine',
		servers = {'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16'},
		date = 20230718,
		cross = 'crossmine.en.2'
	},
	[3104] = {
		id = 3104,
		service = 'crossmine',
		servers = {'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16'},
		date = 20230725,
		cross = 'crossmine.en.2'
	},
	[3105] = {
		id = 3105,
		service = 'crossmine',
		servers = {'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16'},
		date = 20230801,
		cross = 'crossmine.en.2'
	},
	[3106] = {
		id = 3106,
		service = 'crossmine',
		servers = {'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16'},
		date = 20230808,
		cross = 'crossmine.en.2'
	},
	[3107] = {
		id = 3107,
		service = 'crossmine',
		servers = {'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16'},
		date = 20230815,
		cross = 'crossmine.en.2'
	},
	[3108] = {
		id = 3108,
		service = 'crossmine',
		servers = {'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16'},
		date = 20230822,
		cross = 'crossmine.en.2'
	},
	[3109] = {
		id = 3109,
		service = 'crossmine',
		servers = {'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16'},
		date = 20230829,
		cross = 'crossmine.en.2'
	},
	[3110] = {
		id = 3110,
		service = 'crossmine',
		servers = {'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16'},
		date = 20230905,
		cross = 'crossmine.en.2'
	},
	[3111] = {
		id = 3111,
		service = 'crossmine',
		servers = {'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16'},
		date = 20230919,
		cross = 'crossmine.en.2'
	},
	[3112] = {
		id = 3112,
		service = 'crossmine',
		servers = {'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16'},
		date = 20230926,
		cross = 'crossmine.en.2'
	},
	[3113] = {
		id = 3113,
		service = 'crossmine',
		servers = {'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16'},
		date = 20231003,
		cross = 'crossmine.en.2'
	},
	[3200] = {
		id = 3200,
		service = 'crossmine',
		servers = {'gamemerge.en.17', 'gamemerge.en.18'},
		date = 20230704,
		cross = 'crossmine.en.2'
	},
	[3201] = {
		id = 3201,
		service = 'crossmine',
		servers = {'gamemerge.en.17', 'gamemerge.en.18'},
		date = 20230711,
		cross = 'crossmine.en.2'
	},
	[3202] = {
		id = 3202,
		service = 'crossmine',
		servers = {'gamemerge.en.17', 'gamemerge.en.18'},
		date = 20230718,
		cross = 'crossmine.en.2'
	},
	[3203] = {
		id = 3203,
		service = 'crossmine',
		servers = {'gamemerge.en.17', 'gamemerge.en.18'},
		date = 20230725,
		cross = 'crossmine.en.2'
	},
	[3204] = {
		id = 3204,
		service = 'crossmine',
		servers = {'gamemerge.en.17', 'gamemerge.en.18'},
		date = 20230801,
		cross = 'crossmine.en.2'
	},
	[3205] = {
		id = 3205,
		service = 'crossmine',
		servers = {'gamemerge.en.17', 'gamemerge.en.18'},
		date = 20230808,
		cross = 'crossmine.en.2'
	},
	[3206] = {
		id = 3206,
		service = 'crossmine',
		servers = {'gamemerge.en.17', 'gamemerge.en.18'},
		date = 20230815,
		cross = 'crossmine.en.2'
	},
	[3207] = {
		id = 3207,
		service = 'crossmine',
		servers = {'gamemerge.en.17', 'gamemerge.en.18'},
		date = 20230822,
		cross = 'crossmine.en.2'
	},
	[3208] = {
		id = 3208,
		service = 'crossmine',
		servers = {'gamemerge.en.17', 'gamemerge.en.18'},
		date = 20230829,
		cross = 'crossmine.en.2'
	},
	[3209] = {
		id = 3209,
		service = 'crossmine',
		servers = {'gamemerge.en.17', 'gamemerge.en.18'},
		date = 20230905,
		cross = 'crossmine.en.2'
	},
	[3210] = {
		id = 3210,
		service = 'crossmine',
		servers = {'gamemerge.en.17', 'gamemerge.en.18'},
		date = 20230919,
		cross = 'crossmine.en.2'
	},
	[3211] = {
		id = 3211,
		service = 'crossmine',
		servers = {'gamemerge.en.17', 'gamemerge.en.18'},
		date = 20230926,
		cross = 'crossmine.en.2'
	},
	[3212] = {
		id = 3212,
		service = 'crossmine',
		servers = {'gamemerge.en.17', 'gamemerge.en.18'},
		date = 20231003,
		cross = 'crossmine.en.2'
	},
	[4800] = {
		id = 4800,
		service = 'crossmine',
		servers = {'game.en.181', 'game.en.182', 'game.en.183', 'game.en.184', 'game.en.185', 'game.en.186', 'game.en.187', 'game.en.188', 'game.en.189', 'game.en.190'},
		date = 20230704,
		cross = 'crossmine.en.19'
	},
	[4801] = {
		id = 4801,
		service = 'crossmine',
		servers = {'game.en.181', 'game.en.182', 'game.en.183', 'game.en.184', 'game.en.185', 'game.en.186', 'game.en.187', 'game.en.188', 'game.en.189', 'game.en.190'},
		date = 20230711,
		cross = 'crossmine.en.19'
	},
	[4802] = {
		id = 4802,
		service = 'crossmine',
		servers = {'game.en.181', 'game.en.182', 'game.en.183', 'game.en.184', 'game.en.185', 'game.en.186', 'game.en.187', 'game.en.188', 'game.en.189', 'game.en.190'},
		date = 20230718,
		cross = 'crossmine.en.19'
	},
	[4803] = {
		id = 4803,
		service = 'crossmine',
		servers = {'game.en.181', 'game.en.182', 'game.en.183', 'game.en.184', 'game.en.185', 'game.en.186', 'game.en.187', 'game.en.188', 'game.en.189', 'game.en.190'},
		date = 20230725,
		cross = 'crossmine.en.19'
	},
	[4804] = {
		id = 4804,
		service = 'crossmine',
		servers = {'game.en.181', 'game.en.182', 'game.en.183', 'game.en.184', 'game.en.185', 'game.en.186', 'game.en.187', 'game.en.188', 'game.en.189', 'game.en.190'},
		date = 20230801,
		cross = 'crossmine.en.19'
	},
	[4805] = {
		id = 4805,
		service = 'crossmine',
		servers = {'game.en.181', 'game.en.182', 'game.en.183', 'game.en.184', 'game.en.185', 'game.en.186', 'game.en.187', 'game.en.188', 'game.en.189', 'game.en.190'},
		date = 20230808,
		cross = 'crossmine.en.19'
	},
	[4806] = {
		id = 4806,
		service = 'crossmine',
		servers = {'game.en.181', 'game.en.182', 'game.en.183', 'game.en.184', 'game.en.185', 'game.en.186', 'game.en.187', 'game.en.188', 'game.en.189', 'game.en.190'},
		date = 20230815,
		cross = 'crossmine.en.19'
	},
	[4807] = {
		id = 4807,
		service = 'crossmine',
		servers = {'game.en.181', 'game.en.182', 'game.en.183', 'game.en.184', 'game.en.185', 'game.en.186', 'game.en.187', 'game.en.188', 'game.en.189', 'game.en.190'},
		date = 20230822,
		cross = 'crossmine.en.19'
	},
	[4808] = {
		id = 4808,
		service = 'crossmine',
		servers = {'game.en.181', 'game.en.182', 'game.en.183', 'game.en.184', 'game.en.185', 'game.en.186', 'game.en.187', 'game.en.188', 'game.en.189', 'game.en.190'},
		date = 20230829,
		cross = 'crossmine.en.19'
	},
	[4809] = {
		id = 4809,
		service = 'crossmine',
		servers = {'game.en.181', 'game.en.182', 'game.en.183', 'game.en.184', 'game.en.185', 'game.en.186', 'game.en.187', 'game.en.188', 'game.en.189', 'game.en.190'},
		date = 20230905,
		cross = 'crossmine.en.19'
	},
	[4810] = {
		id = 4810,
		service = 'crossmine',
		servers = {'game.en.181', 'game.en.182', 'game.en.183', 'game.en.184', 'game.en.185', 'game.en.186', 'game.en.187', 'game.en.188', 'game.en.189', 'game.en.190'},
		date = 20230919,
		cross = 'crossmine.en.19'
	},
	[4811] = {
		id = 4811,
		service = 'crossmine',
		servers = {'game.en.181', 'game.en.182', 'game.en.183', 'game.en.184', 'game.en.185', 'game.en.186', 'game.en.187', 'game.en.188', 'game.en.189', 'game.en.190'},
		date = 20230926,
		cross = 'crossmine.en.19'
	},
	[4812] = {
		id = 4812,
		service = 'crossmine',
		servers = {'game.en.181', 'game.en.182', 'game.en.183', 'game.en.184', 'game.en.185', 'game.en.186', 'game.en.187', 'game.en.188', 'game.en.189', 'game.en.190'},
		date = 20231003,
		cross = 'crossmine.en.19'
	},
	[4900] = {
		id = 4900,
		service = 'crossmine',
		servers = {'game.en.191', 'game.en.192', 'game.en.193', 'game.en.194', 'game.en.195', 'game.en.196', 'game.en.197', 'game.en.198', 'game.en.199', 'game.en.200'},
		date = 20230704,
		cross = 'crossmine.en.20'
	},
	[4901] = {
		id = 4901,
		service = 'crossmine',
		servers = {'game.en.191', 'game.en.192', 'game.en.193', 'game.en.194', 'game.en.195', 'game.en.196', 'game.en.197', 'game.en.198', 'game.en.199', 'game.en.200'},
		date = 20230711,
		cross = 'crossmine.en.20'
	},
	[4902] = {
		id = 4902,
		service = 'crossmine',
		servers = {'game.en.191', 'game.en.192', 'game.en.193', 'game.en.194', 'game.en.195', 'game.en.196', 'game.en.197', 'game.en.198', 'game.en.199', 'game.en.200'},
		date = 20230718,
		cross = 'crossmine.en.20'
	},
	[4903] = {
		id = 4903,
		service = 'crossmine',
		servers = {'game.en.191', 'game.en.192', 'game.en.193', 'game.en.194', 'game.en.195', 'game.en.196', 'game.en.197', 'game.en.198', 'game.en.199', 'game.en.200'},
		date = 20230725,
		cross = 'crossmine.en.20'
	},
	[4904] = {
		id = 4904,
		service = 'crossmine',
		servers = {'game.en.191', 'game.en.192', 'game.en.193', 'game.en.194', 'game.en.195', 'game.en.196', 'game.en.197', 'game.en.198', 'game.en.199', 'game.en.200'},
		date = 20230801,
		cross = 'crossmine.en.20'
	},
	[4905] = {
		id = 4905,
		service = 'crossmine',
		servers = {'game.en.191', 'game.en.192', 'game.en.193', 'game.en.194', 'game.en.195', 'game.en.196', 'game.en.197', 'game.en.198', 'game.en.199', 'game.en.200'},
		date = 20230808,
		cross = 'crossmine.en.20'
	},
	[4906] = {
		id = 4906,
		service = 'crossmine',
		servers = {'game.en.191', 'game.en.192', 'game.en.193', 'game.en.194', 'game.en.195', 'game.en.196', 'game.en.197', 'game.en.198', 'game.en.199', 'game.en.200'},
		date = 20230815,
		cross = 'crossmine.en.20'
	},
	[4907] = {
		id = 4907,
		service = 'crossmine',
		servers = {'game.en.191', 'game.en.192', 'game.en.193', 'game.en.194', 'game.en.195', 'game.en.196', 'game.en.197', 'game.en.198', 'game.en.199', 'game.en.200'},
		date = 20230822,
		cross = 'crossmine.en.20'
	},
	[4908] = {
		id = 4908,
		service = 'crossmine',
		servers = {'game.en.191', 'game.en.192', 'game.en.193', 'game.en.194', 'game.en.195', 'game.en.196', 'game.en.197', 'game.en.198', 'game.en.199', 'game.en.200'},
		date = 20230829,
		cross = 'crossmine.en.20'
	},
	[4909] = {
		id = 4909,
		service = 'crossmine',
		servers = {'game.en.191', 'game.en.192', 'game.en.193', 'game.en.194', 'game.en.195', 'game.en.196', 'game.en.197', 'game.en.198', 'game.en.199', 'game.en.200'},
		date = 20230905,
		cross = 'crossmine.en.20'
	},
	[4910] = {
		id = 4910,
		service = 'crossmine',
		servers = {'game.en.191', 'game.en.192', 'game.en.193', 'game.en.194', 'game.en.195', 'game.en.196', 'game.en.197', 'game.en.198', 'game.en.199', 'game.en.200'},
		date = 20230919,
		cross = 'crossmine.en.20'
	},
	[4911] = {
		id = 4911,
		service = 'crossmine',
		servers = {'game.en.191', 'game.en.192', 'game.en.193', 'game.en.194', 'game.en.195', 'game.en.196', 'game.en.197', 'game.en.198', 'game.en.199', 'game.en.200'},
		date = 20230926,
		cross = 'crossmine.en.20'
	},
	[4912] = {
		id = 4912,
		service = 'crossmine',
		servers = {'game.en.191', 'game.en.192', 'game.en.193', 'game.en.194', 'game.en.195', 'game.en.196', 'game.en.197', 'game.en.198', 'game.en.199', 'game.en.200'},
		date = 20231003,
		cross = 'crossmine.en.20'
	},
	[5106] = {
		id = 5106,
		service = 'onlinefight',
		servers = {'game.en.1', 'game.en.2', 'game.en.3', 'game.en.4', 'game.en.5', 'game.en.6', 'game.en.7', 'game.en.8', 'game.en.9', 'game.en.10', 'game.en.11', 'game.en.12', 'game.en.13', 'game.en.14', 'game.en.15', 'game.en.16', 'game.en.17', 'game.en.18', 'game.en.19', 'game.en.20', 'game.en.21', 'game.en.22', 'game.en.23', 'game.en.24', 'game.en.25', 'game.en.26', 'game.en.27', 'game.en.28', 'game.en.29', 'game.en.30', 'game.en.31', 'game.en.32', 'game.en.33', 'game.en.34', 'game.en.35', 'game.en.36', 'game.en.37', 'game.en.38', 'game.en.39', 'game.en.40', 'game.en.41', 'game.en.42', 'game.en.43', 'game.en.44', 'game.en.45', 'game.en.46', 'game.en.47', 'game.en.48', 'game.en.49', 'game.en.50', 'game.en.51', 'game.en.52', 'game.en.53', 'game.en.54', 'game.en.55', 'game.en.56'},
		date = 20250624,
		cross = 'onlinefight.en.1',
		endDate = 20250716
	},
	[6101] = {
		id = 6101,
		service = 'crossgym',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8', 'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16', 'gamemerge.en.17', 'gamemerge.en.18', 'game.en.181', 'game.en.182', 'game.en.183', 'game.en.184', 'game.en.185', 'game.en.186', 'game.en.187', 'game.en.188', 'game.en.189', 'game.en.190', 'game.en.191', 'game.en.192', 'game.en.193', 'game.en.194', 'game.en.195', 'game.en.196', 'game.en.197', 'game.en.198', 'game.en.199', 'game.en.200', 'game.en.201', 'game.en.202', 'game.en.203', 'game.en.204', 'game.en.205', 'game.en.206', 'game.en.207', 'game.en.208', 'game.en.209', 'game.en.210', 'game.en.211', 'game.en.212', 'game.en.213', 'game.en.214', 'game.en.215', 'game.en.216', 'game.en.217', 'game.en.218', 'game.en.219', 'game.en.220', 'game.en.221', 'game.en.222', 'game.en.223', 'game.en.224', 'game.en.225', 'game.en.226', 'game.en.227', 'game.en.228', 'game.en.229', 'game.en.230', 'game.en.231', 'game.en.232', 'game.en.233', 'game.en.234', 'game.en.235', 'game.en.236', 'game.en.237', 'game.en.238', 'game.en.239', 'game.en.240', 'game.en.241', 'game.en.242', 'game.en.243', 'game.en.244', 'game.en.245', 'game.en.246', 'game.en.247', 'game.en.248', 'game.en.249', 'game.en.250', 'game.en.251', 'game.en.252', 'game.en.253', 'game.en.254', 'game.en.255', 'game.en.256', 'game.en.257', 'game.en.258', 'game.en.259', 'game.en.260', 'game.en.261', 'game.en.262', 'game.en.263', 'game.en.264', 'game.en.265', 'game.en.266', 'game.en.267', 'game.en.268', 'game.en.269', 'game.en.270', 'game.en.272', 'game.en.273', 'game.en.274', 'game.en.275', 'game.en.276', 'game.en.277', 'game.en.278', 'game.en.279', 'game.en.280', 'game.en.281', 'game.en.282', 'game.en.283', 'game.en.284', 'game.en.285', 'game.en.286', 'game.en.287', 'game.en.288', 'game.en.289', 'game.en.290', 'game.en.291', 'game.en.292', 'game.en.293', 'game.en.294', 'game.en.295', 'game.en.296', 'game.en.297', 'game.en.298'},
		date = 20230710,
		cross = 'crossgym.en.1',
		endDate = 20230716
	},
	[6102] = {
		id = 6102,
		service = 'crossgym',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8', 'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16', 'gamemerge.en.17', 'gamemerge.en.18', 'game.en.181', 'game.en.182', 'game.en.183', 'game.en.184', 'game.en.185', 'game.en.186', 'game.en.187', 'game.en.188', 'game.en.189', 'game.en.190', 'game.en.191', 'game.en.192', 'game.en.193', 'game.en.194', 'game.en.195', 'game.en.196', 'game.en.197', 'game.en.198', 'game.en.199', 'game.en.200', 'game.en.201', 'game.en.202', 'game.en.203', 'game.en.204', 'game.en.205', 'game.en.206', 'game.en.207', 'game.en.208', 'game.en.209', 'game.en.210', 'game.en.211', 'game.en.212', 'game.en.213', 'game.en.214', 'game.en.215', 'game.en.216', 'game.en.217', 'game.en.218', 'game.en.219', 'game.en.220', 'game.en.221', 'game.en.222', 'game.en.223', 'game.en.224', 'game.en.225', 'game.en.226', 'game.en.227', 'game.en.228', 'game.en.229', 'game.en.230', 'game.en.231', 'game.en.232', 'game.en.233', 'game.en.234', 'game.en.235', 'game.en.236', 'game.en.237', 'game.en.238', 'game.en.239', 'game.en.240', 'game.en.241', 'game.en.242', 'game.en.243', 'game.en.244', 'game.en.245', 'game.en.246', 'game.en.247', 'game.en.248', 'game.en.249', 'game.en.250', 'game.en.251', 'game.en.252', 'game.en.253', 'game.en.254', 'game.en.255', 'game.en.256', 'game.en.257', 'game.en.258', 'game.en.259', 'game.en.260', 'game.en.261', 'game.en.262', 'game.en.263', 'game.en.264', 'game.en.265', 'game.en.266', 'game.en.267', 'game.en.268', 'game.en.269', 'game.en.270', 'game.en.272', 'game.en.273', 'game.en.274', 'game.en.275', 'game.en.276', 'game.en.277', 'game.en.278', 'game.en.279', 'game.en.280', 'game.en.281', 'game.en.282', 'game.en.283', 'game.en.284', 'game.en.285', 'game.en.286', 'game.en.287', 'game.en.288', 'game.en.289', 'game.en.290', 'game.en.291', 'game.en.292', 'game.en.293', 'game.en.294', 'game.en.295', 'game.en.296', 'game.en.297', 'game.en.298'},
		date = 20230717,
		cross = 'crossgym.en.1',
		endDate = 20230723
	},
	[6103] = {
		id = 6103,
		service = 'crossgym',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8', 'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16', 'gamemerge.en.17', 'gamemerge.en.18', 'game.en.181', 'game.en.182', 'game.en.183', 'game.en.184', 'game.en.185', 'game.en.186', 'game.en.187', 'game.en.188', 'game.en.189', 'game.en.190', 'game.en.191', 'game.en.192', 'game.en.193', 'game.en.194', 'game.en.195', 'game.en.196', 'game.en.197', 'game.en.198', 'game.en.199', 'game.en.200', 'game.en.201', 'game.en.202', 'game.en.203', 'game.en.204', 'game.en.205', 'game.en.206', 'game.en.207', 'game.en.208', 'game.en.209', 'game.en.210', 'game.en.211', 'game.en.212', 'game.en.213', 'game.en.214', 'game.en.215', 'game.en.216', 'game.en.217', 'game.en.218', 'game.en.219', 'game.en.220', 'game.en.221', 'game.en.222', 'game.en.223', 'game.en.224', 'game.en.225', 'game.en.226', 'game.en.227', 'game.en.228', 'game.en.229', 'game.en.230', 'game.en.231', 'game.en.232', 'game.en.233', 'game.en.234', 'game.en.235', 'game.en.236', 'game.en.237', 'game.en.238', 'game.en.239', 'game.en.240', 'game.en.241', 'game.en.242', 'game.en.243', 'game.en.244', 'game.en.245', 'game.en.246', 'game.en.247', 'game.en.248', 'game.en.249', 'game.en.250', 'game.en.251', 'game.en.252', 'game.en.253', 'game.en.254', 'game.en.255', 'game.en.256', 'game.en.257', 'game.en.258', 'game.en.259', 'game.en.260', 'game.en.261', 'game.en.262', 'game.en.263', 'game.en.264', 'game.en.265', 'game.en.266', 'game.en.267', 'game.en.268', 'game.en.269', 'game.en.270', 'game.en.272', 'game.en.273', 'game.en.274', 'game.en.275', 'game.en.276', 'game.en.277', 'game.en.278', 'game.en.279', 'game.en.280', 'game.en.281', 'game.en.282', 'game.en.283', 'game.en.284', 'game.en.285', 'game.en.286', 'game.en.287', 'game.en.288', 'game.en.289', 'game.en.290', 'game.en.291', 'game.en.292', 'game.en.293', 'game.en.294', 'game.en.295', 'game.en.296', 'game.en.297', 'game.en.298'},
		date = 20230724,
		cross = 'crossgym.en.1',
		endDate = 20230730
	},
	[6104] = {
		id = 6104,
		service = 'crossgym',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8', 'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16', 'gamemerge.en.17', 'gamemerge.en.18', 'game.en.181', 'game.en.182', 'game.en.183', 'game.en.184', 'game.en.185', 'game.en.186', 'game.en.187', 'game.en.188', 'game.en.189', 'game.en.190', 'game.en.191', 'game.en.192', 'game.en.193', 'game.en.194', 'game.en.195', 'game.en.196', 'game.en.197', 'game.en.198', 'game.en.199', 'game.en.200', 'game.en.201', 'game.en.202', 'game.en.203', 'game.en.204', 'game.en.205', 'game.en.206', 'game.en.207', 'game.en.208', 'game.en.209', 'game.en.210', 'game.en.211', 'game.en.212', 'game.en.213', 'game.en.214', 'game.en.215', 'game.en.216', 'game.en.217', 'game.en.218', 'game.en.219', 'game.en.220', 'game.en.221', 'game.en.222', 'game.en.223', 'game.en.224', 'game.en.225', 'game.en.226', 'game.en.227', 'game.en.228', 'game.en.229', 'game.en.230', 'game.en.231', 'game.en.232', 'game.en.233', 'game.en.234', 'game.en.235', 'game.en.236', 'game.en.237', 'game.en.238', 'game.en.239', 'game.en.240', 'game.en.241', 'game.en.242', 'game.en.243', 'game.en.244', 'game.en.245', 'game.en.246', 'game.en.247', 'game.en.248', 'game.en.249', 'game.en.250', 'game.en.251', 'game.en.252', 'game.en.253', 'game.en.254', 'game.en.255', 'game.en.256', 'game.en.257', 'game.en.258', 'game.en.259', 'game.en.260', 'game.en.261', 'game.en.262', 'game.en.263', 'game.en.264', 'game.en.265', 'game.en.266', 'game.en.267', 'game.en.268', 'game.en.269', 'game.en.270', 'game.en.272', 'game.en.273', 'game.en.274', 'game.en.275', 'game.en.276', 'game.en.277', 'game.en.278', 'game.en.279', 'game.en.280', 'game.en.281', 'game.en.282', 'game.en.283', 'game.en.284', 'game.en.285', 'game.en.286', 'game.en.287', 'game.en.288', 'game.en.289', 'game.en.290', 'game.en.291', 'game.en.292', 'game.en.293', 'game.en.294', 'game.en.295', 'game.en.296', 'game.en.297', 'game.en.298'},
		date = 20230731,
		cross = 'crossgym.en.1',
		endDate = 20230806
	},
	[6105] = {
		id = 6105,
		service = 'crossgym',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8', 'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16', 'gamemerge.en.17', 'gamemerge.en.18', 'game.en.181', 'game.en.182', 'game.en.183', 'game.en.184', 'game.en.185', 'game.en.186', 'game.en.187', 'game.en.188', 'game.en.189', 'game.en.190', 'game.en.191', 'game.en.192', 'game.en.193', 'game.en.194', 'game.en.195', 'game.en.196', 'game.en.197', 'game.en.198', 'game.en.199', 'game.en.200', 'game.en.201', 'game.en.202', 'game.en.203', 'game.en.204', 'game.en.205', 'game.en.206', 'game.en.207', 'game.en.208', 'game.en.209', 'game.en.210', 'game.en.211', 'game.en.212', 'game.en.213', 'game.en.214', 'game.en.215', 'game.en.216', 'game.en.217', 'game.en.218', 'game.en.219', 'game.en.220', 'game.en.221', 'game.en.222', 'game.en.223', 'game.en.224', 'game.en.225', 'game.en.226', 'game.en.227', 'game.en.228', 'game.en.229', 'game.en.230', 'game.en.231', 'game.en.232', 'game.en.233', 'game.en.234', 'game.en.235', 'game.en.236', 'game.en.237', 'game.en.238', 'game.en.239', 'game.en.240', 'game.en.241', 'game.en.242', 'game.en.243', 'game.en.244', 'game.en.245', 'game.en.246', 'game.en.247', 'game.en.248', 'game.en.249', 'game.en.250', 'game.en.251', 'game.en.252', 'game.en.253', 'game.en.254', 'game.en.255', 'game.en.256', 'game.en.257', 'game.en.258', 'game.en.259', 'game.en.260', 'game.en.261', 'game.en.262', 'game.en.263', 'game.en.264', 'game.en.265', 'game.en.266', 'game.en.267', 'game.en.268', 'game.en.269', 'game.en.270', 'game.en.272', 'game.en.273', 'game.en.274', 'game.en.275', 'game.en.276', 'game.en.277', 'game.en.278', 'game.en.279', 'game.en.280', 'game.en.281', 'game.en.282', 'game.en.283', 'game.en.284', 'game.en.285', 'game.en.286', 'game.en.287', 'game.en.288', 'game.en.289', 'game.en.290', 'game.en.291', 'game.en.292', 'game.en.293', 'game.en.294', 'game.en.295', 'game.en.296', 'game.en.297', 'game.en.298'},
		date = 20230807,
		cross = 'crossgym.en.1',
		endDate = 20230813
	},
	[6106] = {
		id = 6106,
		service = 'crossgym',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8', 'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16', 'gamemerge.en.17', 'gamemerge.en.18', 'game.en.181', 'game.en.182', 'game.en.183', 'game.en.184', 'game.en.185', 'game.en.186', 'game.en.187', 'game.en.188', 'game.en.189', 'game.en.190', 'game.en.191', 'game.en.192', 'game.en.193', 'game.en.194', 'game.en.195', 'game.en.196', 'game.en.197', 'game.en.198', 'game.en.199', 'game.en.200', 'game.en.201', 'game.en.202', 'game.en.203', 'game.en.204', 'game.en.205', 'game.en.206', 'game.en.207', 'game.en.208', 'game.en.209', 'game.en.210', 'game.en.211', 'game.en.212', 'game.en.213', 'game.en.214', 'game.en.215', 'game.en.216', 'game.en.217', 'game.en.218', 'game.en.219', 'game.en.220', 'game.en.221', 'game.en.222', 'game.en.223', 'game.en.224', 'game.en.225', 'game.en.226', 'game.en.227', 'game.en.228', 'game.en.229', 'game.en.230', 'game.en.231', 'game.en.232', 'game.en.233', 'game.en.234', 'game.en.235', 'game.en.236', 'game.en.237', 'game.en.238', 'game.en.239', 'game.en.240', 'game.en.241', 'game.en.242', 'game.en.243', 'game.en.244', 'game.en.245', 'game.en.246', 'game.en.247', 'game.en.248', 'game.en.249', 'game.en.250', 'game.en.251', 'game.en.252', 'game.en.253', 'game.en.254', 'game.en.255', 'game.en.256', 'game.en.257', 'game.en.258', 'game.en.259', 'game.en.260', 'game.en.261', 'game.en.262', 'game.en.263', 'game.en.264', 'game.en.265', 'game.en.266', 'game.en.267', 'game.en.268', 'game.en.269', 'game.en.270', 'game.en.272', 'game.en.273', 'game.en.274', 'game.en.275', 'game.en.276', 'game.en.277', 'game.en.278', 'game.en.279', 'game.en.280', 'game.en.281', 'game.en.282', 'game.en.283', 'game.en.284', 'game.en.285', 'game.en.286', 'game.en.287', 'game.en.288', 'game.en.289', 'game.en.290', 'game.en.291', 'game.en.292', 'game.en.293', 'game.en.294', 'game.en.295', 'game.en.296', 'game.en.297', 'game.en.298'},
		date = 20230814,
		cross = 'crossgym.en.1',
		endDate = 20230820
	},
	[6107] = {
		id = 6107,
		service = 'crossgym',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8', 'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16', 'gamemerge.en.17', 'gamemerge.en.18', 'game.en.181', 'game.en.182', 'game.en.183', 'game.en.184', 'game.en.185', 'game.en.186', 'game.en.187', 'game.en.188', 'game.en.189', 'game.en.190', 'game.en.191', 'game.en.192', 'game.en.193', 'game.en.194', 'game.en.195', 'game.en.196', 'game.en.197', 'game.en.198', 'game.en.199', 'game.en.200', 'game.en.201', 'game.en.202', 'game.en.203', 'game.en.204', 'game.en.205', 'game.en.206', 'game.en.207', 'game.en.208', 'game.en.209', 'game.en.210', 'game.en.211', 'game.en.212', 'game.en.213', 'game.en.214', 'game.en.215', 'game.en.216', 'game.en.217', 'game.en.218', 'game.en.219', 'game.en.220', 'game.en.221', 'game.en.222', 'game.en.223', 'game.en.224', 'game.en.225', 'game.en.226', 'game.en.227', 'game.en.228', 'game.en.229', 'game.en.230', 'game.en.231', 'game.en.232', 'game.en.233', 'game.en.234', 'game.en.235', 'game.en.236', 'game.en.237', 'game.en.238', 'game.en.239', 'game.en.240', 'game.en.241', 'game.en.242', 'game.en.243', 'game.en.244', 'game.en.245', 'game.en.246', 'game.en.247', 'game.en.248', 'game.en.249', 'game.en.250', 'game.en.251', 'game.en.252', 'game.en.253', 'game.en.254', 'game.en.255', 'game.en.256', 'game.en.257', 'game.en.258', 'game.en.259', 'game.en.260', 'game.en.261', 'game.en.262', 'game.en.263', 'game.en.264', 'game.en.265', 'game.en.266', 'game.en.267', 'game.en.268', 'game.en.269', 'game.en.270', 'game.en.272', 'game.en.273', 'game.en.274', 'game.en.275', 'game.en.276', 'game.en.277', 'game.en.278', 'game.en.279', 'game.en.280', 'game.en.281', 'game.en.282', 'game.en.283', 'game.en.284', 'game.en.285', 'game.en.286', 'game.en.287', 'game.en.288', 'game.en.289', 'game.en.290', 'game.en.291', 'game.en.292', 'game.en.293', 'game.en.294', 'game.en.295', 'game.en.296', 'game.en.297', 'game.en.298'},
		date = 20230821,
		cross = 'crossgym.en.1',
		endDate = 20230827
	},
	[6108] = {
		id = 6108,
		service = 'crossgym',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8', 'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16', 'gamemerge.en.17', 'gamemerge.en.18', 'game.en.181', 'game.en.182', 'game.en.183', 'game.en.184', 'game.en.185', 'game.en.186', 'game.en.187', 'game.en.188', 'game.en.189', 'game.en.190', 'game.en.191', 'game.en.192', 'game.en.193', 'game.en.194', 'game.en.195', 'game.en.196', 'game.en.197', 'game.en.198', 'game.en.199', 'game.en.200', 'game.en.201', 'game.en.202', 'game.en.203', 'game.en.204', 'game.en.205', 'game.en.206', 'game.en.207', 'game.en.208', 'game.en.209', 'game.en.210', 'game.en.211', 'game.en.212', 'game.en.213', 'game.en.214', 'game.en.215', 'game.en.216', 'game.en.217', 'game.en.218', 'game.en.219', 'game.en.220', 'game.en.221', 'game.en.222', 'game.en.223', 'game.en.224', 'game.en.225', 'game.en.226', 'game.en.227', 'game.en.228', 'game.en.229', 'game.en.230', 'game.en.231', 'game.en.232', 'game.en.233', 'game.en.234', 'game.en.235', 'game.en.236', 'game.en.237', 'game.en.238', 'game.en.239', 'game.en.240', 'game.en.241', 'game.en.242', 'game.en.243', 'game.en.244', 'game.en.245', 'game.en.246', 'game.en.247', 'game.en.248', 'game.en.249', 'game.en.250', 'game.en.251', 'game.en.252', 'game.en.253', 'game.en.254', 'game.en.255', 'game.en.256', 'game.en.257', 'game.en.258', 'game.en.259', 'game.en.260', 'game.en.261', 'game.en.262', 'game.en.263', 'game.en.264', 'game.en.265', 'game.en.266', 'game.en.267', 'game.en.268', 'game.en.269', 'game.en.270', 'game.en.272', 'game.en.273', 'game.en.274', 'game.en.275', 'game.en.276', 'game.en.277', 'game.en.278', 'game.en.279', 'game.en.280', 'game.en.281', 'game.en.282', 'game.en.283', 'game.en.284', 'game.en.285', 'game.en.286', 'game.en.287', 'game.en.288', 'game.en.289', 'game.en.290', 'game.en.291', 'game.en.292', 'game.en.293', 'game.en.294', 'game.en.295', 'game.en.296', 'game.en.297', 'game.en.298'},
		date = 20230828,
		cross = 'crossgym.en.1',
		endDate = 20230903
	},
	[6109] = {
		id = 6109,
		service = 'crossgym',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8', 'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16', 'gamemerge.en.17', 'gamemerge.en.18', 'game.en.181', 'game.en.182', 'game.en.183', 'game.en.184', 'game.en.185', 'game.en.186', 'game.en.187', 'game.en.188', 'game.en.189', 'game.en.190', 'game.en.191', 'game.en.192', 'game.en.193', 'game.en.194', 'game.en.195', 'game.en.196', 'game.en.197', 'game.en.198', 'game.en.199', 'game.en.200', 'game.en.201', 'game.en.202', 'game.en.203', 'game.en.204', 'game.en.205', 'game.en.206', 'game.en.207', 'game.en.208', 'game.en.209', 'game.en.210', 'game.en.211', 'game.en.212', 'game.en.213', 'game.en.214', 'game.en.215', 'game.en.216', 'game.en.217', 'game.en.218', 'game.en.219', 'game.en.220', 'game.en.221', 'game.en.222', 'game.en.223', 'game.en.224', 'game.en.225', 'game.en.226', 'game.en.227', 'game.en.228', 'game.en.229', 'game.en.230', 'game.en.231', 'game.en.232', 'game.en.233', 'game.en.234', 'game.en.235', 'game.en.236', 'game.en.237', 'game.en.238', 'game.en.239', 'game.en.240', 'game.en.241', 'game.en.242', 'game.en.243', 'game.en.244', 'game.en.245', 'game.en.246', 'game.en.247', 'game.en.248', 'game.en.249', 'game.en.250', 'game.en.251', 'game.en.252', 'game.en.253', 'game.en.254', 'game.en.255', 'game.en.256', 'game.en.257', 'game.en.258', 'game.en.259', 'game.en.260', 'game.en.261', 'game.en.262', 'game.en.263', 'game.en.264', 'game.en.265', 'game.en.266', 'game.en.267', 'game.en.268', 'game.en.269', 'game.en.270', 'game.en.272', 'game.en.273', 'game.en.274', 'game.en.275', 'game.en.276', 'game.en.277', 'game.en.278', 'game.en.279', 'game.en.280', 'game.en.281', 'game.en.282', 'game.en.283', 'game.en.284', 'game.en.285', 'game.en.286', 'game.en.287', 'game.en.288', 'game.en.289', 'game.en.290', 'game.en.291', 'game.en.292', 'game.en.293', 'game.en.294', 'game.en.295', 'game.en.296', 'game.en.297', 'game.en.298'},
		date = 20230904,
		cross = 'crossgym.en.1',
		endDate = 20230910
	},
	[6110] = {
		id = 6110,
		service = 'crossgym',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8', 'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16', 'gamemerge.en.17', 'gamemerge.en.18', 'game.en.181', 'game.en.182', 'game.en.183', 'game.en.184', 'game.en.185', 'game.en.186', 'game.en.187', 'game.en.188', 'game.en.189', 'game.en.190', 'game.en.191', 'game.en.192', 'game.en.193', 'game.en.194', 'game.en.195', 'game.en.196', 'game.en.197', 'game.en.198', 'game.en.199', 'game.en.200', 'game.en.201', 'game.en.202', 'game.en.203', 'game.en.204', 'game.en.205', 'game.en.206', 'game.en.207', 'game.en.208', 'game.en.209', 'game.en.210', 'game.en.211', 'game.en.212', 'game.en.213', 'game.en.214', 'game.en.215', 'game.en.216', 'game.en.217', 'game.en.218', 'game.en.219', 'game.en.220', 'game.en.221', 'game.en.222', 'game.en.223', 'game.en.224', 'game.en.225', 'game.en.226', 'game.en.227', 'game.en.228', 'game.en.229', 'game.en.230', 'game.en.231', 'game.en.232', 'game.en.233', 'game.en.234', 'game.en.235', 'game.en.236', 'game.en.237', 'game.en.238', 'game.en.239', 'game.en.240', 'game.en.241', 'game.en.242', 'game.en.243', 'game.en.244', 'game.en.245', 'game.en.246', 'game.en.247', 'game.en.248', 'game.en.249', 'game.en.250', 'game.en.251', 'game.en.252', 'game.en.253', 'game.en.254', 'game.en.255', 'game.en.256', 'game.en.257', 'game.en.258', 'game.en.259', 'game.en.260', 'game.en.261', 'game.en.262', 'game.en.263', 'game.en.264', 'game.en.265', 'game.en.266', 'game.en.267', 'game.en.268', 'game.en.269', 'game.en.270', 'game.en.272', 'game.en.273', 'game.en.274', 'game.en.275', 'game.en.276', 'game.en.277', 'game.en.278', 'game.en.279', 'game.en.280', 'game.en.281', 'game.en.282', 'game.en.283', 'game.en.284', 'game.en.285', 'game.en.286', 'game.en.287', 'game.en.288', 'game.en.289', 'game.en.290', 'game.en.291', 'game.en.292', 'game.en.293', 'game.en.294', 'game.en.295', 'game.en.296', 'game.en.297', 'game.en.298'},
		date = 20230911,
		cross = 'crossgym.en.1',
		endDate = 20230917
	},
	[6111] = {
		id = 6111,
		service = 'crossgym',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8', 'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16', 'gamemerge.en.17', 'gamemerge.en.18', 'game.en.181', 'game.en.182', 'game.en.183', 'game.en.184', 'game.en.185', 'game.en.186', 'game.en.187', 'game.en.188', 'game.en.189', 'game.en.190', 'game.en.191', 'game.en.192', 'game.en.193', 'game.en.194', 'game.en.195', 'game.en.196', 'game.en.197', 'game.en.198', 'game.en.199', 'game.en.200', 'game.en.201', 'game.en.202', 'game.en.203', 'game.en.204', 'game.en.205', 'game.en.206', 'game.en.207', 'game.en.208', 'game.en.209', 'game.en.210', 'game.en.211', 'game.en.212', 'game.en.213', 'game.en.214', 'game.en.215', 'game.en.216', 'game.en.217', 'game.en.218', 'game.en.219', 'game.en.220', 'game.en.221', 'game.en.222', 'game.en.223', 'game.en.224', 'game.en.225', 'game.en.226', 'game.en.227', 'game.en.228', 'game.en.229', 'game.en.230', 'game.en.231', 'game.en.232', 'game.en.233', 'game.en.234', 'game.en.235', 'game.en.236', 'game.en.237', 'game.en.238', 'game.en.239', 'game.en.240', 'game.en.241', 'game.en.242', 'game.en.243', 'game.en.244', 'game.en.245', 'game.en.246', 'game.en.247', 'game.en.248', 'game.en.249', 'game.en.250', 'game.en.251', 'game.en.252', 'game.en.253', 'game.en.254', 'game.en.255', 'game.en.256', 'game.en.257', 'game.en.258', 'game.en.259', 'game.en.260', 'game.en.261', 'game.en.262', 'game.en.263', 'game.en.264', 'game.en.265', 'game.en.266', 'game.en.267', 'game.en.268', 'game.en.269', 'game.en.270', 'game.en.272', 'game.en.273', 'game.en.274', 'game.en.275', 'game.en.276', 'game.en.277', 'game.en.278', 'game.en.279', 'game.en.280', 'game.en.281', 'game.en.282', 'game.en.283', 'game.en.284', 'game.en.285', 'game.en.286', 'game.en.287', 'game.en.288', 'game.en.289', 'game.en.290', 'game.en.291', 'game.en.292', 'game.en.293', 'game.en.294', 'game.en.295', 'game.en.296', 'game.en.297', 'game.en.298'},
		date = 20230918,
		cross = 'crossgym.en.1',
		endDate = 20230924
	},
	[6112] = {
		id = 6112,
		service = 'crossgym',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8', 'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16', 'gamemerge.en.17', 'gamemerge.en.18', 'game.en.181', 'game.en.182', 'game.en.183', 'game.en.184', 'game.en.185', 'game.en.186', 'game.en.187', 'game.en.188', 'game.en.189', 'game.en.190', 'game.en.191', 'game.en.192', 'game.en.193', 'game.en.194', 'game.en.195', 'game.en.196', 'game.en.197', 'game.en.198', 'game.en.199', 'game.en.200', 'game.en.201', 'game.en.202', 'game.en.203', 'game.en.204', 'game.en.205', 'game.en.206', 'game.en.207', 'game.en.208', 'game.en.209', 'game.en.210', 'game.en.211', 'game.en.212', 'game.en.213', 'game.en.214', 'game.en.215', 'game.en.216', 'game.en.217', 'game.en.218', 'game.en.219', 'game.en.220', 'game.en.221', 'game.en.222', 'game.en.223', 'game.en.224', 'game.en.225', 'game.en.226', 'game.en.227', 'game.en.228', 'game.en.229', 'game.en.230', 'game.en.231', 'game.en.232', 'game.en.233', 'game.en.234', 'game.en.235', 'game.en.236', 'game.en.237', 'game.en.238', 'game.en.239', 'game.en.240', 'game.en.241', 'game.en.242', 'game.en.243', 'game.en.244', 'game.en.245', 'game.en.246', 'game.en.247', 'game.en.248', 'game.en.249', 'game.en.250', 'game.en.251', 'game.en.252', 'game.en.253', 'game.en.254', 'game.en.255', 'game.en.256', 'game.en.257', 'game.en.258', 'game.en.259', 'game.en.260', 'game.en.261', 'game.en.262', 'game.en.263', 'game.en.264', 'game.en.265', 'game.en.266', 'game.en.267', 'game.en.268', 'game.en.269', 'game.en.270', 'game.en.272', 'game.en.273', 'game.en.274', 'game.en.275', 'game.en.276', 'game.en.277', 'game.en.278', 'game.en.279', 'game.en.280', 'game.en.281', 'game.en.282', 'game.en.283', 'game.en.284', 'game.en.285', 'game.en.286', 'game.en.287', 'game.en.288', 'game.en.289', 'game.en.290', 'game.en.291', 'game.en.292', 'game.en.293', 'game.en.294', 'game.en.295', 'game.en.296', 'game.en.297', 'game.en.298'},
		date = 20230925,
		cross = 'crossgym.en.1',
		endDate = 20231001
	},
	[6113] = {
		id = 6113,
		service = 'crossgym',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8', 'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16', 'gamemerge.en.17', 'gamemerge.en.18', 'game.en.181', 'game.en.182', 'game.en.183', 'game.en.184', 'game.en.185', 'game.en.186', 'game.en.187', 'game.en.188', 'game.en.189', 'game.en.190', 'game.en.191', 'game.en.192', 'game.en.193', 'game.en.194', 'game.en.195', 'game.en.196', 'game.en.197', 'game.en.198', 'game.en.199', 'game.en.200', 'game.en.201', 'game.en.202', 'game.en.203', 'game.en.204', 'game.en.205', 'game.en.206', 'game.en.207', 'game.en.208', 'game.en.209', 'game.en.210', 'game.en.211', 'game.en.212', 'game.en.213', 'game.en.214', 'game.en.215', 'game.en.216', 'game.en.217', 'game.en.218', 'game.en.219', 'game.en.220', 'game.en.221', 'game.en.222', 'game.en.223', 'game.en.224', 'game.en.225', 'game.en.226', 'game.en.227', 'game.en.228', 'game.en.229', 'game.en.230', 'game.en.231', 'game.en.232', 'game.en.233', 'game.en.234', 'game.en.235', 'game.en.236', 'game.en.237', 'game.en.238', 'game.en.239', 'game.en.240', 'game.en.241', 'game.en.242', 'game.en.243', 'game.en.244', 'game.en.245', 'game.en.246', 'game.en.247', 'game.en.248', 'game.en.249', 'game.en.250', 'game.en.251', 'game.en.252', 'game.en.253', 'game.en.254', 'game.en.255', 'game.en.256', 'game.en.257', 'game.en.258', 'game.en.259', 'game.en.260', 'game.en.261', 'game.en.262', 'game.en.263', 'game.en.264', 'game.en.265', 'game.en.266', 'game.en.267', 'game.en.268', 'game.en.269', 'game.en.270', 'game.en.272', 'game.en.273', 'game.en.274', 'game.en.275', 'game.en.276', 'game.en.277', 'game.en.278', 'game.en.279', 'game.en.280', 'game.en.281', 'game.en.282', 'game.en.283', 'game.en.284', 'game.en.285', 'game.en.286', 'game.en.287', 'game.en.288', 'game.en.289', 'game.en.290', 'game.en.291', 'game.en.292', 'game.en.293', 'game.en.294', 'game.en.295', 'game.en.296', 'game.en.297', 'game.en.298'},
		date = 20231002,
		cross = 'crossgym.en.1',
		endDate = 20231008
	},
	[7101] = {
		id = 7101,
		service = 'crossunionfight',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8', 'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16', 'gamemerge.en.17', 'gamemerge.en.18'},
		date = 20230724,
		cross = 'crossunionfight.en.1'
	},
	[7102] = {
		id = 7102,
		service = 'crossunionfight',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8', 'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16', 'gamemerge.en.17', 'gamemerge.en.18'},
		date = 20230824,
		cross = 'crossunionfight.en.1'
	},
	[7103] = {
		id = 7103,
		service = 'crossunionfight',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8', 'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16', 'gamemerge.en.17', 'gamemerge.en.18'},
		date = 20230924,
		cross = 'crossunionfight.en.1'
	},
	[7104] = {
		id = 7104,
		service = 'crossunionfight',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8', 'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16', 'gamemerge.en.17', 'gamemerge.en.18'},
		date = 20231024,
		cross = 'crossunionfight.en.1'
	},
	[8101] = {
		id = 8101,
		service = 'crossarena',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8', 'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16', 'gamemerge.en.17', 'gamemerge.en.18', 'game.en.181', 'game.en.182', 'game.en.183', 'game.en.184', 'game.en.185', 'game.en.186', 'game.en.187', 'game.en.188', 'game.en.189', 'game.en.190', 'game.en.191', 'game.en.192', 'game.en.193', 'game.en.194', 'game.en.195', 'game.en.196', 'game.en.197', 'game.en.198', 'game.en.199', 'game.en.200', 'game.en.201', 'game.en.202', 'game.en.203', 'game.en.204', 'game.en.205', 'game.en.206', 'game.en.207', 'game.en.208', 'game.en.209', 'game.en.210', 'game.en.211', 'game.en.212', 'game.en.213', 'game.en.214', 'game.en.215', 'game.en.216', 'game.en.217', 'game.en.218', 'game.en.219', 'game.en.220', 'game.en.221', 'game.en.222', 'game.en.223', 'game.en.224', 'game.en.225', 'game.en.226', 'game.en.227', 'game.en.228', 'game.en.229', 'game.en.230', 'game.en.231', 'game.en.232', 'game.en.233', 'game.en.234', 'game.en.235', 'game.en.236', 'game.en.237', 'game.en.238', 'game.en.239', 'game.en.240', 'game.en.241', 'game.en.242', 'game.en.243', 'game.en.244', 'game.en.245', 'game.en.246', 'game.en.247', 'game.en.248', 'game.en.249', 'game.en.250', 'game.en.251', 'game.en.252', 'game.en.253', 'game.en.254', 'game.en.255', 'game.en.256', 'game.en.257', 'game.en.258', 'game.en.259', 'game.en.260', 'game.en.261', 'game.en.262', 'game.en.263', 'game.en.264', 'game.en.265', 'game.en.266', 'game.en.267', 'game.en.268', 'game.en.269', 'game.en.270', 'game.en.272', 'game.en.273', 'game.en.274', 'game.en.275', 'game.en.276', 'game.en.277', 'game.en.278', 'game.en.279', 'game.en.280', 'game.en.281', 'game.en.282', 'game.en.283', 'game.en.284', 'game.en.285', 'game.en.286', 'game.en.287', 'game.en.288', 'game.en.289', 'game.en.290', 'game.en.291', 'game.en.292', 'game.en.293', 'game.en.294', 'game.en.295', 'game.en.296', 'game.en.297', 'game.en.298'},
		date = 20230324,
		cross = 'crossarena.en.1'
	},
	[8102] = {
		id = 8102,
		service = 'crossarena',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8', 'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16', 'gamemerge.en.17', 'gamemerge.en.18', 'game.en.181', 'game.en.182', 'game.en.183', 'game.en.184', 'game.en.185', 'game.en.186', 'game.en.187', 'game.en.188', 'game.en.189', 'game.en.190', 'game.en.191', 'game.en.192', 'game.en.193', 'game.en.194', 'game.en.195', 'game.en.196', 'game.en.197', 'game.en.198', 'game.en.199', 'game.en.200', 'game.en.201', 'game.en.202', 'game.en.203', 'game.en.204', 'game.en.205', 'game.en.206', 'game.en.207', 'game.en.208', 'game.en.209', 'game.en.210', 'game.en.211', 'game.en.212', 'game.en.213', 'game.en.214', 'game.en.215', 'game.en.216', 'game.en.217', 'game.en.218', 'game.en.219', 'game.en.220', 'game.en.221', 'game.en.222', 'game.en.223', 'game.en.224', 'game.en.225', 'game.en.226', 'game.en.227', 'game.en.228', 'game.en.229', 'game.en.230', 'game.en.231', 'game.en.232', 'game.en.233', 'game.en.234', 'game.en.235', 'game.en.236', 'game.en.237', 'game.en.238', 'game.en.239', 'game.en.240', 'game.en.241', 'game.en.242', 'game.en.243', 'game.en.244', 'game.en.245', 'game.en.246', 'game.en.247', 'game.en.248', 'game.en.249', 'game.en.250', 'game.en.251', 'game.en.252', 'game.en.253', 'game.en.254', 'game.en.255', 'game.en.256', 'game.en.257', 'game.en.258', 'game.en.259', 'game.en.260', 'game.en.261', 'game.en.262', 'game.en.263', 'game.en.264', 'game.en.265', 'game.en.266', 'game.en.267', 'game.en.268', 'game.en.269', 'game.en.270', 'game.en.272', 'game.en.273', 'game.en.274', 'game.en.275', 'game.en.276', 'game.en.277', 'game.en.278', 'game.en.279', 'game.en.280', 'game.en.281', 'game.en.282', 'game.en.283', 'game.en.284', 'game.en.285', 'game.en.286', 'game.en.287', 'game.en.288', 'game.en.289', 'game.en.290', 'game.en.291', 'game.en.292', 'game.en.293', 'game.en.294', 'game.en.295', 'game.en.296', 'game.en.297', 'game.en.298'},
		date = 20230424,
		cross = 'crossarena.en.1'
	},
	[8103] = {
		id = 8103,
		service = 'crossarena',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8', 'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16', 'gamemerge.en.17', 'gamemerge.en.18', 'game.en.181', 'game.en.182', 'game.en.183', 'game.en.184', 'game.en.185', 'game.en.186', 'game.en.187', 'game.en.188', 'game.en.189', 'game.en.190', 'game.en.191', 'game.en.192', 'game.en.193', 'game.en.194', 'game.en.195', 'game.en.196', 'game.en.197', 'game.en.198', 'game.en.199', 'game.en.200', 'game.en.201', 'game.en.202', 'game.en.203', 'game.en.204', 'game.en.205', 'game.en.206', 'game.en.207', 'game.en.208', 'game.en.209', 'game.en.210', 'game.en.211', 'game.en.212', 'game.en.213', 'game.en.214', 'game.en.215', 'game.en.216', 'game.en.217', 'game.en.218', 'game.en.219', 'game.en.220', 'game.en.221', 'game.en.222', 'game.en.223', 'game.en.224', 'game.en.225', 'game.en.226', 'game.en.227', 'game.en.228', 'game.en.229', 'game.en.230', 'game.en.231', 'game.en.232', 'game.en.233', 'game.en.234', 'game.en.235', 'game.en.236', 'game.en.237', 'game.en.238', 'game.en.239', 'game.en.240', 'game.en.241', 'game.en.242', 'game.en.243', 'game.en.244', 'game.en.245', 'game.en.246', 'game.en.247', 'game.en.248', 'game.en.249', 'game.en.250', 'game.en.251', 'game.en.252', 'game.en.253', 'game.en.254', 'game.en.255', 'game.en.256', 'game.en.257', 'game.en.258', 'game.en.259', 'game.en.260', 'game.en.261', 'game.en.262', 'game.en.263', 'game.en.264', 'game.en.265', 'game.en.266', 'game.en.267', 'game.en.268', 'game.en.269', 'game.en.270', 'game.en.272', 'game.en.273', 'game.en.274', 'game.en.275', 'game.en.276', 'game.en.277', 'game.en.278', 'game.en.279', 'game.en.280', 'game.en.281', 'game.en.282', 'game.en.283', 'game.en.284', 'game.en.285', 'game.en.286', 'game.en.287', 'game.en.288', 'game.en.289', 'game.en.290', 'game.en.291', 'game.en.292', 'game.en.293', 'game.en.294', 'game.en.295', 'game.en.296', 'game.en.297', 'game.en.298'},
		date = 20230624,
		cross = 'crossarena.en.1'
	},
	[8104] = {
		id = 8104,
		service = 'crossarena',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8', 'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16', 'gamemerge.en.17', 'gamemerge.en.18', 'game.en.181', 'game.en.182', 'game.en.183', 'game.en.184', 'game.en.185', 'game.en.186', 'game.en.187', 'game.en.188', 'game.en.189', 'game.en.190', 'game.en.191', 'game.en.192', 'game.en.193', 'game.en.194', 'game.en.195', 'game.en.196', 'game.en.197', 'game.en.198', 'game.en.199', 'game.en.200', 'game.en.201', 'game.en.202', 'game.en.203', 'game.en.204', 'game.en.205', 'game.en.206', 'game.en.207', 'game.en.208', 'game.en.209', 'game.en.210', 'game.en.211', 'game.en.212', 'game.en.213', 'game.en.214', 'game.en.215', 'game.en.216', 'game.en.217', 'game.en.218', 'game.en.219', 'game.en.220', 'game.en.221', 'game.en.222', 'game.en.223', 'game.en.224', 'game.en.225', 'game.en.226', 'game.en.227', 'game.en.228', 'game.en.229', 'game.en.230', 'game.en.231', 'game.en.232', 'game.en.233', 'game.en.234', 'game.en.235', 'game.en.236', 'game.en.237', 'game.en.238', 'game.en.239', 'game.en.240', 'game.en.241', 'game.en.242', 'game.en.243', 'game.en.244', 'game.en.245', 'game.en.246', 'game.en.247', 'game.en.248', 'game.en.249', 'game.en.250', 'game.en.251', 'game.en.252', 'game.en.253', 'game.en.254', 'game.en.255', 'game.en.256', 'game.en.257', 'game.en.258', 'game.en.259', 'game.en.260', 'game.en.261', 'game.en.262', 'game.en.263', 'game.en.264', 'game.en.265', 'game.en.266', 'game.en.267', 'game.en.268', 'game.en.269', 'game.en.270', 'game.en.272', 'game.en.273', 'game.en.274', 'game.en.275', 'game.en.276', 'game.en.277', 'game.en.278', 'game.en.279', 'game.en.280', 'game.en.281', 'game.en.282', 'game.en.283', 'game.en.284', 'game.en.285', 'game.en.286', 'game.en.287', 'game.en.288', 'game.en.289', 'game.en.290', 'game.en.291', 'game.en.292', 'game.en.293', 'game.en.294', 'game.en.295', 'game.en.296', 'game.en.297', 'game.en.298'},
		date = 20230724,
		cross = 'crossarena.en.1'
	},
	[8105] = {
		id = 8105,
		service = 'crossarena',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8', 'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16', 'gamemerge.en.17', 'gamemerge.en.18', 'game.en.181', 'game.en.182', 'game.en.183', 'game.en.184', 'game.en.185', 'game.en.186', 'game.en.187', 'game.en.188', 'game.en.189', 'game.en.190', 'game.en.191', 'game.en.192', 'game.en.193', 'game.en.194', 'game.en.195', 'game.en.196', 'game.en.197', 'game.en.198', 'game.en.199', 'game.en.200', 'game.en.201', 'game.en.202', 'game.en.203', 'game.en.204', 'game.en.205', 'game.en.206', 'game.en.207', 'game.en.208', 'game.en.209', 'game.en.210', 'game.en.211', 'game.en.212', 'game.en.213', 'game.en.214', 'game.en.215', 'game.en.216', 'game.en.217', 'game.en.218', 'game.en.219', 'game.en.220', 'game.en.221', 'game.en.222', 'game.en.223', 'game.en.224', 'game.en.225', 'game.en.226', 'game.en.227', 'game.en.228', 'game.en.229', 'game.en.230', 'game.en.231', 'game.en.232', 'game.en.233', 'game.en.234', 'game.en.235', 'game.en.236', 'game.en.237', 'game.en.238', 'game.en.239', 'game.en.240', 'game.en.241', 'game.en.242', 'game.en.243', 'game.en.244', 'game.en.245', 'game.en.246', 'game.en.247', 'game.en.248', 'game.en.249', 'game.en.250', 'game.en.251', 'game.en.252', 'game.en.253', 'game.en.254', 'game.en.255', 'game.en.256', 'game.en.257', 'game.en.258', 'game.en.259', 'game.en.260', 'game.en.261', 'game.en.262', 'game.en.263', 'game.en.264', 'game.en.265', 'game.en.266', 'game.en.267', 'game.en.268', 'game.en.269', 'game.en.270', 'game.en.272', 'game.en.273', 'game.en.274', 'game.en.275', 'game.en.276', 'game.en.277', 'game.en.278', 'game.en.279', 'game.en.280', 'game.en.281', 'game.en.282', 'game.en.283', 'game.en.284', 'game.en.285', 'game.en.286', 'game.en.287', 'game.en.288', 'game.en.289', 'game.en.290', 'game.en.291', 'game.en.292', 'game.en.293', 'game.en.294', 'game.en.295', 'game.en.296', 'game.en.297', 'game.en.298'},
		date = 20230824,
		cross = 'crossarena.en.1'
	},
	[8106] = {
		id = 8106,
		service = 'crossarena',
		servers = {'gamemerge.en.1', 'gamemerge.en.2', 'gamemerge.en.3', 'gamemerge.en.4', 'gamemerge.en.5', 'gamemerge.en.6', 'gamemerge.en.7', 'gamemerge.en.8', 'gamemerge.en.9', 'gamemerge.en.10', 'gamemerge.en.11', 'gamemerge.en.12', 'gamemerge.en.13', 'gamemerge.en.14', 'gamemerge.en.15', 'gamemerge.en.16', 'gamemerge.en.17', 'gamemerge.en.18', 'game.en.181', 'game.en.182', 'game.en.183', 'game.en.184', 'game.en.185', 'game.en.186', 'game.en.187', 'game.en.188', 'game.en.189', 'game.en.190', 'game.en.191', 'game.en.192', 'game.en.193', 'game.en.194', 'game.en.195', 'game.en.196', 'game.en.197', 'game.en.198', 'game.en.199', 'game.en.200', 'game.en.201', 'game.en.202', 'game.en.203', 'game.en.204', 'game.en.205', 'game.en.206', 'game.en.207', 'game.en.208', 'game.en.209', 'game.en.210', 'game.en.211', 'game.en.212', 'game.en.213', 'game.en.214', 'game.en.215', 'game.en.216', 'game.en.217', 'game.en.218', 'game.en.219', 'game.en.220', 'game.en.221', 'game.en.222', 'game.en.223', 'game.en.224', 'game.en.225', 'game.en.226', 'game.en.227', 'game.en.228', 'game.en.229', 'game.en.230', 'game.en.231', 'game.en.232', 'game.en.233', 'game.en.234', 'game.en.235', 'game.en.236', 'game.en.237', 'game.en.238', 'game.en.239', 'game.en.240', 'game.en.241', 'game.en.242', 'game.en.243', 'game.en.244', 'game.en.245', 'game.en.246', 'game.en.247', 'game.en.248', 'game.en.249', 'game.en.250', 'game.en.251', 'game.en.252', 'game.en.253', 'game.en.254', 'game.en.255', 'game.en.256', 'game.en.257', 'game.en.258', 'game.en.259', 'game.en.260', 'game.en.261', 'game.en.262', 'game.en.263', 'game.en.264', 'game.en.265', 'game.en.266', 'game.en.267', 'game.en.268', 'game.en.269', 'game.en.270', 'game.en.272', 'game.en.273', 'game.en.274', 'game.en.275', 'game.en.276', 'game.en.277', 'game.en.278', 'game.en.279', 'game.en.280', 'game.en.281', 'game.en.282', 'game.en.283', 'game.en.284', 'game.en.285', 'game.en.286', 'game.en.287', 'game.en.288', 'game.en.289', 'game.en.290', 'game.en.291', 'game.en.292', 'game.en.293', 'game.en.294', 'game.en.295', 'game.en.296', 'game.en.297', 'game.en.298'},
		date = 20230924,
		cross = 'crossarena.en.1'
	},
	[8107] = {
		id = 8107,
		service = 'crossarena',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8', 'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16', 'game.vn.17', 'game.vn.18', 'game.vn.19', 'game.vn.20', 'game.vn.21', 'game.vn.22'},
		date = 20230924,
		cross = 'crossarena.vn.1'
	},
	[9001] = {
		id = 9001,
		service = 'crossunionqa',
		date = 20230715,
		cross = 'en',
		endDate = 20230716
	},
	[9002] = {
		id = 9002,
		service = 'crossunionqa',
		date = 20230722,
		cross = 'en',
		endDate = 20230723
	},
	[9003] = {
		id = 9003,
		service = 'crossunionqa',
		date = 20230729,
		cross = 'en',
		endDate = 20230730
	},
	[9004] = {
		id = 9004,
		service = 'crossunionqa',
		date = 20230805,
		cross = 'en',
		endDate = 20230806
	},
	[9005] = {
		id = 9005,
		service = 'crossunionqa',
		date = 20230812,
		cross = 'en',
		endDate = 20230813
	},
	[9006] = {
		id = 9006,
		service = 'crossunionqa',
		date = 20230819,
		cross = 'en',
		endDate = 20230820
	},
	[9007] = {
		id = 9007,
		service = 'crossunionqa',
		date = 20230826,
		cross = 'en',
		endDate = 20230827
	},
	[9008] = {
		id = 9008,
		service = 'crossunionqa',
		date = 20230902,
		cross = 'en',
		endDate = 20230903
	},
	[9009] = {
		id = 9009,
		service = 'crossunionqa',
		date = 20230909,
		cross = 'en',
		endDate = 20230910
	},
	[9010] = {
		id = 9010,
		service = 'crossunionqa',
		date = 20230916,
		cross = 'en',
		endDate = 20230917
	},
	[9011] = {
		id = 9011,
		service = 'crossunionqa',
		date = 20230923,
		cross = 'en',
		endDate = 20230924
	},
	[9012] = {
		id = 9012,
		service = 'crossunionqa',
		date = 20230930,
		cross = 'en',
		endDate = 20231001
	},
	[9013] = {
		id = 9013,
		service = 'crossunionqa',
		date = 20231007,
		cross = 'en',
		endDate = 20231008
	},
	[9014] = {
		id = 9014,
		service = 'crossunionqa',
		date = 20231014,
		cross = 'en',
		endDate = 20231015
	},
	[10101] = {
		id = 10101,
		service = 'crossfishing',
		date = 20230716,
		cross = 'en'
	},
	[10102] = {
		id = 10102,
		service = 'crossfishing',
		date = 20230723,
		cross = 'en'
	},
	[10103] = {
		id = 10103,
		service = 'crossfishing',
		date = 20230730,
		cross = 'en'
	},
	[10104] = {
		id = 10104,
		service = 'crossfishing',
		date = 20230806,
		cross = 'en'
	},
	[10105] = {
		id = 10105,
		service = 'crossfishing',
		date = 20230813,
		cross = 'en'
	},
	[10106] = {
		id = 10106,
		service = 'crossfishing',
		date = 20230820,
		cross = 'en'
	},
	[10107] = {
		id = 10107,
		service = 'crossfishing',
		date = 20230827,
		cross = 'en'
	},
	[10108] = {
		id = 10108,
		service = 'crossfishing',
		date = 20230903,
		cross = 'en'
	},
	[10109] = {
		id = 10109,
		service = 'crossfishing',
		date = 20230910,
		cross = 'en'
	},
	[10110] = {
		id = 10110,
		service = 'crossfishing',
		date = 20230917,
		cross = 'en'
	},
	[10111] = {
		id = 10111,
		service = 'crossfishing',
		date = 20230924,
		cross = 'en'
	},
	[10112] = {
		id = 10112,
		service = 'crossfishing',
		date = 20231001,
		cross = 'en'
	},
	[10113] = {
		id = 10113,
		service = 'crossfishing',
		date = 20231008,
		cross = 'en'
	},
	[10114] = {
		id = 10114,
		service = 'crossfishing',
		date = 20231015,
		cross = 'en'
	},
	[10115] = {
		id = 10115,
		service = 'crossfishing',
		date = 20231022,
		cross = 'en'
	},
	[10116] = {
		id = 10116,
		service = 'crossfishing',
		date = 20231029,
		cross = 'en'
	},
	[10117] = {
		id = 10117,
		service = 'crossfishing',
		date = 20230709,
		cross = 'vn'
	},
	[10118] = {
		id = 10118,
		service = 'crossfishing',
		date = 20230716,
		cross = 'vn'
	},
	[10119] = {
		id = 10119,
		service = 'crossfishing',
		date = 20230723,
		cross = 'vn'
	},
	[10120] = {
		id = 10120,
		service = 'crossfishing',
		date = 20230730,
		cross = 'vn'
	},
	[10121] = {
		id = 10121,
		service = 'crossfishing',
		date = 20230806,
		cross = 'vn'
	},
	[10122] = {
		id = 10122,
		service = 'crossfishing',
		date = 20230813,
		cross = 'vn'
	},
	[10123] = {
		id = 10123,
		service = 'crossfishing',
		date = 20230820,
		cross = 'vn'
	},
	[10124] = {
		id = 10124,
		service = 'crossfishing',
		date = 20230827,
		cross = 'vn'
	},
	[10125] = {
		id = 10125,
		service = 'crossfishing',
		date = 20230903,
		cross = 'vn'
	},
	[10126] = {
		id = 10126,
		service = 'crossfishing',
		date = 20230910,
		cross = 'vn'
	},
	[10127] = {
		id = 10127,
		service = 'crossfishing',
		date = 20230917,
		cross = 'vn'
	},
	[10128] = {
		id = 10128,
		service = 'crossfishing',
		date = 20230924,
		cross = 'vn'
	},
	[10129] = {
		id = 10129,
		service = 'crossfishing',
		date = 20231001,
		cross = 'vn'
	},
	[10130] = {
		id = 10130,
		service = 'crossfishing',
		date = 20231008,
		cross = 'vn'
	},
	[10131] = {
		id = 10131,
		service = 'crossfishing',
		date = 20231015,
		cross = 'vn'
	},
	[10132] = {
		id = 10132,
		service = 'crossfishing',
		date = 20231022,
		cross = 'vn'
	},
	[10133] = {
		id = 10133,
		service = 'crossfishing',
		date = 20231029,
		cross = 'vn'
	},
	[11116] = {
		id = 11116,
		service = 'volleyballranking',
		date = 20230707,
		cross = 'en',
		endDate = 20230714
	},
	[12116] = {
		id = 12116,
		service = 'shavediceranking',
		date = 20230707,
		cross = 'en',
		endDate = 20230714
	},
	[13116] = {
		id = 13116,
		service = 'skyscraper',
		date = 20230709,
		cross = 'en',
		endDate = 20230716
	},
	[29001] = {
		id = 29001,
		service = 'crosscraft',
		servers = {'game.en.181', 'game.en.182', 'game.en.183', 'game.en.184', 'game.en.185', 'game.en.186', 'game.en.187', 'game.en.188', 'game.en.189', 'game.en.190', 'game.en.191', 'game.en.192', 'game.en.193', 'game.en.194', 'game.en.195', 'game.en.196', 'game.en.197', 'game.en.198', 'game.en.199', 'game.en.200'},
		date = 20230703,
		cross = 'crosscraft.en.11'
	},
	[29002] = {
		id = 29002,
		service = 'crosscraft',
		servers = {'game.en.181', 'game.en.182', 'game.en.183', 'game.en.184', 'game.en.185', 'game.en.186', 'game.en.187', 'game.en.188', 'game.en.189', 'game.en.190', 'game.en.191', 'game.en.192', 'game.en.193', 'game.en.194', 'game.en.195', 'game.en.196', 'game.en.197', 'game.en.198', 'game.en.199', 'game.en.200'},
		date = 20230707,
		cross = 'crosscraft.en.11'
	},
	[29003] = {
		id = 29003,
		service = 'crosscraft',
		servers = {'game.en.181', 'game.en.182', 'game.en.183', 'game.en.184', 'game.en.185', 'game.en.186', 'game.en.187', 'game.en.188', 'game.en.189', 'game.en.190', 'game.en.191', 'game.en.192', 'game.en.193', 'game.en.194', 'game.en.195', 'game.en.196', 'game.en.197', 'game.en.198', 'game.en.199', 'game.en.200'},
		date = 20230711,
		cross = 'crosscraft.en.11'
	},
	[29004] = {
		id = 29004,
		service = 'crosscraft',
		servers = {'game.en.181', 'game.en.182', 'game.en.183', 'game.en.184', 'game.en.185', 'game.en.186', 'game.en.187', 'game.en.188', 'game.en.189', 'game.en.190', 'game.en.191', 'game.en.192', 'game.en.193', 'game.en.194', 'game.en.195', 'game.en.196', 'game.en.197', 'game.en.198', 'game.en.199', 'game.en.200'},
		date = 20230714,
		cross = 'crosscraft.en.11'
	},
	[29005] = {
		id = 29005,
		service = 'crosscraft',
		servers = {'game.en.181', 'game.en.182', 'game.en.183', 'game.en.184', 'game.en.185', 'game.en.186', 'game.en.187', 'game.en.188', 'game.en.189', 'game.en.190', 'game.en.191', 'game.en.192', 'game.en.193', 'game.en.194', 'game.en.195', 'game.en.196', 'game.en.197', 'game.en.198', 'game.en.199', 'game.en.200'},
		date = 20230717,
		cross = 'crosscraft.en.11'
	},
	[29006] = {
		id = 29006,
		service = 'crosscraft',
		servers = {'game.en.181', 'game.en.182', 'game.en.183', 'game.en.184', 'game.en.185', 'game.en.186', 'game.en.187', 'game.en.188', 'game.en.189', 'game.en.190', 'game.en.191', 'game.en.192', 'game.en.193', 'game.en.194', 'game.en.195', 'game.en.196', 'game.en.197', 'game.en.198', 'game.en.199', 'game.en.200'},
		date = 20230721,
		cross = 'crosscraft.en.11'
	},
	[29007] = {
		id = 29007,
		service = 'crosscraft',
		servers = {'game.en.181', 'game.en.182', 'game.en.183', 'game.en.184', 'game.en.185', 'game.en.186', 'game.en.187', 'game.en.188', 'game.en.189', 'game.en.190', 'game.en.191', 'game.en.192', 'game.en.193', 'game.en.194', 'game.en.195', 'game.en.196', 'game.en.197', 'game.en.198', 'game.en.199', 'game.en.200'},
		date = 20230724,
		cross = 'crosscraft.en.11'
	},
	[29008] = {
		id = 29008,
		service = 'crosscraft',
		servers = {'game.en.181', 'game.en.182', 'game.en.183', 'game.en.184', 'game.en.185', 'game.en.186', 'game.en.187', 'game.en.188', 'game.en.189', 'game.en.190', 'game.en.191', 'game.en.192', 'game.en.193', 'game.en.194', 'game.en.195', 'game.en.196', 'game.en.197', 'game.en.198', 'game.en.199', 'game.en.200'},
		date = 20230728,
		cross = 'crosscraft.en.11'
	},
	[29009] = {
		id = 29009,
		service = 'crosscraft',
		servers = {'game.en.181', 'game.en.182', 'game.en.183', 'game.en.184', 'game.en.185', 'game.en.186', 'game.en.187', 'game.en.188', 'game.en.189', 'game.en.190', 'game.en.191', 'game.en.192', 'game.en.193', 'game.en.194', 'game.en.195', 'game.en.196', 'game.en.197', 'game.en.198', 'game.en.199', 'game.en.200'},
		date = 20230731,
		cross = 'crosscraft.en.11'
	},
	[29010] = {
		id = 29010,
		service = 'crosscraft',
		servers = {'game.en.181', 'game.en.182', 'game.en.183', 'game.en.184', 'game.en.185', 'game.en.186', 'game.en.187', 'game.en.188', 'game.en.189', 'game.en.190', 'game.en.191', 'game.en.192', 'game.en.193', 'game.en.194', 'game.en.195', 'game.en.196', 'game.en.197', 'game.en.198', 'game.en.199', 'game.en.200'},
		date = 20230804,
		cross = 'crosscraft.en.11'
	},
	[29011] = {
		id = 29011,
		service = 'crosscraft',
		servers = {'game.en.181', 'game.en.182', 'game.en.183', 'game.en.184', 'game.en.185', 'game.en.186', 'game.en.187', 'game.en.188', 'game.en.189', 'game.en.190', 'game.en.191', 'game.en.192', 'game.en.193', 'game.en.194', 'game.en.195', 'game.en.196', 'game.en.197', 'game.en.198', 'game.en.199', 'game.en.200'},
		date = 20230807,
		cross = 'crosscraft.en.11'
	},
	[29012] = {
		id = 29012,
		service = 'crosscraft',
		servers = {'game.en.181', 'game.en.182', 'game.en.183', 'game.en.184', 'game.en.185', 'game.en.186', 'game.en.187', 'game.en.188', 'game.en.189', 'game.en.190', 'game.en.191', 'game.en.192', 'game.en.193', 'game.en.194', 'game.en.195', 'game.en.196', 'game.en.197', 'game.en.198', 'game.en.199', 'game.en.200'},
		date = 20230811,
		cross = 'crosscraft.en.11'
	},
	[29013] = {
		id = 29013,
		service = 'crosscraft',
		servers = {'game.en.181', 'game.en.182', 'game.en.183', 'game.en.184', 'game.en.185', 'game.en.186', 'game.en.187', 'game.en.188', 'game.en.189', 'game.en.190', 'game.en.191', 'game.en.192', 'game.en.193', 'game.en.194', 'game.en.195', 'game.en.196', 'game.en.197', 'game.en.198', 'game.en.199', 'game.en.200'},
		date = 20230814,
		cross = 'crosscraft.en.11'
	},
	[29014] = {
		id = 29014,
		service = 'crosscraft',
		servers = {'game.en.181', 'game.en.182', 'game.en.183', 'game.en.184', 'game.en.185', 'game.en.186', 'game.en.187', 'game.en.188', 'game.en.189', 'game.en.190', 'game.en.191', 'game.en.192', 'game.en.193', 'game.en.194', 'game.en.195', 'game.en.196', 'game.en.197', 'game.en.198', 'game.en.199', 'game.en.200'},
		date = 20230818,
		cross = 'crosscraft.en.11'
	},
	[29015] = {
		id = 29015,
		service = 'crosscraft',
		servers = {'game.en.181', 'game.en.182', 'game.en.183', 'game.en.184', 'game.en.185', 'game.en.186', 'game.en.187', 'game.en.188', 'game.en.189', 'game.en.190', 'game.en.191', 'game.en.192', 'game.en.193', 'game.en.194', 'game.en.195', 'game.en.196', 'game.en.197', 'game.en.198', 'game.en.199', 'game.en.200'},
		date = 20230821,
		cross = 'crosscraft.en.11'
	},
	[29016] = {
		id = 29016,
		service = 'crosscraft',
		servers = {'game.en.181', 'game.en.182', 'game.en.183', 'game.en.184', 'game.en.185', 'game.en.186', 'game.en.187', 'game.en.188', 'game.en.189', 'game.en.190', 'game.en.191', 'game.en.192', 'game.en.193', 'game.en.194', 'game.en.195', 'game.en.196', 'game.en.197', 'game.en.198', 'game.en.199', 'game.en.200'},
		date = 20230825,
		cross = 'crosscraft.en.11'
	},
	[29017] = {
		id = 29017,
		service = 'crosscraft',
		servers = {'game.en.181', 'game.en.182', 'game.en.183', 'game.en.184', 'game.en.185', 'game.en.186', 'game.en.187', 'game.en.188', 'game.en.189', 'game.en.190', 'game.en.191', 'game.en.192', 'game.en.193', 'game.en.194', 'game.en.195', 'game.en.196', 'game.en.197', 'game.en.198', 'game.en.199', 'game.en.200'},
		date = 20230828,
		cross = 'crosscraft.en.11'
	},
	[29018] = {
		id = 29018,
		service = 'crosscraft',
		servers = {'game.en.181', 'game.en.182', 'game.en.183', 'game.en.184', 'game.en.185', 'game.en.186', 'game.en.187', 'game.en.188', 'game.en.189', 'game.en.190', 'game.en.191', 'game.en.192', 'game.en.193', 'game.en.194', 'game.en.195', 'game.en.196', 'game.en.197', 'game.en.198', 'game.en.199', 'game.en.200'},
		date = 20230901,
		cross = 'crosscraft.en.11'
	},
	[29019] = {
		id = 29019,
		service = 'crosscraft',
		servers = {'game.en.181', 'game.en.182', 'game.en.183', 'game.en.184', 'game.en.185', 'game.en.186', 'game.en.187', 'game.en.188', 'game.en.189', 'game.en.190', 'game.en.191', 'game.en.192', 'game.en.193', 'game.en.194', 'game.en.195', 'game.en.196', 'game.en.197', 'game.en.198', 'game.en.199', 'game.en.200'},
		date = 20230904,
		cross = 'crosscraft.en.11'
	},
	[29020] = {
		id = 29020,
		service = 'crosscraft',
		servers = {'game.en.181', 'game.en.182', 'game.en.183', 'game.en.184', 'game.en.185', 'game.en.186', 'game.en.187', 'game.en.188', 'game.en.189', 'game.en.190', 'game.en.191', 'game.en.192', 'game.en.193', 'game.en.194', 'game.en.195', 'game.en.196', 'game.en.197', 'game.en.198', 'game.en.199', 'game.en.200'},
		date = 20230908,
		cross = 'crosscraft.en.11'
	},
	[29021] = {
		id = 29021,
		service = 'crosscraft',
		servers = {'game.en.181', 'game.en.182', 'game.en.183', 'game.en.184', 'game.en.185', 'game.en.186', 'game.en.187', 'game.en.188', 'game.en.189', 'game.en.190', 'game.en.191', 'game.en.192', 'game.en.193', 'game.en.194', 'game.en.195', 'game.en.196', 'game.en.197', 'game.en.198', 'game.en.199', 'game.en.200'},
		date = 20230911,
		cross = 'crosscraft.en.11'
	},
	[29022] = {
		id = 29022,
		service = 'crosscraft',
		servers = {'game.en.181', 'game.en.182', 'game.en.183', 'game.en.184', 'game.en.185', 'game.en.186', 'game.en.187', 'game.en.188', 'game.en.189', 'game.en.190', 'game.en.191', 'game.en.192', 'game.en.193', 'game.en.194', 'game.en.195', 'game.en.196', 'game.en.197', 'game.en.198', 'game.en.199', 'game.en.200'},
		date = 20230918,
		cross = 'crosscraft.en.11'
	},
	[29023] = {
		id = 29023,
		service = 'crosscraft',
		servers = {'game.en.181', 'game.en.182', 'game.en.183', 'game.en.184', 'game.en.185', 'game.en.186', 'game.en.187', 'game.en.188', 'game.en.189', 'game.en.190', 'game.en.191', 'game.en.192', 'game.en.193', 'game.en.194', 'game.en.195', 'game.en.196', 'game.en.197', 'game.en.198', 'game.en.199', 'game.en.200'},
		date = 20230922,
		cross = 'crosscraft.en.11'
	},
	[29024] = {
		id = 29024,
		service = 'crosscraft',
		servers = {'game.en.181', 'game.en.182', 'game.en.183', 'game.en.184', 'game.en.185', 'game.en.186', 'game.en.187', 'game.en.188', 'game.en.189', 'game.en.190', 'game.en.191', 'game.en.192', 'game.en.193', 'game.en.194', 'game.en.195', 'game.en.196', 'game.en.197', 'game.en.198', 'game.en.199', 'game.en.200'},
		date = 20230925,
		cross = 'crosscraft.en.11'
	},
	[29025] = {
		id = 29025,
		service = 'crosscraft',
		servers = {'game.en.181', 'game.en.182', 'game.en.183', 'game.en.184', 'game.en.185', 'game.en.186', 'game.en.187', 'game.en.188', 'game.en.189', 'game.en.190', 'game.en.191', 'game.en.192', 'game.en.193', 'game.en.194', 'game.en.195', 'game.en.196', 'game.en.197', 'game.en.198', 'game.en.199', 'game.en.200'},
		date = 20230929,
		cross = 'crosscraft.en.11'
	},
	[29026] = {
		id = 29026,
		service = 'crosscraft',
		servers = {'game.en.181', 'game.en.182', 'game.en.183', 'game.en.184', 'game.en.185', 'game.en.186', 'game.en.187', 'game.en.188', 'game.en.189', 'game.en.190', 'game.en.191', 'game.en.192', 'game.en.193', 'game.en.194', 'game.en.195', 'game.en.196', 'game.en.197', 'game.en.198', 'game.en.199', 'game.en.200'},
		date = 20231002,
		cross = 'crosscraft.en.11'
	},
	[29100] = {
		id = 29100,
		service = 'crosscraft',
		servers = {'game.en.201', 'game.en.202', 'game.en.203', 'game.en.204', 'game.en.205', 'game.en.206', 'game.en.207', 'game.en.208', 'game.en.209', 'game.en.210', 'game.en.211', 'game.en.212', 'game.en.213', 'game.en.214', 'game.en.215', 'game.en.216', 'game.en.217', 'game.en.218', 'game.en.219', 'game.en.220'},
		date = 20230703,
		cross = 'crosscraft.en.12'
	},
	[29101] = {
		id = 29101,
		service = 'crosscraft',
		servers = {'game.en.201', 'game.en.202', 'game.en.203', 'game.en.204', 'game.en.205', 'game.en.206', 'game.en.207', 'game.en.208', 'game.en.209', 'game.en.210', 'game.en.211', 'game.en.212', 'game.en.213', 'game.en.214', 'game.en.215', 'game.en.216', 'game.en.217', 'game.en.218', 'game.en.219', 'game.en.220'},
		date = 20230707,
		cross = 'crosscraft.en.12'
	},
	[29102] = {
		id = 29102,
		service = 'crosscraft',
		servers = {'game.en.201', 'game.en.202', 'game.en.203', 'game.en.204', 'game.en.205', 'game.en.206', 'game.en.207', 'game.en.208', 'game.en.209', 'game.en.210', 'game.en.211', 'game.en.212', 'game.en.213', 'game.en.214', 'game.en.215', 'game.en.216', 'game.en.217', 'game.en.218', 'game.en.219', 'game.en.220'},
		date = 20230711
	},
	[29103] = {
		id = 29103,
		service = 'crosscraft',
		servers = {'game.en.201', 'game.en.202', 'game.en.203', 'game.en.204', 'game.en.205', 'game.en.206', 'game.en.207', 'game.en.208', 'game.en.209', 'game.en.210', 'game.en.211', 'game.en.212', 'game.en.213', 'game.en.214', 'game.en.215', 'game.en.216', 'game.en.217', 'game.en.218', 'game.en.219', 'game.en.220'},
		date = 20230714,
		cross = 'crosscraft.en.12'
	},
	[29104] = {
		id = 29104,
		service = 'crosscraft',
		servers = {'game.en.201', 'game.en.202', 'game.en.203', 'game.en.204', 'game.en.205', 'game.en.206', 'game.en.207', 'game.en.208', 'game.en.209', 'game.en.210', 'game.en.211', 'game.en.212', 'game.en.213', 'game.en.214', 'game.en.215', 'game.en.216', 'game.en.217', 'game.en.218', 'game.en.219', 'game.en.220'},
		date = 20230717,
		cross = 'crosscraft.en.12'
	},
	[29105] = {
		id = 29105,
		service = 'crosscraft',
		servers = {'game.en.201', 'game.en.202', 'game.en.203', 'game.en.204', 'game.en.205', 'game.en.206', 'game.en.207', 'game.en.208', 'game.en.209', 'game.en.210', 'game.en.211', 'game.en.212', 'game.en.213', 'game.en.214', 'game.en.215', 'game.en.216', 'game.en.217', 'game.en.218', 'game.en.219', 'game.en.220'},
		date = 20230721,
		cross = 'crosscraft.en.12'
	},
	[29106] = {
		id = 29106,
		service = 'crosscraft',
		servers = {'game.en.201', 'game.en.202', 'game.en.203', 'game.en.204', 'game.en.205', 'game.en.206', 'game.en.207', 'game.en.208', 'game.en.209', 'game.en.210', 'game.en.211', 'game.en.212', 'game.en.213', 'game.en.214', 'game.en.215', 'game.en.216', 'game.en.217', 'game.en.218', 'game.en.219', 'game.en.220'},
		date = 20230724,
		cross = 'crosscraft.en.12'
	},
	[29107] = {
		id = 29107,
		service = 'crosscraft',
		servers = {'game.en.201', 'game.en.202', 'game.en.203', 'game.en.204', 'game.en.205', 'game.en.206', 'game.en.207', 'game.en.208', 'game.en.209', 'game.en.210', 'game.en.211', 'game.en.212', 'game.en.213', 'game.en.214', 'game.en.215', 'game.en.216', 'game.en.217', 'game.en.218', 'game.en.219', 'game.en.220'},
		date = 20230728,
		cross = 'crosscraft.en.12'
	},
	[29108] = {
		id = 29108,
		service = 'crosscraft',
		servers = {'game.en.201', 'game.en.202', 'game.en.203', 'game.en.204', 'game.en.205', 'game.en.206', 'game.en.207', 'game.en.208', 'game.en.209', 'game.en.210', 'game.en.211', 'game.en.212', 'game.en.213', 'game.en.214', 'game.en.215', 'game.en.216', 'game.en.217', 'game.en.218', 'game.en.219', 'game.en.220'},
		date = 20230731,
		cross = 'crosscraft.en.12'
	},
	[29109] = {
		id = 29109,
		service = 'crosscraft',
		servers = {'game.en.201', 'game.en.202', 'game.en.203', 'game.en.204', 'game.en.205', 'game.en.206', 'game.en.207', 'game.en.208', 'game.en.209', 'game.en.210', 'game.en.211', 'game.en.212', 'game.en.213', 'game.en.214', 'game.en.215', 'game.en.216', 'game.en.217', 'game.en.218', 'game.en.219', 'game.en.220'},
		date = 20230804,
		cross = 'crosscraft.en.12'
	},
	[29110] = {
		id = 29110,
		service = 'crosscraft',
		servers = {'game.en.201', 'game.en.202', 'game.en.203', 'game.en.204', 'game.en.205', 'game.en.206', 'game.en.207', 'game.en.208', 'game.en.209', 'game.en.210', 'game.en.211', 'game.en.212', 'game.en.213', 'game.en.214', 'game.en.215', 'game.en.216', 'game.en.217', 'game.en.218', 'game.en.219', 'game.en.220'},
		date = 20230807,
		cross = 'crosscraft.en.12'
	},
	[29111] = {
		id = 29111,
		service = 'crosscraft',
		servers = {'game.en.201', 'game.en.202', 'game.en.203', 'game.en.204', 'game.en.205', 'game.en.206', 'game.en.207', 'game.en.208', 'game.en.209', 'game.en.210', 'game.en.211', 'game.en.212', 'game.en.213', 'game.en.214', 'game.en.215', 'game.en.216', 'game.en.217', 'game.en.218', 'game.en.219', 'game.en.220'},
		date = 20230811,
		cross = 'crosscraft.en.12'
	},
	[29112] = {
		id = 29112,
		service = 'crosscraft',
		servers = {'game.en.201', 'game.en.202', 'game.en.203', 'game.en.204', 'game.en.205', 'game.en.206', 'game.en.207', 'game.en.208', 'game.en.209', 'game.en.210', 'game.en.211', 'game.en.212', 'game.en.213', 'game.en.214', 'game.en.215', 'game.en.216', 'game.en.217', 'game.en.218', 'game.en.219', 'game.en.220'},
		date = 20230814,
		cross = 'crosscraft.en.12'
	},
	[29113] = {
		id = 29113,
		service = 'crosscraft',
		servers = {'game.en.201', 'game.en.202', 'game.en.203', 'game.en.204', 'game.en.205', 'game.en.206', 'game.en.207', 'game.en.208', 'game.en.209', 'game.en.210', 'game.en.211', 'game.en.212', 'game.en.213', 'game.en.214', 'game.en.215', 'game.en.216', 'game.en.217', 'game.en.218', 'game.en.219', 'game.en.220'},
		date = 20230818,
		cross = 'crosscraft.en.12'
	},
	[29114] = {
		id = 29114,
		service = 'crosscraft',
		servers = {'game.en.201', 'game.en.202', 'game.en.203', 'game.en.204', 'game.en.205', 'game.en.206', 'game.en.207', 'game.en.208', 'game.en.209', 'game.en.210', 'game.en.211', 'game.en.212', 'game.en.213', 'game.en.214', 'game.en.215', 'game.en.216', 'game.en.217', 'game.en.218', 'game.en.219', 'game.en.220'},
		date = 20230821,
		cross = 'crosscraft.en.12'
	},
	[29115] = {
		id = 29115,
		service = 'crosscraft',
		servers = {'game.en.201', 'game.en.202', 'game.en.203', 'game.en.204', 'game.en.205', 'game.en.206', 'game.en.207', 'game.en.208', 'game.en.209', 'game.en.210', 'game.en.211', 'game.en.212', 'game.en.213', 'game.en.214', 'game.en.215', 'game.en.216', 'game.en.217', 'game.en.218', 'game.en.219', 'game.en.220'},
		date = 20230825,
		cross = 'crosscraft.en.12'
	},
	[29116] = {
		id = 29116,
		service = 'crosscraft',
		servers = {'game.en.201', 'game.en.202', 'game.en.203', 'game.en.204', 'game.en.205', 'game.en.206', 'game.en.207', 'game.en.208', 'game.en.209', 'game.en.210', 'game.en.211', 'game.en.212', 'game.en.213', 'game.en.214', 'game.en.215', 'game.en.216', 'game.en.217', 'game.en.218', 'game.en.219', 'game.en.220'},
		date = 20230828,
		cross = 'crosscraft.en.12'
	},
	[29117] = {
		id = 29117,
		service = 'crosscraft',
		servers = {'game.en.201', 'game.en.202', 'game.en.203', 'game.en.204', 'game.en.205', 'game.en.206', 'game.en.207', 'game.en.208', 'game.en.209', 'game.en.210', 'game.en.211', 'game.en.212', 'game.en.213', 'game.en.214', 'game.en.215', 'game.en.216', 'game.en.217', 'game.en.218', 'game.en.219', 'game.en.220'},
		date = 20230901,
		cross = 'crosscraft.en.12'
	},
	[29118] = {
		id = 29118,
		service = 'crosscraft',
		servers = {'game.en.201', 'game.en.202', 'game.en.203', 'game.en.204', 'game.en.205', 'game.en.206', 'game.en.207', 'game.en.208', 'game.en.209', 'game.en.210', 'game.en.211', 'game.en.212', 'game.en.213', 'game.en.214', 'game.en.215', 'game.en.216', 'game.en.217', 'game.en.218', 'game.en.219', 'game.en.220'},
		date = 20230904,
		cross = 'crosscraft.en.12'
	},
	[29119] = {
		id = 29119,
		service = 'crosscraft',
		servers = {'game.en.201', 'game.en.202', 'game.en.203', 'game.en.204', 'game.en.205', 'game.en.206', 'game.en.207', 'game.en.208', 'game.en.209', 'game.en.210', 'game.en.211', 'game.en.212', 'game.en.213', 'game.en.214', 'game.en.215', 'game.en.216', 'game.en.217', 'game.en.218', 'game.en.219', 'game.en.220'},
		date = 20230908,
		cross = 'crosscraft.en.12'
	},
	[29120] = {
		id = 29120,
		service = 'crosscraft',
		servers = {'game.en.201', 'game.en.202', 'game.en.203', 'game.en.204', 'game.en.205', 'game.en.206', 'game.en.207', 'game.en.208', 'game.en.209', 'game.en.210', 'game.en.211', 'game.en.212', 'game.en.213', 'game.en.214', 'game.en.215', 'game.en.216', 'game.en.217', 'game.en.218', 'game.en.219', 'game.en.220'},
		date = 20230911,
		cross = 'crosscraft.en.12'
	},
	[29121] = {
		id = 29121,
		service = 'crosscraft',
		servers = {'game.en.201', 'game.en.202', 'game.en.203', 'game.en.204', 'game.en.205', 'game.en.206', 'game.en.207', 'game.en.208', 'game.en.209', 'game.en.210', 'game.en.211', 'game.en.212', 'game.en.213', 'game.en.214', 'game.en.215', 'game.en.216', 'game.en.217', 'game.en.218', 'game.en.219', 'game.en.220'},
		date = 20230918,
		cross = 'crosscraft.en.12'
	},
	[29122] = {
		id = 29122,
		service = 'crosscraft',
		servers = {'game.en.201', 'game.en.202', 'game.en.203', 'game.en.204', 'game.en.205', 'game.en.206', 'game.en.207', 'game.en.208', 'game.en.209', 'game.en.210', 'game.en.211', 'game.en.212', 'game.en.213', 'game.en.214', 'game.en.215', 'game.en.216', 'game.en.217', 'game.en.218', 'game.en.219', 'game.en.220'},
		date = 20230922,
		cross = 'crosscraft.en.12'
	},
	[29123] = {
		id = 29123,
		service = 'crosscraft',
		servers = {'game.en.201', 'game.en.202', 'game.en.203', 'game.en.204', 'game.en.205', 'game.en.206', 'game.en.207', 'game.en.208', 'game.en.209', 'game.en.210', 'game.en.211', 'game.en.212', 'game.en.213', 'game.en.214', 'game.en.215', 'game.en.216', 'game.en.217', 'game.en.218', 'game.en.219', 'game.en.220'},
		date = 20230925,
		cross = 'crosscraft.en.12'
	},
	[29124] = {
		id = 29124,
		service = 'crosscraft',
		servers = {'game.en.201', 'game.en.202', 'game.en.203', 'game.en.204', 'game.en.205', 'game.en.206', 'game.en.207', 'game.en.208', 'game.en.209', 'game.en.210', 'game.en.211', 'game.en.212', 'game.en.213', 'game.en.214', 'game.en.215', 'game.en.216', 'game.en.217', 'game.en.218', 'game.en.219', 'game.en.220'},
		date = 20230929,
		cross = 'crosscraft.en.12'
	},
	[29125] = {
		id = 29125,
		service = 'crosscraft',
		servers = {'game.en.201', 'game.en.202', 'game.en.203', 'game.en.204', 'game.en.205', 'game.en.206', 'game.en.207', 'game.en.208', 'game.en.209', 'game.en.210', 'game.en.211', 'game.en.212', 'game.en.213', 'game.en.214', 'game.en.215', 'game.en.216', 'game.en.217', 'game.en.218', 'game.en.219', 'game.en.220'},
		date = 20231002,
		cross = 'crosscraft.en.12'
	},
	[29200] = {
		id = 29200,
		service = 'crosscraft',
		servers = {'game.en.221', 'game.en.222', 'game.en.223', 'game.en.224', 'game.en.225', 'game.en.226', 'game.en.227', 'game.en.228', 'game.en.229', 'game.en.230', 'game.en.231', 'game.en.232', 'game.en.233', 'game.en.234', 'game.en.235', 'game.en.236', 'game.en.237', 'game.en.238', 'game.en.239', 'game.en.240'},
		date = 20230703,
		cross = 'crosscraft.en.13'
	},
	[29201] = {
		id = 29201,
		service = 'crosscraft',
		servers = {'game.en.221', 'game.en.222', 'game.en.223', 'game.en.224', 'game.en.225', 'game.en.226', 'game.en.227', 'game.en.228', 'game.en.229', 'game.en.230', 'game.en.231', 'game.en.232', 'game.en.233', 'game.en.234', 'game.en.235', 'game.en.236', 'game.en.237', 'game.en.238', 'game.en.239', 'game.en.240'},
		date = 20230707,
		cross = 'crosscraft.en.13'
	},
	[29202] = {
		id = 29202,
		service = 'crosscraft',
		servers = {'game.en.221', 'game.en.222', 'game.en.223', 'game.en.224', 'game.en.225', 'game.en.226', 'game.en.227', 'game.en.228', 'game.en.229', 'game.en.230', 'game.en.231', 'game.en.232', 'game.en.233', 'game.en.234', 'game.en.235', 'game.en.236', 'game.en.237', 'game.en.238', 'game.en.239', 'game.en.240'},
		date = 20230711,
		cross = 'crosscraft.en.13'
	},
	[29203] = {
		id = 29203,
		service = 'crosscraft',
		servers = {'game.en.221', 'game.en.222', 'game.en.223', 'game.en.224', 'game.en.225', 'game.en.226', 'game.en.227', 'game.en.228', 'game.en.229', 'game.en.230', 'game.en.231', 'game.en.232', 'game.en.233', 'game.en.234', 'game.en.235', 'game.en.236', 'game.en.237', 'game.en.238', 'game.en.239', 'game.en.240'},
		date = 20230714,
		cross = 'crosscraft.en.13'
	},
	[29204] = {
		id = 29204,
		service = 'crosscraft',
		servers = {'game.en.221', 'game.en.222', 'game.en.223', 'game.en.224', 'game.en.225', 'game.en.226', 'game.en.227', 'game.en.228', 'game.en.229', 'game.en.230', 'game.en.231', 'game.en.232', 'game.en.233', 'game.en.234', 'game.en.235', 'game.en.236', 'game.en.237', 'game.en.238', 'game.en.239', 'game.en.240'},
		date = 20230717,
		cross = 'crosscraft.en.13'
	},
	[29205] = {
		id = 29205,
		service = 'crosscraft',
		servers = {'game.en.221', 'game.en.222', 'game.en.223', 'game.en.224', 'game.en.225', 'game.en.226', 'game.en.227', 'game.en.228', 'game.en.229', 'game.en.230', 'game.en.231', 'game.en.232', 'game.en.233', 'game.en.234', 'game.en.235', 'game.en.236', 'game.en.237', 'game.en.238', 'game.en.239', 'game.en.240'},
		date = 20230721,
		cross = 'crosscraft.en.13'
	},
	[29206] = {
		id = 29206,
		service = 'crosscraft',
		servers = {'game.en.221', 'game.en.222', 'game.en.223', 'game.en.224', 'game.en.225', 'game.en.226', 'game.en.227', 'game.en.228', 'game.en.229', 'game.en.230', 'game.en.231', 'game.en.232', 'game.en.233', 'game.en.234', 'game.en.235', 'game.en.236', 'game.en.237', 'game.en.238', 'game.en.239', 'game.en.240'},
		date = 20230724,
		cross = 'crosscraft.en.13'
	},
	[29207] = {
		id = 29207,
		service = 'crosscraft',
		servers = {'game.en.221', 'game.en.222', 'game.en.223', 'game.en.224', 'game.en.225', 'game.en.226', 'game.en.227', 'game.en.228', 'game.en.229', 'game.en.230', 'game.en.231', 'game.en.232', 'game.en.233', 'game.en.234', 'game.en.235', 'game.en.236', 'game.en.237', 'game.en.238', 'game.en.239', 'game.en.240'},
		date = 20230728,
		cross = 'crosscraft.en.13'
	},
	[29208] = {
		id = 29208,
		service = 'crosscraft',
		servers = {'game.en.221', 'game.en.222', 'game.en.223', 'game.en.224', 'game.en.225', 'game.en.226', 'game.en.227', 'game.en.228', 'game.en.229', 'game.en.230', 'game.en.231', 'game.en.232', 'game.en.233', 'game.en.234', 'game.en.235', 'game.en.236', 'game.en.237', 'game.en.238', 'game.en.239', 'game.en.240'},
		date = 20230731,
		cross = 'crosscraft.en.13'
	},
	[29209] = {
		id = 29209,
		service = 'crosscraft',
		servers = {'game.en.221', 'game.en.222', 'game.en.223', 'game.en.224', 'game.en.225', 'game.en.226', 'game.en.227', 'game.en.228', 'game.en.229', 'game.en.230', 'game.en.231', 'game.en.232', 'game.en.233', 'game.en.234', 'game.en.235', 'game.en.236', 'game.en.237', 'game.en.238', 'game.en.239', 'game.en.240'},
		date = 20230804,
		cross = 'crosscraft.en.13'
	},
	[29210] = {
		id = 29210,
		service = 'crosscraft',
		servers = {'game.en.221', 'game.en.222', 'game.en.223', 'game.en.224', 'game.en.225', 'game.en.226', 'game.en.227', 'game.en.228', 'game.en.229', 'game.en.230', 'game.en.231', 'game.en.232', 'game.en.233', 'game.en.234', 'game.en.235', 'game.en.236', 'game.en.237', 'game.en.238', 'game.en.239', 'game.en.240'},
		date = 20230807,
		cross = 'crosscraft.en.13'
	},
	[29211] = {
		id = 29211,
		service = 'crosscraft',
		servers = {'game.en.221', 'game.en.222', 'game.en.223', 'game.en.224', 'game.en.225', 'game.en.226', 'game.en.227', 'game.en.228', 'game.en.229', 'game.en.230', 'game.en.231', 'game.en.232', 'game.en.233', 'game.en.234', 'game.en.235', 'game.en.236', 'game.en.237', 'game.en.238', 'game.en.239', 'game.en.240'},
		date = 20230811,
		cross = 'crosscraft.en.13'
	},
	[29212] = {
		id = 29212,
		service = 'crosscraft',
		servers = {'game.en.221', 'game.en.222', 'game.en.223', 'game.en.224', 'game.en.225', 'game.en.226', 'game.en.227', 'game.en.228', 'game.en.229', 'game.en.230', 'game.en.231', 'game.en.232', 'game.en.233', 'game.en.234', 'game.en.235', 'game.en.236', 'game.en.237', 'game.en.238', 'game.en.239', 'game.en.240'},
		date = 20230814,
		cross = 'crosscraft.en.13'
	},
	[29213] = {
		id = 29213,
		service = 'crosscraft',
		servers = {'game.en.221', 'game.en.222', 'game.en.223', 'game.en.224', 'game.en.225', 'game.en.226', 'game.en.227', 'game.en.228', 'game.en.229', 'game.en.230', 'game.en.231', 'game.en.232', 'game.en.233', 'game.en.234', 'game.en.235', 'game.en.236', 'game.en.237', 'game.en.238', 'game.en.239', 'game.en.240'},
		date = 20230818,
		cross = 'crosscraft.en.13'
	},
	[29214] = {
		id = 29214,
		service = 'crosscraft',
		servers = {'game.en.221', 'game.en.222', 'game.en.223', 'game.en.224', 'game.en.225', 'game.en.226', 'game.en.227', 'game.en.228', 'game.en.229', 'game.en.230', 'game.en.231', 'game.en.232', 'game.en.233', 'game.en.234', 'game.en.235', 'game.en.236', 'game.en.237', 'game.en.238', 'game.en.239', 'game.en.240'},
		date = 20230821,
		cross = 'crosscraft.en.13'
	},
	[29215] = {
		id = 29215,
		service = 'crosscraft',
		servers = {'game.en.221', 'game.en.222', 'game.en.223', 'game.en.224', 'game.en.225', 'game.en.226', 'game.en.227', 'game.en.228', 'game.en.229', 'game.en.230', 'game.en.231', 'game.en.232', 'game.en.233', 'game.en.234', 'game.en.235', 'game.en.236', 'game.en.237', 'game.en.238', 'game.en.239', 'game.en.240'},
		date = 20230825,
		cross = 'crosscraft.en.13'
	},
	[29216] = {
		id = 29216,
		service = 'crosscraft',
		servers = {'game.en.221', 'game.en.222', 'game.en.223', 'game.en.224', 'game.en.225', 'game.en.226', 'game.en.227', 'game.en.228', 'game.en.229', 'game.en.230', 'game.en.231', 'game.en.232', 'game.en.233', 'game.en.234', 'game.en.235', 'game.en.236', 'game.en.237', 'game.en.238', 'game.en.239', 'game.en.240'},
		date = 20230828,
		cross = 'crosscraft.en.13'
	},
	[29217] = {
		id = 29217,
		service = 'crosscraft',
		servers = {'game.en.221', 'game.en.222', 'game.en.223', 'game.en.224', 'game.en.225', 'game.en.226', 'game.en.227', 'game.en.228', 'game.en.229', 'game.en.230', 'game.en.231', 'game.en.232', 'game.en.233', 'game.en.234', 'game.en.235', 'game.en.236', 'game.en.237', 'game.en.238', 'game.en.239', 'game.en.240'},
		date = 20230901,
		cross = 'crosscraft.en.13'
	},
	[29218] = {
		id = 29218,
		service = 'crosscraft',
		servers = {'game.en.221', 'game.en.222', 'game.en.223', 'game.en.224', 'game.en.225', 'game.en.226', 'game.en.227', 'game.en.228', 'game.en.229', 'game.en.230', 'game.en.231', 'game.en.232', 'game.en.233', 'game.en.234', 'game.en.235', 'game.en.236', 'game.en.237', 'game.en.238', 'game.en.239', 'game.en.240'},
		date = 20230904,
		cross = 'crosscraft.en.13'
	},
	[29219] = {
		id = 29219,
		service = 'crosscraft',
		servers = {'game.en.221', 'game.en.222', 'game.en.223', 'game.en.224', 'game.en.225', 'game.en.226', 'game.en.227', 'game.en.228', 'game.en.229', 'game.en.230', 'game.en.231', 'game.en.232', 'game.en.233', 'game.en.234', 'game.en.235', 'game.en.236', 'game.en.237', 'game.en.238', 'game.en.239', 'game.en.240'},
		date = 20230908,
		cross = 'crosscraft.en.13'
	},
	[29220] = {
		id = 29220,
		service = 'crosscraft',
		servers = {'game.en.221', 'game.en.222', 'game.en.223', 'game.en.224', 'game.en.225', 'game.en.226', 'game.en.227', 'game.en.228', 'game.en.229', 'game.en.230', 'game.en.231', 'game.en.232', 'game.en.233', 'game.en.234', 'game.en.235', 'game.en.236', 'game.en.237', 'game.en.238', 'game.en.239', 'game.en.240'},
		date = 20230911,
		cross = 'crosscraft.en.13'
	},
	[29221] = {
		id = 29221,
		service = 'crosscraft',
		servers = {'game.en.221', 'game.en.222', 'game.en.223', 'game.en.224', 'game.en.225', 'game.en.226', 'game.en.227', 'game.en.228', 'game.en.229', 'game.en.230', 'game.en.231', 'game.en.232', 'game.en.233', 'game.en.234', 'game.en.235', 'game.en.236', 'game.en.237', 'game.en.238', 'game.en.239', 'game.en.240'},
		date = 20230918,
		cross = 'crosscraft.en.13'
	},
	[29222] = {
		id = 29222,
		service = 'crosscraft',
		servers = {'game.en.221', 'game.en.222', 'game.en.223', 'game.en.224', 'game.en.225', 'game.en.226', 'game.en.227', 'game.en.228', 'game.en.229', 'game.en.230', 'game.en.231', 'game.en.232', 'game.en.233', 'game.en.234', 'game.en.235', 'game.en.236', 'game.en.237', 'game.en.238', 'game.en.239', 'game.en.240'},
		date = 20230922,
		cross = 'crosscraft.en.13'
	},
	[29223] = {
		id = 29223,
		service = 'crosscraft',
		servers = {'game.en.221', 'game.en.222', 'game.en.223', 'game.en.224', 'game.en.225', 'game.en.226', 'game.en.227', 'game.en.228', 'game.en.229', 'game.en.230', 'game.en.231', 'game.en.232', 'game.en.233', 'game.en.234', 'game.en.235', 'game.en.236', 'game.en.237', 'game.en.238', 'game.en.239', 'game.en.240'},
		date = 20230925,
		cross = 'crosscraft.en.13'
	},
	[29224] = {
		id = 29224,
		service = 'crosscraft',
		servers = {'game.en.221', 'game.en.222', 'game.en.223', 'game.en.224', 'game.en.225', 'game.en.226', 'game.en.227', 'game.en.228', 'game.en.229', 'game.en.230', 'game.en.231', 'game.en.232', 'game.en.233', 'game.en.234', 'game.en.235', 'game.en.236', 'game.en.237', 'game.en.238', 'game.en.239', 'game.en.240'},
		date = 20230929,
		cross = 'crosscraft.en.13'
	},
	[29225] = {
		id = 29225,
		service = 'crosscraft',
		servers = {'game.en.221', 'game.en.222', 'game.en.223', 'game.en.224', 'game.en.225', 'game.en.226', 'game.en.227', 'game.en.228', 'game.en.229', 'game.en.230', 'game.en.231', 'game.en.232', 'game.en.233', 'game.en.234', 'game.en.235', 'game.en.236', 'game.en.237', 'game.en.238', 'game.en.239', 'game.en.240'},
		date = 20231002,
		cross = 'crosscraft.en.13'
	},
	[29300] = {
		id = 29300,
		service = 'crosscraft',
		servers = {'game.en.241', 'game.en.242', 'game.en.243', 'game.en.244', 'game.en.245', 'game.en.246', 'game.en.247', 'game.en.248', 'game.en.249', 'game.en.250', 'game.en.251', 'game.en.252', 'game.en.253', 'game.en.254', 'game.en.255', 'game.en.256', 'game.en.257', 'game.en.258', 'game.en.259', 'game.en.260'},
		date = 20230703,
		cross = 'crosscraft.en.14'
	},
	[29301] = {
		id = 29301,
		service = 'crosscraft',
		servers = {'game.en.241', 'game.en.242', 'game.en.243', 'game.en.244', 'game.en.245', 'game.en.246', 'game.en.247', 'game.en.248', 'game.en.249', 'game.en.250', 'game.en.251', 'game.en.252', 'game.en.253', 'game.en.254', 'game.en.255', 'game.en.256', 'game.en.257', 'game.en.258', 'game.en.259', 'game.en.260'},
		date = 20230707,
		cross = 'crosscraft.en.14'
	},
	[29302] = {
		id = 29302,
		service = 'crosscraft',
		servers = {'game.en.241', 'game.en.242', 'game.en.243', 'game.en.244', 'game.en.245', 'game.en.246', 'game.en.247', 'game.en.248', 'game.en.249', 'game.en.250', 'game.en.251', 'game.en.252', 'game.en.253', 'game.en.254', 'game.en.255', 'game.en.256', 'game.en.257', 'game.en.258', 'game.en.259', 'game.en.260'},
		date = 20230711,
		cross = 'crosscraft.en.14'
	},
	[29303] = {
		id = 29303,
		service = 'crosscraft',
		servers = {'game.en.241', 'game.en.242', 'game.en.243', 'game.en.244', 'game.en.245', 'game.en.246', 'game.en.247', 'game.en.248', 'game.en.249', 'game.en.250', 'game.en.251', 'game.en.252', 'game.en.253', 'game.en.254', 'game.en.255', 'game.en.256', 'game.en.257', 'game.en.258', 'game.en.259', 'game.en.260'},
		date = 20230714,
		cross = 'crosscraft.en.14'
	},
	[29304] = {
		id = 29304,
		service = 'crosscraft',
		servers = {'game.en.241', 'game.en.242', 'game.en.243', 'game.en.244', 'game.en.245', 'game.en.246', 'game.en.247', 'game.en.248', 'game.en.249', 'game.en.250', 'game.en.251', 'game.en.252', 'game.en.253', 'game.en.254', 'game.en.255', 'game.en.256', 'game.en.257', 'game.en.258', 'game.en.259', 'game.en.260'},
		date = 20230717,
		cross = 'crosscraft.en.14'
	},
	[29305] = {
		id = 29305,
		service = 'crosscraft',
		servers = {'game.en.241', 'game.en.242', 'game.en.243', 'game.en.244', 'game.en.245', 'game.en.246', 'game.en.247', 'game.en.248', 'game.en.249', 'game.en.250', 'game.en.251', 'game.en.252', 'game.en.253', 'game.en.254', 'game.en.255', 'game.en.256', 'game.en.257', 'game.en.258', 'game.en.259', 'game.en.260'},
		date = 20230721,
		cross = 'crosscraft.en.14'
	},
	[29306] = {
		id = 29306,
		service = 'crosscraft',
		servers = {'game.en.241', 'game.en.242', 'game.en.243', 'game.en.244', 'game.en.245', 'game.en.246', 'game.en.247', 'game.en.248', 'game.en.249', 'game.en.250', 'game.en.251', 'game.en.252', 'game.en.253', 'game.en.254', 'game.en.255', 'game.en.256', 'game.en.257', 'game.en.258', 'game.en.259', 'game.en.260'},
		date = 20230724,
		cross = 'crosscraft.en.14'
	},
	[29307] = {
		id = 29307,
		service = 'crosscraft',
		servers = {'game.en.241', 'game.en.242', 'game.en.243', 'game.en.244', 'game.en.245', 'game.en.246', 'game.en.247', 'game.en.248', 'game.en.249', 'game.en.250', 'game.en.251', 'game.en.252', 'game.en.253', 'game.en.254', 'game.en.255', 'game.en.256', 'game.en.257', 'game.en.258', 'game.en.259', 'game.en.260'},
		date = 20230728,
		cross = 'crosscraft.en.14'
	},
	[29308] = {
		id = 29308,
		service = 'crosscraft',
		servers = {'game.en.241', 'game.en.242', 'game.en.243', 'game.en.244', 'game.en.245', 'game.en.246', 'game.en.247', 'game.en.248', 'game.en.249', 'game.en.250', 'game.en.251', 'game.en.252', 'game.en.253', 'game.en.254', 'game.en.255', 'game.en.256', 'game.en.257', 'game.en.258', 'game.en.259', 'game.en.260'},
		date = 20230731,
		cross = 'crosscraft.en.14'
	},
	[29309] = {
		id = 29309,
		service = 'crosscraft',
		servers = {'game.en.241', 'game.en.242', 'game.en.243', 'game.en.244', 'game.en.245', 'game.en.246', 'game.en.247', 'game.en.248', 'game.en.249', 'game.en.250', 'game.en.251', 'game.en.252', 'game.en.253', 'game.en.254', 'game.en.255', 'game.en.256', 'game.en.257', 'game.en.258', 'game.en.259', 'game.en.260'},
		date = 20230804,
		cross = 'crosscraft.en.14'
	},
	[29310] = {
		id = 29310,
		service = 'crosscraft',
		servers = {'game.en.241', 'game.en.242', 'game.en.243', 'game.en.244', 'game.en.245', 'game.en.246', 'game.en.247', 'game.en.248', 'game.en.249', 'game.en.250', 'game.en.251', 'game.en.252', 'game.en.253', 'game.en.254', 'game.en.255', 'game.en.256', 'game.en.257', 'game.en.258', 'game.en.259', 'game.en.260'},
		date = 20230807,
		cross = 'crosscraft.en.14'
	},
	[29311] = {
		id = 29311,
		service = 'crosscraft',
		servers = {'game.en.241', 'game.en.242', 'game.en.243', 'game.en.244', 'game.en.245', 'game.en.246', 'game.en.247', 'game.en.248', 'game.en.249', 'game.en.250', 'game.en.251', 'game.en.252', 'game.en.253', 'game.en.254', 'game.en.255', 'game.en.256', 'game.en.257', 'game.en.258', 'game.en.259', 'game.en.260'},
		date = 20230811,
		cross = 'crosscraft.en.14'
	},
	[29312] = {
		id = 29312,
		service = 'crosscraft',
		servers = {'game.en.241', 'game.en.242', 'game.en.243', 'game.en.244', 'game.en.245', 'game.en.246', 'game.en.247', 'game.en.248', 'game.en.249', 'game.en.250', 'game.en.251', 'game.en.252', 'game.en.253', 'game.en.254', 'game.en.255', 'game.en.256', 'game.en.257', 'game.en.258', 'game.en.259', 'game.en.260'},
		date = 20230814,
		cross = 'crosscraft.en.14'
	},
	[29313] = {
		id = 29313,
		service = 'crosscraft',
		servers = {'game.en.241', 'game.en.242', 'game.en.243', 'game.en.244', 'game.en.245', 'game.en.246', 'game.en.247', 'game.en.248', 'game.en.249', 'game.en.250', 'game.en.251', 'game.en.252', 'game.en.253', 'game.en.254', 'game.en.255', 'game.en.256', 'game.en.257', 'game.en.258', 'game.en.259', 'game.en.260'},
		date = 20230818,
		cross = 'crosscraft.en.14'
	},
	[29314] = {
		id = 29314,
		service = 'crosscraft',
		servers = {'game.en.241', 'game.en.242', 'game.en.243', 'game.en.244', 'game.en.245', 'game.en.246', 'game.en.247', 'game.en.248', 'game.en.249', 'game.en.250', 'game.en.251', 'game.en.252', 'game.en.253', 'game.en.254', 'game.en.255', 'game.en.256', 'game.en.257', 'game.en.258', 'game.en.259', 'game.en.260'},
		date = 20230821,
		cross = 'crosscraft.en.14'
	},
	[29315] = {
		id = 29315,
		service = 'crosscraft',
		servers = {'game.en.241', 'game.en.242', 'game.en.243', 'game.en.244', 'game.en.245', 'game.en.246', 'game.en.247', 'game.en.248', 'game.en.249', 'game.en.250', 'game.en.251', 'game.en.252', 'game.en.253', 'game.en.254', 'game.en.255', 'game.en.256', 'game.en.257', 'game.en.258', 'game.en.259', 'game.en.260'},
		date = 20230825,
		cross = 'crosscraft.en.14'
	},
	[29316] = {
		id = 29316,
		service = 'crosscraft',
		servers = {'game.en.241', 'game.en.242', 'game.en.243', 'game.en.244', 'game.en.245', 'game.en.246', 'game.en.247', 'game.en.248', 'game.en.249', 'game.en.250', 'game.en.251', 'game.en.252', 'game.en.253', 'game.en.254', 'game.en.255', 'game.en.256', 'game.en.257', 'game.en.258', 'game.en.259', 'game.en.260'},
		date = 20230828,
		cross = 'crosscraft.en.14'
	},
	[29317] = {
		id = 29317,
		service = 'crosscraft',
		servers = {'game.en.241', 'game.en.242', 'game.en.243', 'game.en.244', 'game.en.245', 'game.en.246', 'game.en.247', 'game.en.248', 'game.en.249', 'game.en.250', 'game.en.251', 'game.en.252', 'game.en.253', 'game.en.254', 'game.en.255', 'game.en.256', 'game.en.257', 'game.en.258', 'game.en.259', 'game.en.260'},
		date = 20230901,
		cross = 'crosscraft.en.14'
	},
	[29318] = {
		id = 29318,
		service = 'crosscraft',
		servers = {'game.en.241', 'game.en.242', 'game.en.243', 'game.en.244', 'game.en.245', 'game.en.246', 'game.en.247', 'game.en.248', 'game.en.249', 'game.en.250', 'game.en.251', 'game.en.252', 'game.en.253', 'game.en.254', 'game.en.255', 'game.en.256', 'game.en.257', 'game.en.258', 'game.en.259', 'game.en.260'},
		date = 20230904,
		cross = 'crosscraft.en.14'
	},
	[29319] = {
		id = 29319,
		service = 'crosscraft',
		servers = {'game.en.241', 'game.en.242', 'game.en.243', 'game.en.244', 'game.en.245', 'game.en.246', 'game.en.247', 'game.en.248', 'game.en.249', 'game.en.250', 'game.en.251', 'game.en.252', 'game.en.253', 'game.en.254', 'game.en.255', 'game.en.256', 'game.en.257', 'game.en.258', 'game.en.259', 'game.en.260'},
		date = 20230908,
		cross = 'crosscraft.en.14'
	},
	[29320] = {
		id = 29320,
		service = 'crosscraft',
		servers = {'game.en.241', 'game.en.242', 'game.en.243', 'game.en.244', 'game.en.245', 'game.en.246', 'game.en.247', 'game.en.248', 'game.en.249', 'game.en.250', 'game.en.251', 'game.en.252', 'game.en.253', 'game.en.254', 'game.en.255', 'game.en.256', 'game.en.257', 'game.en.258', 'game.en.259', 'game.en.260'},
		date = 20230911,
		cross = 'crosscraft.en.14'
	},
	[29321] = {
		id = 29321,
		service = 'crosscraft',
		servers = {'game.en.241', 'game.en.242', 'game.en.243', 'game.en.244', 'game.en.245', 'game.en.246', 'game.en.247', 'game.en.248', 'game.en.249', 'game.en.250', 'game.en.251', 'game.en.252', 'game.en.253', 'game.en.254', 'game.en.255', 'game.en.256', 'game.en.257', 'game.en.258', 'game.en.259', 'game.en.260'},
		date = 20230918,
		cross = 'crosscraft.en.14'
	},
	[29322] = {
		id = 29322,
		service = 'crosscraft',
		servers = {'game.en.241', 'game.en.242', 'game.en.243', 'game.en.244', 'game.en.245', 'game.en.246', 'game.en.247', 'game.en.248', 'game.en.249', 'game.en.250', 'game.en.251', 'game.en.252', 'game.en.253', 'game.en.254', 'game.en.255', 'game.en.256', 'game.en.257', 'game.en.258', 'game.en.259', 'game.en.260'},
		date = 20230922,
		cross = 'crosscraft.en.14'
	},
	[29323] = {
		id = 29323,
		service = 'crosscraft',
		servers = {'game.en.241', 'game.en.242', 'game.en.243', 'game.en.244', 'game.en.245', 'game.en.246', 'game.en.247', 'game.en.248', 'game.en.249', 'game.en.250', 'game.en.251', 'game.en.252', 'game.en.253', 'game.en.254', 'game.en.255', 'game.en.256', 'game.en.257', 'game.en.258', 'game.en.259', 'game.en.260'},
		date = 20230925,
		cross = 'crosscraft.en.14'
	},
	[29324] = {
		id = 29324,
		service = 'crosscraft',
		servers = {'game.en.241', 'game.en.242', 'game.en.243', 'game.en.244', 'game.en.245', 'game.en.246', 'game.en.247', 'game.en.248', 'game.en.249', 'game.en.250', 'game.en.251', 'game.en.252', 'game.en.253', 'game.en.254', 'game.en.255', 'game.en.256', 'game.en.257', 'game.en.258', 'game.en.259', 'game.en.260'},
		date = 20230929,
		cross = 'crosscraft.en.14'
	},
	[29325] = {
		id = 29325,
		service = 'crosscraft',
		servers = {'game.en.241', 'game.en.242', 'game.en.243', 'game.en.244', 'game.en.245', 'game.en.246', 'game.en.247', 'game.en.248', 'game.en.249', 'game.en.250', 'game.en.251', 'game.en.252', 'game.en.253', 'game.en.254', 'game.en.255', 'game.en.256', 'game.en.257', 'game.en.258', 'game.en.259', 'game.en.260'},
		date = 20231002,
		cross = 'crosscraft.en.14'
	},
	[29400] = {
		id = 29400,
		service = 'crosscraft',
		servers = {'game.en.261', 'game.en.262', 'game.en.263', 'game.en.264', 'game.en.265', 'game.en.266', 'game.en.267', 'game.en.268', 'game.en.269', 'game.en.270', 'game.en.271', 'game.en.272', 'game.en.273', 'game.en.274', 'game.en.275', 'game.en.276', 'game.en.277', 'game.en.278', 'game.en.279', 'game.en.280'},
		date = 20230703,
		cross = 'crosscraft.en.15'
	},
	[29401] = {
		id = 29401,
		service = 'crosscraft',
		servers = {'game.en.261', 'game.en.262', 'game.en.263', 'game.en.264', 'game.en.265', 'game.en.266', 'game.en.267', 'game.en.268', 'game.en.269', 'game.en.270', 'game.en.271', 'game.en.272', 'game.en.273', 'game.en.274', 'game.en.275', 'game.en.276', 'game.en.277', 'game.en.278', 'game.en.279', 'game.en.280'},
		date = 20230707,
		cross = 'crosscraft.en.15'
	},
	[29402] = {
		id = 29402,
		service = 'crosscraft',
		servers = {'game.en.261', 'game.en.262', 'game.en.263', 'game.en.264', 'game.en.265', 'game.en.266', 'game.en.267', 'game.en.268', 'game.en.269', 'game.en.270', 'game.en.271', 'game.en.272', 'game.en.273', 'game.en.274', 'game.en.275', 'game.en.276', 'game.en.277', 'game.en.278', 'game.en.279', 'game.en.280'},
		date = 20230711,
		cross = 'crosscraft.en.15'
	},
	[29403] = {
		id = 29403,
		service = 'crosscraft',
		servers = {'game.en.261', 'game.en.262', 'game.en.263', 'game.en.264', 'game.en.265', 'game.en.266', 'game.en.267', 'game.en.268', 'game.en.269', 'game.en.270', 'game.en.271', 'game.en.272', 'game.en.273', 'game.en.274', 'game.en.275', 'game.en.276', 'game.en.277', 'game.en.278', 'game.en.279', 'game.en.280'},
		date = 20230714,
		cross = 'crosscraft.en.15'
	},
	[29404] = {
		id = 29404,
		service = 'crosscraft',
		servers = {'game.en.261', 'game.en.262', 'game.en.263', 'game.en.264', 'game.en.265', 'game.en.266', 'game.en.267', 'game.en.268', 'game.en.269', 'game.en.270', 'game.en.271', 'game.en.272', 'game.en.273', 'game.en.274', 'game.en.275', 'game.en.276', 'game.en.277', 'game.en.278', 'game.en.279', 'game.en.280'},
		date = 20230717,
		cross = 'crosscraft.en.15'
	},
	[29405] = {
		id = 29405,
		service = 'crosscraft',
		servers = {'game.en.261', 'game.en.262', 'game.en.263', 'game.en.264', 'game.en.265', 'game.en.266', 'game.en.267', 'game.en.268', 'game.en.269', 'game.en.270', 'game.en.271', 'game.en.272', 'game.en.273', 'game.en.274', 'game.en.275', 'game.en.276', 'game.en.277', 'game.en.278', 'game.en.279', 'game.en.280'},
		date = 20230721,
		cross = 'crosscraft.en.15'
	},
	[29406] = {
		id = 29406,
		service = 'crosscraft',
		servers = {'game.en.261', 'game.en.262', 'game.en.263', 'game.en.264', 'game.en.265', 'game.en.266', 'game.en.267', 'game.en.268', 'game.en.269', 'game.en.270', 'game.en.271', 'game.en.272', 'game.en.273', 'game.en.274', 'game.en.275', 'game.en.276', 'game.en.277', 'game.en.278', 'game.en.279', 'game.en.280'},
		date = 20230724,
		cross = 'crosscraft.en.15'
	},
	[29407] = {
		id = 29407,
		service = 'crosscraft',
		servers = {'game.en.261', 'game.en.262', 'game.en.263', 'game.en.264', 'game.en.265', 'game.en.266', 'game.en.267', 'game.en.268', 'game.en.269', 'game.en.270', 'game.en.271', 'game.en.272', 'game.en.273', 'game.en.274', 'game.en.275', 'game.en.276', 'game.en.277', 'game.en.278', 'game.en.279', 'game.en.280'},
		date = 20230728,
		cross = 'crosscraft.en.15'
	},
	[29408] = {
		id = 29408,
		service = 'crosscraft',
		servers = {'game.en.261', 'game.en.262', 'game.en.263', 'game.en.264', 'game.en.265', 'game.en.266', 'game.en.267', 'game.en.268', 'game.en.269', 'game.en.270', 'game.en.271', 'game.en.272', 'game.en.273', 'game.en.274', 'game.en.275', 'game.en.276', 'game.en.277', 'game.en.278', 'game.en.279', 'game.en.280'},
		date = 20230731,
		cross = 'crosscraft.en.15'
	},
	[29409] = {
		id = 29409,
		service = 'crosscraft',
		servers = {'game.en.261', 'game.en.262', 'game.en.263', 'game.en.264', 'game.en.265', 'game.en.266', 'game.en.267', 'game.en.268', 'game.en.269', 'game.en.270', 'game.en.271', 'game.en.272', 'game.en.273', 'game.en.274', 'game.en.275', 'game.en.276', 'game.en.277', 'game.en.278', 'game.en.279', 'game.en.280'},
		date = 20230804,
		cross = 'crosscraft.en.15'
	},
	[29410] = {
		id = 29410,
		service = 'crosscraft',
		servers = {'game.en.261', 'game.en.262', 'game.en.263', 'game.en.264', 'game.en.265', 'game.en.266', 'game.en.267', 'game.en.268', 'game.en.269', 'game.en.270', 'game.en.271', 'game.en.272', 'game.en.273', 'game.en.274', 'game.en.275', 'game.en.276', 'game.en.277', 'game.en.278', 'game.en.279', 'game.en.280'},
		date = 20230807,
		cross = 'crosscraft.en.15'
	},
	[29411] = {
		id = 29411,
		service = 'crosscraft',
		servers = {'game.en.261', 'game.en.262', 'game.en.263', 'game.en.264', 'game.en.265', 'game.en.266', 'game.en.267', 'game.en.268', 'game.en.269', 'game.en.270', 'game.en.271', 'game.en.272', 'game.en.273', 'game.en.274', 'game.en.275', 'game.en.276', 'game.en.277', 'game.en.278', 'game.en.279', 'game.en.280'},
		date = 20230811,
		cross = 'crosscraft.en.15'
	},
	[29412] = {
		id = 29412,
		service = 'crosscraft',
		servers = {'game.en.261', 'game.en.262', 'game.en.263', 'game.en.264', 'game.en.265', 'game.en.266', 'game.en.267', 'game.en.268', 'game.en.269', 'game.en.270', 'game.en.271', 'game.en.272', 'game.en.273', 'game.en.274', 'game.en.275', 'game.en.276', 'game.en.277', 'game.en.278', 'game.en.279', 'game.en.280'},
		date = 20230814,
		cross = 'crosscraft.en.15'
	},
	[29413] = {
		id = 29413,
		service = 'crosscraft',
		servers = {'game.en.261', 'game.en.262', 'game.en.263', 'game.en.264', 'game.en.265', 'game.en.266', 'game.en.267', 'game.en.268', 'game.en.269', 'game.en.270', 'game.en.271', 'game.en.272', 'game.en.273', 'game.en.274', 'game.en.275', 'game.en.276', 'game.en.277', 'game.en.278', 'game.en.279', 'game.en.280'},
		date = 20230818,
		cross = 'crosscraft.en.15'
	},
	[29414] = {
		id = 29414,
		service = 'crosscraft',
		servers = {'game.en.261', 'game.en.262', 'game.en.263', 'game.en.264', 'game.en.265', 'game.en.266', 'game.en.267', 'game.en.268', 'game.en.269', 'game.en.270', 'game.en.271', 'game.en.272', 'game.en.273', 'game.en.274', 'game.en.275', 'game.en.276', 'game.en.277', 'game.en.278', 'game.en.279', 'game.en.280'},
		date = 20230821,
		cross = 'crosscraft.en.15'
	},
	[29415] = {
		id = 29415,
		service = 'crosscraft',
		servers = {'game.en.261', 'game.en.262', 'game.en.263', 'game.en.264', 'game.en.265', 'game.en.266', 'game.en.267', 'game.en.268', 'game.en.269', 'game.en.270', 'game.en.271', 'game.en.272', 'game.en.273', 'game.en.274', 'game.en.275', 'game.en.276', 'game.en.277', 'game.en.278', 'game.en.279', 'game.en.280'},
		date = 20230825,
		cross = 'crosscraft.en.15'
	},
	[29416] = {
		id = 29416,
		service = 'crosscraft',
		servers = {'game.en.261', 'game.en.262', 'game.en.263', 'game.en.264', 'game.en.265', 'game.en.266', 'game.en.267', 'game.en.268', 'game.en.269', 'game.en.270', 'game.en.271', 'game.en.272', 'game.en.273', 'game.en.274', 'game.en.275', 'game.en.276', 'game.en.277', 'game.en.278', 'game.en.279', 'game.en.280'},
		date = 20230828,
		cross = 'crosscraft.en.15'
	},
	[29417] = {
		id = 29417,
		service = 'crosscraft',
		servers = {'game.en.261', 'game.en.262', 'game.en.263', 'game.en.264', 'game.en.265', 'game.en.266', 'game.en.267', 'game.en.268', 'game.en.269', 'game.en.270', 'game.en.271', 'game.en.272', 'game.en.273', 'game.en.274', 'game.en.275', 'game.en.276', 'game.en.277', 'game.en.278', 'game.en.279', 'game.en.280'},
		date = 20230901,
		cross = 'crosscraft.en.15'
	},
	[29418] = {
		id = 29418,
		service = 'crosscraft',
		servers = {'game.en.261', 'game.en.262', 'game.en.263', 'game.en.264', 'game.en.265', 'game.en.266', 'game.en.267', 'game.en.268', 'game.en.269', 'game.en.270', 'game.en.271', 'game.en.272', 'game.en.273', 'game.en.274', 'game.en.275', 'game.en.276', 'game.en.277', 'game.en.278', 'game.en.279', 'game.en.280'},
		date = 20230904,
		cross = 'crosscraft.en.15'
	},
	[29419] = {
		id = 29419,
		service = 'crosscraft',
		servers = {'game.en.261', 'game.en.262', 'game.en.263', 'game.en.264', 'game.en.265', 'game.en.266', 'game.en.267', 'game.en.268', 'game.en.269', 'game.en.270', 'game.en.271', 'game.en.272', 'game.en.273', 'game.en.274', 'game.en.275', 'game.en.276', 'game.en.277', 'game.en.278', 'game.en.279', 'game.en.280'},
		date = 20230908,
		cross = 'crosscraft.en.15'
	},
	[29420] = {
		id = 29420,
		service = 'crosscraft',
		servers = {'game.en.261', 'game.en.262', 'game.en.263', 'game.en.264', 'game.en.265', 'game.en.266', 'game.en.267', 'game.en.268', 'game.en.269', 'game.en.270', 'game.en.271', 'game.en.272', 'game.en.273', 'game.en.274', 'game.en.275', 'game.en.276', 'game.en.277', 'game.en.278', 'game.en.279', 'game.en.280'},
		date = 20230911,
		cross = 'crosscraft.en.15'
	},
	[29421] = {
		id = 29421,
		service = 'crosscraft',
		servers = {'game.en.261', 'game.en.262', 'game.en.263', 'game.en.264', 'game.en.265', 'game.en.266', 'game.en.267', 'game.en.268', 'game.en.269', 'game.en.270', 'game.en.271', 'game.en.272', 'game.en.273', 'game.en.274', 'game.en.275', 'game.en.276', 'game.en.277', 'game.en.278', 'game.en.279', 'game.en.280'},
		date = 20230918,
		cross = 'crosscraft.en.15'
	},
	[29422] = {
		id = 29422,
		service = 'crosscraft',
		servers = {'game.en.261', 'game.en.262', 'game.en.263', 'game.en.264', 'game.en.265', 'game.en.266', 'game.en.267', 'game.en.268', 'game.en.269', 'game.en.270', 'game.en.271', 'game.en.272', 'game.en.273', 'game.en.274', 'game.en.275', 'game.en.276', 'game.en.277', 'game.en.278', 'game.en.279', 'game.en.280'},
		date = 20230922,
		cross = 'crosscraft.en.15'
	},
	[29423] = {
		id = 29423,
		service = 'crosscraft',
		servers = {'game.en.261', 'game.en.262', 'game.en.263', 'game.en.264', 'game.en.265', 'game.en.266', 'game.en.267', 'game.en.268', 'game.en.269', 'game.en.270', 'game.en.271', 'game.en.272', 'game.en.273', 'game.en.274', 'game.en.275', 'game.en.276', 'game.en.277', 'game.en.278', 'game.en.279', 'game.en.280'},
		date = 20230925,
		cross = 'crosscraft.en.15'
	},
	[29424] = {
		id = 29424,
		service = 'crosscraft',
		servers = {'game.en.261', 'game.en.262', 'game.en.263', 'game.en.264', 'game.en.265', 'game.en.266', 'game.en.267', 'game.en.268', 'game.en.269', 'game.en.270', 'game.en.271', 'game.en.272', 'game.en.273', 'game.en.274', 'game.en.275', 'game.en.276', 'game.en.277', 'game.en.278', 'game.en.279', 'game.en.280'},
		date = 20230929,
		cross = 'crosscraft.en.15'
	},
	[29425] = {
		id = 29425,
		service = 'crosscraft',
		servers = {'game.en.261', 'game.en.262', 'game.en.263', 'game.en.264', 'game.en.265', 'game.en.266', 'game.en.267', 'game.en.268', 'game.en.269', 'game.en.270', 'game.en.271', 'game.en.272', 'game.en.273', 'game.en.274', 'game.en.275', 'game.en.276', 'game.en.277', 'game.en.278', 'game.en.279', 'game.en.280'},
		date = 20231002,
		cross = 'crosscraft.en.15'
	},
	[29500] = {
		id = 29500,
		service = 'crosscraft',
		servers = {'game.en.281', 'game.en.282', 'game.en.283', 'game.en.284', 'game.en.285', 'game.en.286', 'game.en.287', 'game.en.288', 'game.en.289', 'game.en.290', 'game.en.291', 'game.en.292', 'game.en.293', 'game.en.294', 'game.en.295', 'game.en.296', 'game.en.297', 'game.en.298'},
		date = 20230703,
		cross = 'crosscraft.en.16'
	},
	[29501] = {
		id = 29501,
		service = 'crosscraft',
		servers = {'game.en.281', 'game.en.282', 'game.en.283', 'game.en.284', 'game.en.285', 'game.en.286', 'game.en.287', 'game.en.288', 'game.en.289', 'game.en.290', 'game.en.291', 'game.en.292', 'game.en.293', 'game.en.294', 'game.en.295', 'game.en.296', 'game.en.297', 'game.en.298'},
		date = 20230707,
		cross = 'crosscraft.en.16'
	},
	[29502] = {
		id = 29502,
		service = 'crosscraft',
		servers = {'game.en.281', 'game.en.282', 'game.en.283', 'game.en.284', 'game.en.285', 'game.en.286', 'game.en.287', 'game.en.288', 'game.en.289', 'game.en.290', 'game.en.291', 'game.en.292', 'game.en.293', 'game.en.294', 'game.en.295', 'game.en.296', 'game.en.297', 'game.en.298'},
		date = 20230711,
		cross = 'crosscraft.en.16'
	},
	[29503] = {
		id = 29503,
		service = 'crosscraft',
		servers = {'game.en.281', 'game.en.282', 'game.en.283', 'game.en.284', 'game.en.285', 'game.en.286', 'game.en.287', 'game.en.288', 'game.en.289', 'game.en.290', 'game.en.291', 'game.en.292', 'game.en.293', 'game.en.294', 'game.en.295', 'game.en.296', 'game.en.297', 'game.en.298'},
		date = 20230714,
		cross = 'crosscraft.en.16'
	},
	[29504] = {
		id = 29504,
		service = 'crosscraft',
		servers = {'game.en.281', 'game.en.282', 'game.en.283', 'game.en.284', 'game.en.285', 'game.en.286', 'game.en.287', 'game.en.288', 'game.en.289', 'game.en.290', 'game.en.291', 'game.en.292', 'game.en.293', 'game.en.294', 'game.en.295', 'game.en.296', 'game.en.297', 'game.en.298'},
		date = 20230717,
		cross = 'crosscraft.en.16'
	},
	[29505] = {
		id = 29505,
		service = 'crosscraft',
		servers = {'game.en.281', 'game.en.282', 'game.en.283', 'game.en.284', 'game.en.285', 'game.en.286', 'game.en.287', 'game.en.288', 'game.en.289', 'game.en.290', 'game.en.291', 'game.en.292', 'game.en.293', 'game.en.294', 'game.en.295', 'game.en.296', 'game.en.297', 'game.en.298'},
		date = 20230721,
		cross = 'crosscraft.en.16'
	},
	[29506] = {
		id = 29506,
		service = 'crosscraft',
		servers = {'game.en.281', 'game.en.282', 'game.en.283', 'game.en.284', 'game.en.285', 'game.en.286', 'game.en.287', 'game.en.288', 'game.en.289', 'game.en.290', 'game.en.291', 'game.en.292', 'game.en.293', 'game.en.294', 'game.en.295', 'game.en.296', 'game.en.297', 'game.en.298'},
		date = 20230724,
		cross = 'crosscraft.en.16'
	},
	[29507] = {
		id = 29507,
		service = 'crosscraft',
		servers = {'game.en.281', 'game.en.282', 'game.en.283', 'game.en.284', 'game.en.285', 'game.en.286', 'game.en.287', 'game.en.288', 'game.en.289', 'game.en.290', 'game.en.291', 'game.en.292', 'game.en.293', 'game.en.294', 'game.en.295', 'game.en.296', 'game.en.297', 'game.en.298'},
		date = 20230728,
		cross = 'crosscraft.en.16'
	},
	[29508] = {
		id = 29508,
		service = 'crosscraft',
		servers = {'game.en.281', 'game.en.282', 'game.en.283', 'game.en.284', 'game.en.285', 'game.en.286', 'game.en.287', 'game.en.288', 'game.en.289', 'game.en.290', 'game.en.291', 'game.en.292', 'game.en.293', 'game.en.294', 'game.en.295', 'game.en.296', 'game.en.297', 'game.en.298'},
		date = 20230731,
		cross = 'crosscraft.en.16'
	},
	[29509] = {
		id = 29509,
		service = 'crosscraft',
		servers = {'game.en.281', 'game.en.282', 'game.en.283', 'game.en.284', 'game.en.285', 'game.en.286', 'game.en.287', 'game.en.288', 'game.en.289', 'game.en.290', 'game.en.291', 'game.en.292', 'game.en.293', 'game.en.294', 'game.en.295', 'game.en.296', 'game.en.297', 'game.en.298'},
		date = 20230804,
		cross = 'crosscraft.en.16'
	},
	[29510] = {
		id = 29510,
		service = 'crosscraft',
		servers = {'game.en.281', 'game.en.282', 'game.en.283', 'game.en.284', 'game.en.285', 'game.en.286', 'game.en.287', 'game.en.288', 'game.en.289', 'game.en.290', 'game.en.291', 'game.en.292', 'game.en.293', 'game.en.294', 'game.en.295', 'game.en.296', 'game.en.297', 'game.en.298'},
		date = 20230807,
		cross = 'crosscraft.en.16'
	},
	[29511] = {
		id = 29511,
		service = 'crosscraft',
		servers = {'game.en.281', 'game.en.282', 'game.en.283', 'game.en.284', 'game.en.285', 'game.en.286', 'game.en.287', 'game.en.288', 'game.en.289', 'game.en.290', 'game.en.291', 'game.en.292', 'game.en.293', 'game.en.294', 'game.en.295', 'game.en.296', 'game.en.297', 'game.en.298'},
		date = 20230811,
		cross = 'crosscraft.en.16'
	},
	[29512] = {
		id = 29512,
		service = 'crosscraft',
		servers = {'game.en.281', 'game.en.282', 'game.en.283', 'game.en.284', 'game.en.285', 'game.en.286', 'game.en.287', 'game.en.288', 'game.en.289', 'game.en.290', 'game.en.291', 'game.en.292', 'game.en.293', 'game.en.294', 'game.en.295', 'game.en.296', 'game.en.297', 'game.en.298'},
		date = 20230814,
		cross = 'crosscraft.en.16'
	},
	[29513] = {
		id = 29513,
		service = 'crosscraft',
		servers = {'game.en.281', 'game.en.282', 'game.en.283', 'game.en.284', 'game.en.285', 'game.en.286', 'game.en.287', 'game.en.288', 'game.en.289', 'game.en.290', 'game.en.291', 'game.en.292', 'game.en.293', 'game.en.294', 'game.en.295', 'game.en.296', 'game.en.297', 'game.en.298'},
		date = 20230818,
		cross = 'crosscraft.en.16'
	},
	[29514] = {
		id = 29514,
		service = 'crosscraft',
		servers = {'game.en.281', 'game.en.282', 'game.en.283', 'game.en.284', 'game.en.285', 'game.en.286', 'game.en.287', 'game.en.288', 'game.en.289', 'game.en.290', 'game.en.291', 'game.en.292', 'game.en.293', 'game.en.294', 'game.en.295', 'game.en.296', 'game.en.297', 'game.en.298'},
		date = 20230821,
		cross = 'crosscraft.en.16'
	},
	[29515] = {
		id = 29515,
		service = 'crosscraft',
		servers = {'game.en.281', 'game.en.282', 'game.en.283', 'game.en.284', 'game.en.285', 'game.en.286', 'game.en.287', 'game.en.288', 'game.en.289', 'game.en.290', 'game.en.291', 'game.en.292', 'game.en.293', 'game.en.294', 'game.en.295', 'game.en.296', 'game.en.297', 'game.en.298'},
		date = 20230825,
		cross = 'crosscraft.en.13'
	},
	[29516] = {
		id = 29516,
		service = 'crosscraft',
		servers = {'game.en.281', 'game.en.282', 'game.en.283', 'game.en.284', 'game.en.285', 'game.en.286', 'game.en.287', 'game.en.288', 'game.en.289', 'game.en.290', 'game.en.291', 'game.en.292', 'game.en.293', 'game.en.294', 'game.en.295', 'game.en.296', 'game.en.297', 'game.en.298'},
		date = 20230828,
		cross = 'crosscraft.en.16'
	},
	[29517] = {
		id = 29517,
		service = 'crosscraft',
		servers = {'game.en.281', 'game.en.282', 'game.en.283', 'game.en.284', 'game.en.285', 'game.en.286', 'game.en.287', 'game.en.288', 'game.en.289', 'game.en.290', 'game.en.291', 'game.en.292', 'game.en.293', 'game.en.294', 'game.en.295', 'game.en.296', 'game.en.297', 'game.en.298'},
		date = 20230901,
		cross = 'crosscraft.en.16'
	},
	[29518] = {
		id = 29518,
		service = 'crosscraft',
		servers = {'game.en.281', 'game.en.282', 'game.en.283', 'game.en.284', 'game.en.285', 'game.en.286', 'game.en.287', 'game.en.288', 'game.en.289', 'game.en.290', 'game.en.291', 'game.en.292', 'game.en.293', 'game.en.294', 'game.en.295', 'game.en.296', 'game.en.297', 'game.en.298'},
		date = 20230904,
		cross = 'crosscraft.en.16'
	},
	[29519] = {
		id = 29519,
		service = 'crosscraft',
		servers = {'game.en.281', 'game.en.282', 'game.en.283', 'game.en.284', 'game.en.285', 'game.en.286', 'game.en.287', 'game.en.288', 'game.en.289', 'game.en.290', 'game.en.291', 'game.en.292', 'game.en.293', 'game.en.294', 'game.en.295', 'game.en.296', 'game.en.297', 'game.en.298'},
		date = 20230908,
		cross = 'crosscraft.en.16'
	},
	[29520] = {
		id = 29520,
		service = 'crosscraft',
		servers = {'game.en.281', 'game.en.282', 'game.en.283', 'game.en.284', 'game.en.285', 'game.en.286', 'game.en.287', 'game.en.288', 'game.en.289', 'game.en.290', 'game.en.291', 'game.en.292', 'game.en.293', 'game.en.294', 'game.en.295', 'game.en.296', 'game.en.297', 'game.en.298'},
		date = 20230911,
		cross = 'crosscraft.en.16'
	},
	[29521] = {
		id = 29521,
		service = 'crosscraft',
		servers = {'game.en.281', 'game.en.282', 'game.en.283', 'game.en.284', 'game.en.285', 'game.en.286', 'game.en.287', 'game.en.288', 'game.en.289', 'game.en.290', 'game.en.291', 'game.en.292', 'game.en.293', 'game.en.294', 'game.en.295', 'game.en.296', 'game.en.297', 'game.en.298'},
		date = 20230918,
		cross = 'crosscraft.en.16'
	},
	[29522] = {
		id = 29522,
		service = 'crosscraft',
		servers = {'game.en.281', 'game.en.282', 'game.en.283', 'game.en.284', 'game.en.285', 'game.en.286', 'game.en.287', 'game.en.288', 'game.en.289', 'game.en.290', 'game.en.291', 'game.en.292', 'game.en.293', 'game.en.294', 'game.en.295', 'game.en.296', 'game.en.297', 'game.en.298'},
		date = 20230922,
		cross = 'crosscraft.en.16'
	},
	[29523] = {
		id = 29523,
		service = 'crosscraft',
		servers = {'game.en.281', 'game.en.282', 'game.en.283', 'game.en.284', 'game.en.285', 'game.en.286', 'game.en.287', 'game.en.288', 'game.en.289', 'game.en.290', 'game.en.291', 'game.en.292', 'game.en.293', 'game.en.294', 'game.en.295', 'game.en.296', 'game.en.297', 'game.en.298'},
		date = 20230925,
		cross = 'crosscraft.en.16'
	},
	[29524] = {
		id = 29524,
		service = 'crosscraft',
		servers = {'game.en.281', 'game.en.282', 'game.en.283', 'game.en.284', 'game.en.285', 'game.en.286', 'game.en.287', 'game.en.288', 'game.en.289', 'game.en.290', 'game.en.291', 'game.en.292', 'game.en.293', 'game.en.294', 'game.en.295', 'game.en.296', 'game.en.297', 'game.en.298'},
		date = 20230929,
		cross = 'crosscraft.en.16'
	},
	[29525] = {
		id = 29525,
		service = 'crosscraft',
		servers = {'game.en.281', 'game.en.282', 'game.en.283', 'game.en.284', 'game.en.285', 'game.en.286', 'game.en.287', 'game.en.288', 'game.en.289', 'game.en.290', 'game.en.291', 'game.en.292', 'game.en.293', 'game.en.294', 'game.en.295', 'game.en.296', 'game.en.297', 'game.en.298'},
		date = 20231002,
		cross = 'crosscraft.en.16'
	},
	[49000] = {
		id = 49000,
		service = 'crossmine',
		servers = {'game.en.201', 'game.en.202', 'game.en.203', 'game.en.204', 'game.en.205', 'game.en.206', 'game.en.207', 'game.en.208', 'game.en.209', 'game.en.210'},
		date = 20230704,
		cross = 'crossmine.en.21'
	},
	[49001] = {
		id = 49001,
		service = 'crossmine',
		servers = {'game.en.201', 'game.en.202', 'game.en.203', 'game.en.204', 'game.en.205', 'game.en.206', 'game.en.207', 'game.en.208', 'game.en.209', 'game.en.210'},
		date = 20230711,
		cross = 'crossmine.en.21'
	},
	[49002] = {
		id = 49002,
		service = 'crossmine',
		servers = {'game.en.201', 'game.en.202', 'game.en.203', 'game.en.204', 'game.en.205', 'game.en.206', 'game.en.207', 'game.en.208', 'game.en.209', 'game.en.210'},
		date = 20230718,
		cross = 'crossmine.en.21'
	},
	[49003] = {
		id = 49003,
		service = 'crossmine',
		servers = {'game.en.201', 'game.en.202', 'game.en.203', 'game.en.204', 'game.en.205', 'game.en.206', 'game.en.207', 'game.en.208', 'game.en.209', 'game.en.210'},
		date = 20230725,
		cross = 'crossmine.en.21'
	},
	[49004] = {
		id = 49004,
		service = 'crossmine',
		servers = {'game.en.201', 'game.en.202', 'game.en.203', 'game.en.204', 'game.en.205', 'game.en.206', 'game.en.207', 'game.en.208', 'game.en.209', 'game.en.210'},
		date = 20230801,
		cross = 'crossmine.en.21'
	},
	[49005] = {
		id = 49005,
		service = 'crossmine',
		servers = {'game.en.201', 'game.en.202', 'game.en.203', 'game.en.204', 'game.en.205', 'game.en.206', 'game.en.207', 'game.en.208', 'game.en.209', 'game.en.210'},
		date = 20230808,
		cross = 'crossmine.en.21'
	},
	[49006] = {
		id = 49006,
		service = 'crossmine',
		servers = {'game.en.201', 'game.en.202', 'game.en.203', 'game.en.204', 'game.en.205', 'game.en.206', 'game.en.207', 'game.en.208', 'game.en.209', 'game.en.210'},
		date = 20230815,
		cross = 'crossmine.en.21'
	},
	[49007] = {
		id = 49007,
		service = 'crossmine',
		servers = {'game.en.201', 'game.en.202', 'game.en.203', 'game.en.204', 'game.en.205', 'game.en.206', 'game.en.207', 'game.en.208', 'game.en.209', 'game.en.210'},
		date = 20230822,
		cross = 'crossmine.en.21'
	},
	[49008] = {
		id = 49008,
		service = 'crossmine',
		servers = {'game.en.201', 'game.en.202', 'game.en.203', 'game.en.204', 'game.en.205', 'game.en.206', 'game.en.207', 'game.en.208', 'game.en.209', 'game.en.210'},
		date = 20230829,
		cross = 'crossmine.en.21'
	},
	[49009] = {
		id = 49009,
		service = 'crossmine',
		servers = {'game.en.201', 'game.en.202', 'game.en.203', 'game.en.204', 'game.en.205', 'game.en.206', 'game.en.207', 'game.en.208', 'game.en.209', 'game.en.210'},
		date = 20230905,
		cross = 'crossmine.en.21'
	},
	[49010] = {
		id = 49010,
		service = 'crossmine',
		servers = {'game.en.201', 'game.en.202', 'game.en.203', 'game.en.204', 'game.en.205', 'game.en.206', 'game.en.207', 'game.en.208', 'game.en.209', 'game.en.210'},
		date = 20230919,
		cross = 'crossmine.en.21'
	},
	[49011] = {
		id = 49011,
		service = 'crossmine',
		servers = {'game.en.201', 'game.en.202', 'game.en.203', 'game.en.204', 'game.en.205', 'game.en.206', 'game.en.207', 'game.en.208', 'game.en.209', 'game.en.210'},
		date = 20230926,
		cross = 'crossmine.en.21'
	},
	[49012] = {
		id = 49012,
		service = 'crossmine',
		servers = {'game.en.201', 'game.en.202', 'game.en.203', 'game.en.204', 'game.en.205', 'game.en.206', 'game.en.207', 'game.en.208', 'game.en.209', 'game.en.210'},
		date = 20231003,
		cross = 'crossmine.en.21'
	},
	[49100] = {
		id = 49100,
		service = 'crossmine',
		servers = {'game.en.211', 'game.en.212', 'game.en.213', 'game.en.214', 'game.en.215', 'game.en.216', 'game.en.217', 'game.en.218', 'game.en.219', 'game.en.220'},
		date = 20230704,
		cross = 'crossmine.en.22'
	},
	[49101] = {
		id = 49101,
		service = 'crossmine',
		servers = {'game.en.211', 'game.en.212', 'game.en.213', 'game.en.214', 'game.en.215', 'game.en.216', 'game.en.217', 'game.en.218', 'game.en.219', 'game.en.220'},
		date = 20230711,
		cross = 'crossmine.en.22'
	},
	[49102] = {
		id = 49102,
		service = 'crossmine',
		servers = {'game.en.211', 'game.en.212', 'game.en.213', 'game.en.214', 'game.en.215', 'game.en.216', 'game.en.217', 'game.en.218', 'game.en.219', 'game.en.220'},
		date = 20230718,
		cross = 'crossmine.en.22'
	},
	[49103] = {
		id = 49103,
		service = 'crossmine',
		servers = {'game.en.211', 'game.en.212', 'game.en.213', 'game.en.214', 'game.en.215', 'game.en.216', 'game.en.217', 'game.en.218', 'game.en.219', 'game.en.220'},
		date = 20230725,
		cross = 'crossmine.en.22'
	},
	[49104] = {
		id = 49104,
		service = 'crossmine',
		servers = {'game.en.211', 'game.en.212', 'game.en.213', 'game.en.214', 'game.en.215', 'game.en.216', 'game.en.217', 'game.en.218', 'game.en.219', 'game.en.220'},
		date = 20230801,
		cross = 'crossmine.en.22'
	},
	[49105] = {
		id = 49105,
		service = 'crossmine',
		servers = {'game.en.211', 'game.en.212', 'game.en.213', 'game.en.214', 'game.en.215', 'game.en.216', 'game.en.217', 'game.en.218', 'game.en.219', 'game.en.220'},
		date = 20230808,
		cross = 'crossmine.en.22'
	},
	[49106] = {
		id = 49106,
		service = 'crossmine',
		servers = {'game.en.211', 'game.en.212', 'game.en.213', 'game.en.214', 'game.en.215', 'game.en.216', 'game.en.217', 'game.en.218', 'game.en.219', 'game.en.220'},
		date = 20230815,
		cross = 'crossmine.en.22'
	},
	[49107] = {
		id = 49107,
		service = 'crossmine',
		servers = {'game.en.211', 'game.en.212', 'game.en.213', 'game.en.214', 'game.en.215', 'game.en.216', 'game.en.217', 'game.en.218', 'game.en.219', 'game.en.220'},
		date = 20230822,
		cross = 'crossmine.en.22'
	},
	[49108] = {
		id = 49108,
		service = 'crossmine',
		servers = {'game.en.211', 'game.en.212', 'game.en.213', 'game.en.214', 'game.en.215', 'game.en.216', 'game.en.217', 'game.en.218', 'game.en.219', 'game.en.220'},
		date = 20230829,
		cross = 'crossmine.en.22'
	},
	[49109] = {
		id = 49109,
		service = 'crossmine',
		servers = {'game.en.211', 'game.en.212', 'game.en.213', 'game.en.214', 'game.en.215', 'game.en.216', 'game.en.217', 'game.en.218', 'game.en.219', 'game.en.220'},
		date = 20230905,
		cross = 'crossmine.en.22'
	},
	[49110] = {
		id = 49110,
		service = 'crossmine',
		servers = {'game.en.211', 'game.en.212', 'game.en.213', 'game.en.214', 'game.en.215', 'game.en.216', 'game.en.217', 'game.en.218', 'game.en.219', 'game.en.220'},
		date = 20230919,
		cross = 'crossmine.en.22'
	},
	[49111] = {
		id = 49111,
		service = 'crossmine',
		servers = {'game.en.211', 'game.en.212', 'game.en.213', 'game.en.214', 'game.en.215', 'game.en.216', 'game.en.217', 'game.en.218', 'game.en.219', 'game.en.220'},
		date = 20230926,
		cross = 'crossmine.en.22'
	},
	[49112] = {
		id = 49112,
		service = 'crossmine',
		servers = {'game.en.211', 'game.en.212', 'game.en.213', 'game.en.214', 'game.en.215', 'game.en.216', 'game.en.217', 'game.en.218', 'game.en.219', 'game.en.220'},
		date = 20231003,
		cross = 'crossmine.en.22'
	},
	[49200] = {
		id = 49200,
		service = 'crossmine',
		servers = {'game.en.221', 'game.en.222', 'game.en.223', 'game.en.224', 'game.en.225', 'game.en.226', 'game.en.227', 'game.en.228', 'game.en.229', 'game.en.230'},
		date = 20230704,
		cross = 'crossmine.en.23'
	},
	[49201] = {
		id = 49201,
		service = 'crossmine',
		servers = {'game.en.221', 'game.en.222', 'game.en.223', 'game.en.224', 'game.en.225', 'game.en.226', 'game.en.227', 'game.en.228', 'game.en.229', 'game.en.230'},
		date = 20230711,
		cross = 'crossmine.en.23'
	},
	[49202] = {
		id = 49202,
		service = 'crossmine',
		servers = {'game.en.221', 'game.en.222', 'game.en.223', 'game.en.224', 'game.en.225', 'game.en.226', 'game.en.227', 'game.en.228', 'game.en.229', 'game.en.230'},
		date = 20230718,
		cross = 'crossmine.en.23'
	},
	[49203] = {
		id = 49203,
		service = 'crossmine',
		servers = {'game.en.221', 'game.en.222', 'game.en.223', 'game.en.224', 'game.en.225', 'game.en.226', 'game.en.227', 'game.en.228', 'game.en.229', 'game.en.230'},
		date = 20230725,
		cross = 'crossmine.en.23'
	},
	[49204] = {
		id = 49204,
		service = 'crossmine',
		servers = {'game.en.221', 'game.en.222', 'game.en.223', 'game.en.224', 'game.en.225', 'game.en.226', 'game.en.227', 'game.en.228', 'game.en.229', 'game.en.230'},
		date = 20230801,
		cross = 'crossmine.en.23'
	},
	[49205] = {
		id = 49205,
		service = 'crossmine',
		servers = {'game.en.221', 'game.en.222', 'game.en.223', 'game.en.224', 'game.en.225', 'game.en.226', 'game.en.227', 'game.en.228', 'game.en.229', 'game.en.230'},
		date = 20230808,
		cross = 'crossmine.en.23'
	},
	[49206] = {
		id = 49206,
		service = 'crossmine',
		servers = {'game.en.221', 'game.en.222', 'game.en.223', 'game.en.224', 'game.en.225', 'game.en.226', 'game.en.227', 'game.en.228', 'game.en.229', 'game.en.230'},
		date = 20230815,
		cross = 'crossmine.en.23'
	},
	[49207] = {
		id = 49207,
		service = 'crossmine',
		servers = {'game.en.221', 'game.en.222', 'game.en.223', 'game.en.224', 'game.en.225', 'game.en.226', 'game.en.227', 'game.en.228', 'game.en.229', 'game.en.230'},
		date = 20230822,
		cross = 'crossmine.en.23'
	},
	[49208] = {
		id = 49208,
		service = 'crossmine',
		servers = {'game.en.221', 'game.en.222', 'game.en.223', 'game.en.224', 'game.en.225', 'game.en.226', 'game.en.227', 'game.en.228', 'game.en.229', 'game.en.230'},
		date = 20230829,
		cross = 'crossmine.en.23'
	},
	[49209] = {
		id = 49209,
		service = 'crossmine',
		servers = {'game.en.221', 'game.en.222', 'game.en.223', 'game.en.224', 'game.en.225', 'game.en.226', 'game.en.227', 'game.en.228', 'game.en.229', 'game.en.230'},
		date = 20230905,
		cross = 'crossmine.en.23'
	},
	[49210] = {
		id = 49210,
		service = 'crossmine',
		servers = {'game.en.221', 'game.en.222', 'game.en.223', 'game.en.224', 'game.en.225', 'game.en.226', 'game.en.227', 'game.en.228', 'game.en.229', 'game.en.230'},
		date = 20230919,
		cross = 'crossmine.en.23'
	},
	[49211] = {
		id = 49211,
		service = 'crossmine',
		servers = {'game.en.221', 'game.en.222', 'game.en.223', 'game.en.224', 'game.en.225', 'game.en.226', 'game.en.227', 'game.en.228', 'game.en.229', 'game.en.230'},
		date = 20230926,
		cross = 'crossmine.en.23'
	},
	[49212] = {
		id = 49212,
		service = 'crossmine',
		servers = {'game.en.221', 'game.en.222', 'game.en.223', 'game.en.224', 'game.en.225', 'game.en.226', 'game.en.227', 'game.en.228', 'game.en.229', 'game.en.230'},
		date = 20231003,
		cross = 'crossmine.en.23'
	},
	[49300] = {
		id = 49300,
		service = 'crossmine',
		servers = {'game.en.231', 'game.en.232', 'game.en.233', 'game.en.234', 'game.en.235', 'game.en.236', 'game.en.237', 'game.en.238', 'game.en.239', 'game.en.240'},
		date = 20230704,
		cross = 'crossmine.en.24'
	},
	[49301] = {
		id = 49301,
		service = 'crossmine',
		servers = {'game.en.231', 'game.en.232', 'game.en.233', 'game.en.234', 'game.en.235', 'game.en.236', 'game.en.237', 'game.en.238', 'game.en.239', 'game.en.240'},
		date = 20230711,
		cross = 'crossmine.en.24'
	},
	[49302] = {
		id = 49302,
		service = 'crossmine',
		servers = {'game.en.231', 'game.en.232', 'game.en.233', 'game.en.234', 'game.en.235', 'game.en.236', 'game.en.237', 'game.en.238', 'game.en.239', 'game.en.240'},
		date = 20230718,
		cross = 'crossmine.en.24'
	},
	[49303] = {
		id = 49303,
		service = 'crossmine',
		servers = {'game.en.231', 'game.en.232', 'game.en.233', 'game.en.234', 'game.en.235', 'game.en.236', 'game.en.237', 'game.en.238', 'game.en.239', 'game.en.240'},
		date = 20230725,
		cross = 'crossmine.en.24'
	},
	[49304] = {
		id = 49304,
		service = 'crossmine',
		servers = {'game.en.231', 'game.en.232', 'game.en.233', 'game.en.234', 'game.en.235', 'game.en.236', 'game.en.237', 'game.en.238', 'game.en.239', 'game.en.240'},
		date = 20230801,
		cross = 'crossmine.en.24'
	},
	[49305] = {
		id = 49305,
		service = 'crossmine',
		servers = {'game.en.231', 'game.en.232', 'game.en.233', 'game.en.234', 'game.en.235', 'game.en.236', 'game.en.237', 'game.en.238', 'game.en.239', 'game.en.240'},
		date = 20230808,
		cross = 'crossmine.en.24'
	},
	[49306] = {
		id = 49306,
		service = 'crossmine',
		servers = {'game.en.231', 'game.en.232', 'game.en.233', 'game.en.234', 'game.en.235', 'game.en.236', 'game.en.237', 'game.en.238', 'game.en.239', 'game.en.240'},
		date = 20230815,
		cross = 'crossmine.en.24'
	},
	[49307] = {
		id = 49307,
		service = 'crossmine',
		servers = {'game.en.231', 'game.en.232', 'game.en.233', 'game.en.234', 'game.en.235', 'game.en.236', 'game.en.237', 'game.en.238', 'game.en.239', 'game.en.240'},
		date = 20230822,
		cross = 'crossmine.en.24'
	},
	[49308] = {
		id = 49308,
		service = 'crossmine',
		servers = {'game.en.231', 'game.en.232', 'game.en.233', 'game.en.234', 'game.en.235', 'game.en.236', 'game.en.237', 'game.en.238', 'game.en.239', 'game.en.240'},
		date = 20230829,
		cross = 'crossmine.en.24'
	},
	[49309] = {
		id = 49309,
		service = 'crossmine',
		servers = {'game.en.231', 'game.en.232', 'game.en.233', 'game.en.234', 'game.en.235', 'game.en.236', 'game.en.237', 'game.en.238', 'game.en.239', 'game.en.240'},
		date = 20230905,
		cross = 'crossmine.en.24'
	},
	[49310] = {
		id = 49310,
		service = 'crossmine',
		servers = {'game.en.231', 'game.en.232', 'game.en.233', 'game.en.234', 'game.en.235', 'game.en.236', 'game.en.237', 'game.en.238', 'game.en.239', 'game.en.240'},
		date = 20230919,
		cross = 'crossmine.en.24'
	},
	[49311] = {
		id = 49311,
		service = 'crossmine',
		servers = {'game.en.231', 'game.en.232', 'game.en.233', 'game.en.234', 'game.en.235', 'game.en.236', 'game.en.237', 'game.en.238', 'game.en.239', 'game.en.240'},
		date = 20230926,
		cross = 'crossmine.en.24'
	},
	[49312] = {
		id = 49312,
		service = 'crossmine',
		servers = {'game.en.231', 'game.en.232', 'game.en.233', 'game.en.234', 'game.en.235', 'game.en.236', 'game.en.237', 'game.en.238', 'game.en.239', 'game.en.240'},
		date = 20231003,
		cross = 'crossmine.en.24'
	},
	[49400] = {
		id = 49400,
		service = 'crossmine',
		servers = {'game.en.241', 'game.en.242', 'game.en.243', 'game.en.244', 'game.en.245', 'game.en.246', 'game.en.247', 'game.en.248', 'game.en.249', 'game.en.250'},
		date = 20230704,
		cross = 'crossmine.en.25'
	},
	[49401] = {
		id = 49401,
		service = 'crossmine',
		servers = {'game.en.241', 'game.en.242', 'game.en.243', 'game.en.244', 'game.en.245', 'game.en.246', 'game.en.247', 'game.en.248', 'game.en.249', 'game.en.250'},
		date = 20230711,
		cross = 'crossmine.en.25'
	},
	[49402] = {
		id = 49402,
		service = 'crossmine',
		servers = {'game.en.241', 'game.en.242', 'game.en.243', 'game.en.244', 'game.en.245', 'game.en.246', 'game.en.247', 'game.en.248', 'game.en.249', 'game.en.250'},
		date = 20230718,
		cross = 'crossmine.en.25'
	},
	[49403] = {
		id = 49403,
		service = 'crossmine',
		servers = {'game.en.241', 'game.en.242', 'game.en.243', 'game.en.244', 'game.en.245', 'game.en.246', 'game.en.247', 'game.en.248', 'game.en.249', 'game.en.250'},
		date = 20230725,
		cross = 'crossmine.en.25'
	},
	[49404] = {
		id = 49404,
		service = 'crossmine',
		servers = {'game.en.241', 'game.en.242', 'game.en.243', 'game.en.244', 'game.en.245', 'game.en.246', 'game.en.247', 'game.en.248', 'game.en.249', 'game.en.250'},
		date = 20230801,
		cross = 'crossmine.en.25'
	},
	[49405] = {
		id = 49405,
		service = 'crossmine',
		servers = {'game.en.241', 'game.en.242', 'game.en.243', 'game.en.244', 'game.en.245', 'game.en.246', 'game.en.247', 'game.en.248', 'game.en.249', 'game.en.250'},
		date = 20230808,
		cross = 'crossmine.en.25'
	},
	[49406] = {
		id = 49406,
		service = 'crossmine',
		servers = {'game.en.241', 'game.en.242', 'game.en.243', 'game.en.244', 'game.en.245', 'game.en.246', 'game.en.247', 'game.en.248', 'game.en.249', 'game.en.250'},
		date = 20230815,
		cross = 'crossmine.en.25'
	},
	[49407] = {
		id = 49407,
		service = 'crossmine',
		servers = {'game.en.241', 'game.en.242', 'game.en.243', 'game.en.244', 'game.en.245', 'game.en.246', 'game.en.247', 'game.en.248', 'game.en.249', 'game.en.250'},
		date = 20230822,
		cross = 'crossmine.en.25'
	},
	[49408] = {
		id = 49408,
		service = 'crossmine',
		servers = {'game.en.241', 'game.en.242', 'game.en.243', 'game.en.244', 'game.en.245', 'game.en.246', 'game.en.247', 'game.en.248', 'game.en.249', 'game.en.250'},
		date = 20230829,
		cross = 'crossmine.en.25'
	},
	[49409] = {
		id = 49409,
		service = 'crossmine',
		servers = {'game.en.241', 'game.en.242', 'game.en.243', 'game.en.244', 'game.en.245', 'game.en.246', 'game.en.247', 'game.en.248', 'game.en.249', 'game.en.250'},
		date = 20230905,
		cross = 'crossmine.en.25'
	},
	[49410] = {
		id = 49410,
		service = 'crossmine',
		servers = {'game.en.241', 'game.en.242', 'game.en.243', 'game.en.244', 'game.en.245', 'game.en.246', 'game.en.247', 'game.en.248', 'game.en.249', 'game.en.250'},
		date = 20230919,
		cross = 'crossmine.en.24'
	},
	[49411] = {
		id = 49411,
		service = 'crossmine',
		servers = {'game.en.241', 'game.en.242', 'game.en.243', 'game.en.244', 'game.en.245', 'game.en.246', 'game.en.247', 'game.en.248', 'game.en.249', 'game.en.250'},
		date = 20230926,
		cross = 'crossmine.en.25'
	},
	[49412] = {
		id = 49412,
		service = 'crossmine',
		servers = {'game.en.241', 'game.en.242', 'game.en.243', 'game.en.244', 'game.en.245', 'game.en.246', 'game.en.247', 'game.en.248', 'game.en.249', 'game.en.250'},
		date = 20231003,
		cross = 'crossmine.en.25'
	},
	[49500] = {
		id = 49500,
		service = 'crossmine',
		servers = {'game.en.251', 'game.en.252', 'game.en.253', 'game.en.254', 'game.en.255', 'game.en.256', 'game.en.257', 'game.en.258', 'game.en.259', 'game.en.260'},
		date = 20230704,
		cross = 'crossmine.en.26'
	},
	[49501] = {
		id = 49501,
		service = 'crossmine',
		servers = {'game.en.251', 'game.en.252', 'game.en.253', 'game.en.254', 'game.en.255', 'game.en.256', 'game.en.257', 'game.en.258', 'game.en.259', 'game.en.260'},
		date = 20230711,
		cross = 'crossmine.en.26'
	},
	[49502] = {
		id = 49502,
		service = 'crossmine',
		servers = {'game.en.251', 'game.en.252', 'game.en.253', 'game.en.254', 'game.en.255', 'game.en.256', 'game.en.257', 'game.en.258', 'game.en.259', 'game.en.260'},
		date = 20230718,
		cross = 'crossmine.en.26'
	},
	[49503] = {
		id = 49503,
		service = 'crossmine',
		servers = {'game.en.251', 'game.en.252', 'game.en.253', 'game.en.254', 'game.en.255', 'game.en.256', 'game.en.257', 'game.en.258', 'game.en.259', 'game.en.260'},
		date = 20230725,
		cross = 'crossmine.en.26'
	},
	[49504] = {
		id = 49504,
		service = 'crossmine',
		servers = {'game.en.251', 'game.en.252', 'game.en.253', 'game.en.254', 'game.en.255', 'game.en.256', 'game.en.257', 'game.en.258', 'game.en.259', 'game.en.260'},
		date = 20230801,
		cross = 'crossmine.en.26'
	},
	[49505] = {
		id = 49505,
		service = 'crossmine',
		servers = {'game.en.251', 'game.en.252', 'game.en.253', 'game.en.254', 'game.en.255', 'game.en.256', 'game.en.257', 'game.en.258', 'game.en.259', 'game.en.260'},
		date = 20230808,
		cross = 'crossmine.en.26'
	},
	[49506] = {
		id = 49506,
		service = 'crossmine',
		servers = {'game.en.251', 'game.en.252', 'game.en.253', 'game.en.254', 'game.en.255', 'game.en.256', 'game.en.257', 'game.en.258', 'game.en.259', 'game.en.260'},
		date = 20230815,
		cross = 'crossmine.en.26'
	},
	[49507] = {
		id = 49507,
		service = 'crossmine',
		servers = {'game.en.251', 'game.en.252', 'game.en.253', 'game.en.254', 'game.en.255', 'game.en.256', 'game.en.257', 'game.en.258', 'game.en.259', 'game.en.260'},
		date = 20230822,
		cross = 'crossmine.en.26'
	},
	[49508] = {
		id = 49508,
		service = 'crossmine',
		servers = {'game.en.251', 'game.en.252', 'game.en.253', 'game.en.254', 'game.en.255', 'game.en.256', 'game.en.257', 'game.en.258', 'game.en.259', 'game.en.260'},
		date = 20230829,
		cross = 'crossmine.en.26'
	},
	[49509] = {
		id = 49509,
		service = 'crossmine',
		servers = {'game.en.251', 'game.en.252', 'game.en.253', 'game.en.254', 'game.en.255', 'game.en.256', 'game.en.257', 'game.en.258', 'game.en.259', 'game.en.260'},
		date = 20230905,
		cross = 'crossmine.en.26'
	},
	[49510] = {
		id = 49510,
		service = 'crossmine',
		servers = {'game.en.251', 'game.en.252', 'game.en.253', 'game.en.254', 'game.en.255', 'game.en.256', 'game.en.257', 'game.en.258', 'game.en.259', 'game.en.260'},
		date = 20230919,
		cross = 'crossmine.en.26'
	},
	[49511] = {
		id = 49511,
		service = 'crossmine',
		servers = {'game.en.251', 'game.en.252', 'game.en.253', 'game.en.254', 'game.en.255', 'game.en.256', 'game.en.257', 'game.en.258', 'game.en.259', 'game.en.260'},
		date = 20230926,
		cross = 'crossmine.en.26'
	},
	[49512] = {
		id = 49512,
		service = 'crossmine',
		servers = {'game.en.251', 'game.en.252', 'game.en.253', 'game.en.254', 'game.en.255', 'game.en.256', 'game.en.257', 'game.en.258', 'game.en.259', 'game.en.260'},
		date = 20231003,
		cross = 'crossmine.en.26'
	},
	[49600] = {
		id = 49600,
		service = 'crossmine',
		servers = {'game.en.261', 'game.en.262', 'game.en.263', 'game.en.264', 'game.en.265', 'game.en.266', 'game.en.267', 'game.en.268', 'game.en.269', 'game.en.270'},
		date = 20230704,
		cross = 'crossmine.en.27'
	},
	[49601] = {
		id = 49601,
		service = 'crossmine',
		servers = {'game.en.261', 'game.en.262', 'game.en.263', 'game.en.264', 'game.en.265', 'game.en.266', 'game.en.267', 'game.en.268', 'game.en.269', 'game.en.270'},
		date = 20230711,
		cross = 'crossmine.en.27'
	},
	[49602] = {
		id = 49602,
		service = 'crossmine',
		servers = {'game.en.261', 'game.en.262', 'game.en.263', 'game.en.264', 'game.en.265', 'game.en.266', 'game.en.267', 'game.en.268', 'game.en.269', 'game.en.270'},
		date = 20230718,
		cross = 'crossmine.en.27'
	},
	[49603] = {
		id = 49603,
		service = 'crossmine',
		servers = {'game.en.261', 'game.en.262', 'game.en.263', 'game.en.264', 'game.en.265', 'game.en.266', 'game.en.267', 'game.en.268', 'game.en.269', 'game.en.270'},
		date = 20230725,
		cross = 'crossmine.en.27'
	},
	[49604] = {
		id = 49604,
		service = 'crossmine',
		servers = {'game.en.261', 'game.en.262', 'game.en.263', 'game.en.264', 'game.en.265', 'game.en.266', 'game.en.267', 'game.en.268', 'game.en.269', 'game.en.270'},
		date = 20230801,
		cross = 'crossmine.en.27'
	},
	[49605] = {
		id = 49605,
		service = 'crossmine',
		servers = {'game.en.261', 'game.en.262', 'game.en.263', 'game.en.264', 'game.en.265', 'game.en.266', 'game.en.267', 'game.en.268', 'game.en.269', 'game.en.270'},
		date = 20230808,
		cross = 'crossmine.en.27'
	},
	[49606] = {
		id = 49606,
		service = 'crossmine',
		servers = {'game.en.261', 'game.en.262', 'game.en.263', 'game.en.264', 'game.en.265', 'game.en.266', 'game.en.267', 'game.en.268', 'game.en.269', 'game.en.270'},
		date = 20230815,
		cross = 'crossmine.en.27'
	},
	[49607] = {
		id = 49607,
		service = 'crossmine',
		servers = {'game.en.261', 'game.en.262', 'game.en.263', 'game.en.264', 'game.en.265', 'game.en.266', 'game.en.267', 'game.en.268', 'game.en.269', 'game.en.270'},
		date = 20230822,
		cross = 'crossmine.en.27'
	},
	[49608] = {
		id = 49608,
		service = 'crossmine',
		servers = {'game.en.261', 'game.en.262', 'game.en.263', 'game.en.264', 'game.en.265', 'game.en.266', 'game.en.267', 'game.en.268', 'game.en.269', 'game.en.270'},
		date = 20230829,
		cross = 'crossmine.en.27'
	},
	[49609] = {
		id = 49609,
		service = 'crossmine',
		servers = {'game.en.261', 'game.en.262', 'game.en.263', 'game.en.264', 'game.en.265', 'game.en.266', 'game.en.267', 'game.en.268', 'game.en.269', 'game.en.270'},
		date = 20230905,
		cross = 'crossmine.en.27'
	},
	[49610] = {
		id = 49610,
		service = 'crossmine',
		servers = {'game.en.261', 'game.en.262', 'game.en.263', 'game.en.264', 'game.en.265', 'game.en.266', 'game.en.267', 'game.en.268', 'game.en.269', 'game.en.270'},
		date = 20230919,
		cross = 'crossmine.en.27'
	},
	[49611] = {
		id = 49611,
		service = 'crossmine',
		servers = {'game.en.261', 'game.en.262', 'game.en.263', 'game.en.264', 'game.en.265', 'game.en.266', 'game.en.267', 'game.en.268', 'game.en.269', 'game.en.270'},
		date = 20230926,
		cross = 'crossmine.en.27'
	},
	[49612] = {
		id = 49612,
		service = 'crossmine',
		servers = {'game.en.261', 'game.en.262', 'game.en.263', 'game.en.264', 'game.en.265', 'game.en.266', 'game.en.267', 'game.en.268', 'game.en.269', 'game.en.270'},
		date = 20231003,
		cross = 'crossmine.en.27'
	},
	[49700] = {
		id = 49700,
		service = 'crossmine',
		servers = {'game.en.271', 'game.en.272', 'game.en.273', 'game.en.274', 'game.en.275', 'game.en.276', 'game.en.277', 'game.en.278', 'game.en.279', 'game.en.280'},
		date = 20230704,
		cross = 'crossmine.en.28'
	},
	[49701] = {
		id = 49701,
		service = 'crossmine',
		servers = {'game.en.271', 'game.en.272', 'game.en.273', 'game.en.274', 'game.en.275', 'game.en.276', 'game.en.277', 'game.en.278', 'game.en.279', 'game.en.280'},
		date = 20230711,
		cross = 'crossmine.en.28'
	},
	[49702] = {
		id = 49702,
		service = 'crossmine',
		servers = {'game.en.271', 'game.en.272', 'game.en.273', 'game.en.274', 'game.en.275', 'game.en.276', 'game.en.277', 'game.en.278', 'game.en.279', 'game.en.280'},
		date = 20230718,
		cross = 'crossmine.en.28'
	},
	[49703] = {
		id = 49703,
		service = 'crossmine',
		servers = {'game.en.271', 'game.en.272', 'game.en.273', 'game.en.274', 'game.en.275', 'game.en.276', 'game.en.277', 'game.en.278', 'game.en.279', 'game.en.280'},
		date = 20230725,
		cross = 'crossmine.en.28'
	},
	[49704] = {
		id = 49704,
		service = 'crossmine',
		servers = {'game.en.271', 'game.en.272', 'game.en.273', 'game.en.274', 'game.en.275', 'game.en.276', 'game.en.277', 'game.en.278', 'game.en.279', 'game.en.280'},
		date = 20230801,
		cross = 'crossmine.en.28'
	},
	[49705] = {
		id = 49705,
		service = 'crossmine',
		servers = {'game.en.271', 'game.en.272', 'game.en.273', 'game.en.274', 'game.en.275', 'game.en.276', 'game.en.277', 'game.en.278', 'game.en.279', 'game.en.280'},
		date = 20230808,
		cross = 'crossmine.en.28'
	},
	[49706] = {
		id = 49706,
		service = 'crossmine',
		servers = {'game.en.271', 'game.en.272', 'game.en.273', 'game.en.274', 'game.en.275', 'game.en.276', 'game.en.277', 'game.en.278', 'game.en.279', 'game.en.280'},
		date = 20230815,
		cross = 'crossmine.en.28'
	},
	[49707] = {
		id = 49707,
		service = 'crossmine',
		servers = {'game.en.271', 'game.en.272', 'game.en.273', 'game.en.274', 'game.en.275', 'game.en.276', 'game.en.277', 'game.en.278', 'game.en.279', 'game.en.280'},
		date = 20230822,
		cross = 'crossmine.en.28'
	},
	[49708] = {
		id = 49708,
		service = 'crossmine',
		servers = {'game.en.271', 'game.en.272', 'game.en.273', 'game.en.274', 'game.en.275', 'game.en.276', 'game.en.277', 'game.en.278', 'game.en.279', 'game.en.280'},
		date = 20230829,
		cross = 'crossmine.en.28'
	},
	[49709] = {
		id = 49709,
		service = 'crossmine',
		servers = {'game.en.271', 'game.en.272', 'game.en.273', 'game.en.274', 'game.en.275', 'game.en.276', 'game.en.277', 'game.en.278', 'game.en.279', 'game.en.280'},
		date = 20230905,
		cross = 'crossmine.en.28'
	},
	[49710] = {
		id = 49710,
		service = 'crossmine',
		servers = {'game.en.271', 'game.en.272', 'game.en.273', 'game.en.274', 'game.en.275', 'game.en.276', 'game.en.277', 'game.en.278', 'game.en.279', 'game.en.280'},
		date = 20230919,
		cross = 'crossmine.en.28'
	},
	[49711] = {
		id = 49711,
		service = 'crossmine',
		servers = {'game.en.271', 'game.en.272', 'game.en.273', 'game.en.274', 'game.en.275', 'game.en.276', 'game.en.277', 'game.en.278', 'game.en.279', 'game.en.280'},
		date = 20230926,
		cross = 'crossmine.en.28'
	},
	[49712] = {
		id = 49712,
		service = 'crossmine',
		servers = {'game.en.271', 'game.en.272', 'game.en.273', 'game.en.274', 'game.en.275', 'game.en.276', 'game.en.277', 'game.en.278', 'game.en.279', 'game.en.280'},
		date = 20231003,
		cross = 'crossmine.en.28'
	},
	[49800] = {
		id = 49800,
		service = 'crossmine',
		servers = {'game.en.281', 'game.en.282', 'game.en.283', 'game.en.284', 'game.en.285', 'game.en.286', 'game.en.287', 'game.en.288', 'game.en.289', 'game.en.290'},
		date = 20230704,
		cross = 'crossmine.en.29'
	},
	[49801] = {
		id = 49801,
		service = 'crossmine',
		servers = {'game.en.281', 'game.en.282', 'game.en.283', 'game.en.284', 'game.en.285', 'game.en.286', 'game.en.287', 'game.en.288', 'game.en.289', 'game.en.290'},
		date = 20230711,
		cross = 'crossmine.en.29'
	},
	[49802] = {
		id = 49802,
		service = 'crossmine',
		servers = {'game.en.281', 'game.en.282', 'game.en.283', 'game.en.284', 'game.en.285', 'game.en.286', 'game.en.287', 'game.en.288', 'game.en.289', 'game.en.290'},
		date = 20230718,
		cross = 'crossmine.en.29'
	},
	[49803] = {
		id = 49803,
		service = 'crossmine',
		servers = {'game.en.281', 'game.en.282', 'game.en.283', 'game.en.284', 'game.en.285', 'game.en.286', 'game.en.287', 'game.en.288', 'game.en.289', 'game.en.290'},
		date = 20230725,
		cross = 'crossmine.en.29'
	},
	[49804] = {
		id = 49804,
		service = 'crossmine',
		servers = {'game.en.281', 'game.en.282', 'game.en.283', 'game.en.284', 'game.en.285', 'game.en.286', 'game.en.287', 'game.en.288', 'game.en.289', 'game.en.290'},
		date = 20230801,
		cross = 'crossmine.en.29'
	},
	[49805] = {
		id = 49805,
		service = 'crossmine',
		servers = {'game.en.281', 'game.en.282', 'game.en.283', 'game.en.284', 'game.en.285', 'game.en.286', 'game.en.287', 'game.en.288', 'game.en.289', 'game.en.290'},
		date = 20230808,
		cross = 'crossmine.en.29'
	},
	[49806] = {
		id = 49806,
		service = 'crossmine',
		servers = {'game.en.281', 'game.en.282', 'game.en.283', 'game.en.284', 'game.en.285', 'game.en.286', 'game.en.287', 'game.en.288', 'game.en.289', 'game.en.290'},
		date = 20230815,
		cross = 'crossmine.en.29'
	},
	[49807] = {
		id = 49807,
		service = 'crossmine',
		servers = {'game.en.281', 'game.en.282', 'game.en.283', 'game.en.284', 'game.en.285', 'game.en.286', 'game.en.287', 'game.en.288', 'game.en.289', 'game.en.290'},
		date = 20230822,
		cross = 'crossmine.en.29'
	},
	[49808] = {
		id = 49808,
		service = 'crossmine',
		servers = {'game.en.281', 'game.en.282', 'game.en.283', 'game.en.284', 'game.en.285', 'game.en.286', 'game.en.287', 'game.en.288', 'game.en.289', 'game.en.290'},
		date = 20230829,
		cross = 'crossmine.en.29'
	},
	[49809] = {
		id = 49809,
		service = 'crossmine',
		servers = {'game.en.281', 'game.en.282', 'game.en.283', 'game.en.284', 'game.en.285', 'game.en.286', 'game.en.287', 'game.en.288', 'game.en.289', 'game.en.290'},
		date = 20230905,
		cross = 'crossmine.en.29'
	},
	[49810] = {
		id = 49810,
		service = 'crossmine',
		servers = {'game.en.281', 'game.en.282', 'game.en.283', 'game.en.284', 'game.en.285', 'game.en.286', 'game.en.287', 'game.en.288', 'game.en.289', 'game.en.290'},
		date = 20230919,
		cross = 'crossmine.en.29'
	},
	[49811] = {
		id = 49811,
		service = 'crossmine',
		servers = {'game.en.281', 'game.en.282', 'game.en.283', 'game.en.284', 'game.en.285', 'game.en.286', 'game.en.287', 'game.en.288', 'game.en.289', 'game.en.290'},
		date = 20230926,
		cross = 'crossmine.en.29'
	},
	[49812] = {
		id = 49812,
		service = 'crossmine',
		servers = {'game.en.281', 'game.en.282', 'game.en.283', 'game.en.284', 'game.en.285', 'game.en.286', 'game.en.287', 'game.en.288', 'game.en.289', 'game.en.290'},
		date = 20231003,
		cross = 'crossmine.en.29'
	},
	[49900] = {
		id = 49900,
		service = 'crossmine',
		servers = {'game.en.291', 'game.en.292', 'game.en.293', 'game.en.294', 'game.en.295', 'game.en.296', 'game.en.297', 'game.en.298'},
		date = 20230704,
		cross = 'crossmine.en.30'
	},
	[49901] = {
		id = 49901,
		service = 'crossmine',
		servers = {'game.en.291', 'game.en.292', 'game.en.293', 'game.en.294', 'game.en.295', 'game.en.296', 'game.en.297', 'game.en.298'},
		date = 20230711,
		cross = 'crossmine.en.30'
	},
	[49902] = {
		id = 49902,
		service = 'crossmine',
		servers = {'game.en.291', 'game.en.292', 'game.en.293', 'game.en.294', 'game.en.295', 'game.en.296', 'game.en.297', 'game.en.298'},
		date = 20230718,
		cross = 'crossmine.en.30'
	},
	[49903] = {
		id = 49903,
		service = 'crossmine',
		servers = {'game.en.291', 'game.en.292', 'game.en.293', 'game.en.294', 'game.en.295', 'game.en.296', 'game.en.297', 'game.en.298'},
		date = 20230725,
		cross = 'crossmine.en.30'
	},
	[49904] = {
		id = 49904,
		service = 'crossmine',
		servers = {'game.en.291', 'game.en.292', 'game.en.293', 'game.en.294', 'game.en.295', 'game.en.296', 'game.en.297', 'game.en.298'},
		date = 20230801,
		cross = 'crossmine.en.30'
	},
	[49905] = {
		id = 49905,
		service = 'crossmine',
		servers = {'game.en.291', 'game.en.292', 'game.en.293', 'game.en.294', 'game.en.295', 'game.en.296', 'game.en.297', 'game.en.298'},
		date = 20230808,
		cross = 'crossmine.en.30'
	},
	[49906] = {
		id = 49906,
		service = 'crossmine',
		servers = {'game.en.291', 'game.en.292', 'game.en.293', 'game.en.294', 'game.en.295', 'game.en.296', 'game.en.297', 'game.en.298'},
		date = 20230815,
		cross = 'crossmine.en.30'
	},
	[49907] = {
		id = 49907,
		service = 'crossmine',
		servers = {'game.en.291', 'game.en.292', 'game.en.293', 'game.en.294', 'game.en.295', 'game.en.296', 'game.en.297', 'game.en.298'},
		date = 20230822,
		cross = 'crossmine.en.30'
	},
	[49908] = {
		id = 49908,
		service = 'crossmine',
		servers = {'game.en.291', 'game.en.292', 'game.en.293', 'game.en.294', 'game.en.295', 'game.en.296', 'game.en.297', 'game.en.298'},
		date = 20230829,
		cross = 'crossmine.en.30'
	},
	[49909] = {
		id = 49909,
		service = 'crossmine',
		servers = {'game.en.291', 'game.en.292', 'game.en.293', 'game.en.294', 'game.en.295', 'game.en.296', 'game.en.297', 'game.en.298'},
		date = 20230905,
		cross = 'crossmine.en.30'
	},
	[49910] = {
		id = 49910,
		service = 'crossmine',
		servers = {'game.en.291', 'game.en.292', 'game.en.293', 'game.en.294', 'game.en.295', 'game.en.296', 'game.en.297', 'game.en.298'},
		date = 20230919,
		cross = 'crossmine.en.30'
	},
	[49911] = {
		id = 49911,
		service = 'crossmine',
		servers = {'game.en.291', 'game.en.292', 'game.en.293', 'game.en.294', 'game.en.295', 'game.en.296', 'game.en.297', 'game.en.298'},
		date = 20230926,
		cross = 'crossmine.en.30'
	},
	[49912] = {
		id = 49912,
		service = 'crossmine',
		servers = {'game.en.291', 'game.en.292', 'game.en.293', 'game.en.294', 'game.en.295', 'game.en.296', 'game.en.297', 'game.en.298'},
		date = 20231003,
		cross = 'crossmine.en.30'
	},
	[68681] = {
		id = 68681,
		service = 'bravechallengeranking',
		date = 20230821,
		cross = 'en',
		endDate = 20230830
	},
	[68682] = {
		id = 68682,
		service = 'normalbravechallengeranking',
		date = 20230821,
		cross = 'en',
		endDate = 20230830
	},
	[164999] = {
		id = 164999,
		service = 'bravechallengeranking',
		date = 20230815,
		cross = 'vn',
		endDate = 20230830
	},
	[166999] = {
		id = 166999,
		service = 'horseraceranking',
		date = 20230815,
		cross = 'vn',
		endDate = 20230830
	},
	[202999] = {
		id = 202999,
		service = 'normalbravechallengeranking',
		date = 20230815,
		cross = 'vn',
		endDate = 20230830
	},
	[222221] = {
		id = 222221,
		service = 'crosscraft',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8', 'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16', 'game.vn.17', 'game.vn.18'},
		date = 20230703,
		cross = 'crosscraft.vn.1'
	},
	[222222] = {
		id = 222222,
		service = 'crosscraft',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8', 'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16', 'game.vn.17', 'game.vn.18'},
		date = 20230707,
		cross = 'crosscraft.vn.1'
	},
	[222223] = {
		id = 222223,
		service = 'crosscraft',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8', 'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16', 'game.vn.17', 'game.vn.18'},
		date = 20230711,
		cross = 'crosscraft.vn.1'
	},
	[222224] = {
		id = 222224,
		service = 'crosscraft',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8', 'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16', 'game.vn.17', 'game.vn.18'},
		date = 20230714,
		cross = 'crosscraft.vn.1'
	},
	[222225] = {
		id = 222225,
		service = 'crosscraft',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8', 'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16', 'game.vn.17', 'game.vn.18'},
		date = 20230717,
		cross = 'crosscraft.vn.1'
	},
	[222226] = {
		id = 222226,
		service = 'crosscraft',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8', 'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16', 'game.vn.17', 'game.vn.18'},
		date = 20230721,
		cross = 'crosscraft.vn.1'
	},
	[222227] = {
		id = 222227,
		service = 'crosscraft',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8', 'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16', 'game.vn.17', 'game.vn.18'},
		date = 20230724,
		cross = 'crosscraft.vn.1'
	},
	[222228] = {
		id = 222228,
		service = 'crosscraft',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8', 'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16', 'game.vn.17', 'game.vn.18'},
		date = 20230728,
		cross = 'crosscraft.vn.1'
	},
	[222229] = {
		id = 222229,
		service = 'crosscraft',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8', 'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16', 'game.vn.17', 'game.vn.18'},
		date = 20230731,
		cross = 'crosscraft.vn.1'
	},
	[222230] = {
		id = 222230,
		service = 'crosscraft',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8', 'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16', 'game.vn.17', 'game.vn.18'},
		date = 20230804,
		cross = 'crosscraft.vn.1'
	},
	[222231] = {
		id = 222231,
		service = 'crosscraft',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8', 'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16', 'game.vn.17', 'game.vn.18'},
		date = 20230807,
		cross = 'crosscraft.vn.1'
	},
	[222232] = {
		id = 222232,
		service = 'crosscraft',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8', 'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16', 'game.vn.17', 'game.vn.18'},
		date = 20230811,
		cross = 'crosscraft.vn.1'
	},
	[222233] = {
		id = 222233,
		service = 'crosscraft',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8', 'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16', 'game.vn.17', 'game.vn.18'},
		date = 20230814,
		cross = 'crosscraft.vn.1'
	},
	[222234] = {
		id = 222234,
		service = 'crosscraft',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8', 'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16', 'game.vn.17', 'game.vn.18'},
		date = 20230818,
		cross = 'crosscraft.vn.1'
	},
	[222235] = {
		id = 222235,
		service = 'crosscraft',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8', 'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16', 'game.vn.17', 'game.vn.18'},
		date = 20230821,
		cross = 'crosscraft.vn.1'
	},
	[222236] = {
		id = 222236,
		service = 'crosscraft',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8', 'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16', 'game.vn.17', 'game.vn.18'},
		date = 20230825,
		cross = 'crosscraft.vn.1'
	},
	[222237] = {
		id = 222237,
		service = 'crosscraft',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8', 'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16', 'game.vn.17', 'game.vn.18'},
		date = 20230828,
		cross = 'crosscraft.vn.1'
	},
	[222238] = {
		id = 222238,
		service = 'crosscraft',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8', 'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16', 'game.vn.17', 'game.vn.18'},
		date = 20230901,
		cross = 'crosscraft.vn.1'
	},
	[222239] = {
		id = 222239,
		service = 'crosscraft',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8', 'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16', 'game.vn.17', 'game.vn.18'},
		date = 20230904,
		cross = 'crosscraft.vn.1'
	},
	[222240] = {
		id = 222240,
		service = 'crosscraft',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8', 'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16', 'game.vn.17', 'game.vn.18'},
		date = 20230908,
		cross = 'crosscraft.vn.1'
	},
	[222241] = {
		id = 222241,
		service = 'crosscraft',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8', 'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16', 'game.vn.17', 'game.vn.18'},
		date = 20230911,
		cross = 'crosscraft.vn.1'
	},
	[222242] = {
		id = 222242,
		service = 'crosscraft',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8', 'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16', 'game.vn.17', 'game.vn.18'},
		date = 20230918,
		cross = 'crosscraft.vn.1'
	},
	[222243] = {
		id = 222243,
		service = 'crosscraft',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8', 'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16', 'game.vn.17', 'game.vn.18'},
		date = 20230922,
		cross = 'crosscraft.vn.1'
	},
	[222244] = {
		id = 222244,
		service = 'crosscraft',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8', 'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16', 'game.vn.17', 'game.vn.18'},
		date = 20230925,
		cross = 'crosscraft.vn.1'
	},
	[222245] = {
		id = 222245,
		service = 'crosscraft',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8', 'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16', 'game.vn.17', 'game.vn.18'},
		date = 20230929,
		cross = 'crosscraft.vn.1'
	},
	[222246] = {
		id = 222246,
		service = 'crosscraft',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8', 'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16', 'game.vn.17', 'game.vn.18'},
		date = 20231002,
		cross = 'crosscraft.vn.1'
	},
	[767771] = {
		id = 767771,
		service = 'crossunionfight',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8', 'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16', 'game.vn.17', 'game.vn.18'},
		date = 20230624,
		cross = 'crossunionfight.vn.1'
	},
	[767772] = {
		id = 767772,
		service = 'crossunionfight',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8', 'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16', 'game.vn.17', 'game.vn.18'},
		date = 20230724,
		cross = 'crossunionfight.vn.1'
	},
	[767773] = {
		id = 767773,
		service = 'crossunionfight',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8', 'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16', 'game.vn.17', 'game.vn.18'},
		date = 20230824,
		cross = 'crossunionfight.vn.1'
	},
	[767774] = {
		id = 767774,
		service = 'crossunionfight',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8', 'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16', 'game.vn.17', 'game.vn.18'},
		date = 20230924,
		cross = 'crossunionfight.vn.1'
	},
	[767775] = {
		id = 767775,
		service = 'crossunionfight',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8', 'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16', 'game.vn.17', 'game.vn.18'},
		date = 20231024,
		cross = 'crossunionfight.vn.1'
	},
	[818881] = {
		id = 818881,
		service = 'crossarena',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8', 'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16', 'game.vn.17', 'game.vn.18', 'game.vn.19', 'game.vn.20', 'game.vn.21', 'game.vn.22'},
		date = 20230224,
		cross = 'crossarena.vn.1'
	},
	[818882] = {
		id = 818882,
		service = 'crossarena',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8', 'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16', 'game.vn.17', 'game.vn.18', 'game.vn.19', 'game.vn.20', 'game.vn.21', 'game.vn.22'},
		date = 20230324,
		cross = 'crossarena.vn.1'
	},
	[818883] = {
		id = 818883,
		service = 'crossarena',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8', 'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16', 'game.vn.17', 'game.vn.18', 'game.vn.19', 'game.vn.20', 'game.vn.21', 'game.vn.22'},
		date = 20230424,
		cross = 'crossarena.vn.1'
	},
	[818884] = {
		id = 818884,
		service = 'crossarena',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8', 'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16', 'game.vn.17', 'game.vn.18', 'game.vn.19', 'game.vn.20', 'game.vn.21', 'game.vn.22'},
		date = 20230624,
		cross = 'crossarena.vn.1'
	},
	[818885] = {
		id = 818885,
		service = 'crossarena',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8', 'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16', 'game.vn.17', 'game.vn.18', 'game.vn.19', 'game.vn.20', 'game.vn.21', 'game.vn.22'},
		date = 20230724,
		cross = 'crossarena.vn.1'
	},
	[818886] = {
		id = 818886,
		service = 'crossarena',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8', 'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16', 'game.vn.17', 'game.vn.18', 'game.vn.19', 'game.vn.20', 'game.vn.21', 'game.vn.22'},
		date = 20230824,
		cross = 'crossarena.vn.1'
	},
	[818888] = {
		id = 818888,
		service = 'crossarena',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8', 'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16', 'game.vn.17', 'game.vn.18', 'game.vn.19', 'game.vn.20', 'game.vn.21', 'game.vn.22'},
		date = 20230924,
		cross = 'crossarena.vn.1'
	},
	[919991] = {
		id = 919991,
		service = 'crossunionqa',
		date = 20230708,
		cross = 'vn',
		endDate = 20230709
	},
	[919992] = {
		id = 919992,
		service = 'crossunionqa',
		date = 20230715,
		cross = 'vn',
		endDate = 20230716
	},
	[919993] = {
		id = 919993,
		service = 'crossunionqa',
		date = 20230722,
		cross = 'vn',
		endDate = 20230723
	},
	[919994] = {
		id = 919994,
		service = 'crossunionqa',
		date = 20230729,
		cross = 'vn',
		endDate = 20230730
	},
	[919995] = {
		id = 919995,
		service = 'crossunionqa',
		date = 20230805,
		cross = 'vn',
		endDate = 20230806
	},
	[919996] = {
		id = 919996,
		service = 'crossunionqa',
		date = 20230812,
		cross = 'vn',
		endDate = 20230813
	},
	[919997] = {
		id = 919997,
		service = 'crossunionqa',
		date = 20230819,
		cross = 'vn',
		endDate = 20230820
	},
	[919998] = {
		id = 919998,
		service = 'crossunionqa',
		date = 20230826,
		cross = 'vn',
		endDate = 20230827
	},
	[919999] = {
		id = 919999,
		service = 'crossunionqa',
		date = 20230902,
		cross = 'vn',
		endDate = 20230903
	},
	[920000] = {
		id = 920000,
		service = 'crossunionqa',
		date = 20230909,
		cross = 'vn',
		endDate = 20230910
	},
	[920001] = {
		id = 920001,
		service = 'crossunionqa',
		date = 20230916,
		cross = 'vn',
		endDate = 20230917
	},
	[920002] = {
		id = 920002,
		service = 'crossunionqa',
		date = 20230923,
		cross = 'vn',
		endDate = 20230924
	},
	[920003] = {
		id = 920003,
		service = 'crossunionqa',
		date = 20230930,
		cross = 'vn',
		endDate = 20231001
	},
	[920004] = {
		id = 920004,
		service = 'crossunionqa',
		date = 20231007,
		cross = 'vn',
		endDate = 20231008
	},
	[920005] = {
		id = 920005,
		service = 'crossunionqa',
		date = 20231014,
		cross = 'vn',
		endDate = 20231015
	},
	[3333331] = {
		id = 3333331,
		service = 'crossmine',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8'},
		date = 20230704,
		cross = 'crossmine.vn.1'
	},
	[3333332] = {
		id = 3333332,
		service = 'crossmine',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8'},
		date = 20230711,
		cross = 'crossmine.vn.1'
	},
	[3333333] = {
		id = 3333333,
		service = 'crossmine',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8'},
		date = 20230718,
		cross = 'crossmine.vn.1'
	},
	[3333334] = {
		id = 3333334,
		service = 'crossmine',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8'},
		date = 20230725,
		cross = 'crossmine.vn.1'
	},
	[3333335] = {
		id = 3333335,
		service = 'crossmine',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8'},
		date = 20230801,
		cross = 'crossmine.vn.1'
	},
	[3333336] = {
		id = 3333336,
		service = 'crossmine',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8'},
		date = 20230808,
		cross = 'crossmine.vn.1'
	},
	[3333337] = {
		id = 3333337,
		service = 'crossmine',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8'},
		date = 20230815,
		cross = 'crossmine.vn.1'
	},
	[3333338] = {
		id = 3333338,
		service = 'crossmine',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8'},
		date = 20230822,
		cross = 'crossmine.vn.1'
	},
	[3333339] = {
		id = 3333339,
		service = 'crossmine',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8'},
		date = 20230829,
		cross = 'crossmine.vn.1'
	},
	[3333340] = {
		id = 3333340,
		service = 'crossmine',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8'},
		date = 20230905,
		cross = 'crossmine.vn.1'
	},
	[3333341] = {
		id = 3333341,
		service = 'crossmine',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8'},
		date = 20230919,
		cross = 'crossmine.vn.1'
	},
	[3333342] = {
		id = 3333342,
		service = 'crossmine',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8'},
		date = 20230926,
		cross = 'crossmine.vn.1'
	},
	[3333343] = {
		id = 3333343,
		service = 'crossmine',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8'},
		date = 20231003,
		cross = 'crossmine.vn.1'
	},
	[3433331] = {
		id = 3433331,
		service = 'crossmine',
		servers = {'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16'},
		date = 20230704,
		cross = 'crossmine.vn.2'
	},
	[3433332] = {
		id = 3433332,
		service = 'crossmine',
		servers = {'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16'},
		date = 20230711,
		cross = 'crossmine.vn.2'
	},
	[3433333] = {
		id = 3433333,
		service = 'crossmine',
		servers = {'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16'},
		date = 20230718,
		cross = 'crossmine.vn.2'
	},
	[3433334] = {
		id = 3433334,
		service = 'crossmine',
		servers = {'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16'},
		date = 20230725,
		cross = 'crossmine.vn.2'
	},
	[3433335] = {
		id = 3433335,
		service = 'crossmine',
		servers = {'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16'},
		date = 20230801,
		cross = 'crossmine.vn.2'
	},
	[3433336] = {
		id = 3433336,
		service = 'crossmine',
		servers = {'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16'},
		date = 20230808,
		cross = 'crossmine.vn.2'
	},
	[3433337] = {
		id = 3433337,
		service = 'crossmine',
		servers = {'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16'},
		date = 20230815,
		cross = 'crossmine.vn.2'
	},
	[3433338] = {
		id = 3433338,
		service = 'crossmine',
		servers = {'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16'},
		date = 20230822,
		cross = 'crossmine.vn.2'
	},
	[3433339] = {
		id = 3433339,
		service = 'crossmine',
		servers = {'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16'},
		date = 20230829,
		cross = 'crossmine.vn.2'
	},
	[3433340] = {
		id = 3433340,
		service = 'crossmine',
		servers = {'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16'},
		date = 20230905,
		cross = 'crossmine.vn.2'
	},
	[3433341] = {
		id = 3433341,
		service = 'crossmine',
		servers = {'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16'},
		date = 20230919,
		cross = 'crossmine.vn.2'
	},
	[3433342] = {
		id = 3433342,
		service = 'crossmine',
		servers = {'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16'},
		date = 20230926,
		cross = 'crossmine.vn.2'
	},
	[3433343] = {
		id = 3433343,
		service = 'crossmine',
		servers = {'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16'},
		date = 20231003,
		cross = 'crossmine.vn.2'
	},
	[3533331] = {
		id = 3533331,
		service = 'crossmine',
		servers = {'game.vn.17', 'game.vn.18', 'game.vn.19', 'game.vn.20', 'game.vn.21', 'game.vn.22'},
		date = 20230704,
		cross = 'crossmine.vn.3'
	},
	[3533332] = {
		id = 3533332,
		service = 'crossmine',
		servers = {'game.vn.17', 'game.vn.18', 'game.vn.19', 'game.vn.20', 'game.vn.21', 'game.vn.22'},
		date = 20230711,
		cross = 'crossmine.vn.3'
	},
	[3533333] = {
		id = 3533333,
		service = 'crossmine',
		servers = {'game.vn.17', 'game.vn.18', 'game.vn.19', 'game.vn.20', 'game.vn.21', 'game.vn.22'},
		date = 20230718,
		cross = 'crossmine.vn.3'
	},
	[3533334] = {
		id = 3533334,
		service = 'crossmine',
		servers = {'game.vn.17', 'game.vn.18', 'game.vn.19', 'game.vn.20', 'game.vn.21', 'game.vn.22'},
		date = 20230725,
		cross = 'crossmine.vn.3'
	},
	[3533335] = {
		id = 3533335,
		service = 'crossmine',
		servers = {'game.vn.17', 'game.vn.18', 'game.vn.19', 'game.vn.20', 'game.vn.21', 'game.vn.22'},
		date = 20230801,
		cross = 'crossmine.vn.3'
	},
	[3533336] = {
		id = 3533336,
		service = 'crossmine',
		servers = {'game.vn.17', 'game.vn.18', 'game.vn.19', 'game.vn.20', 'game.vn.21', 'game.vn.22'},
		date = 20230808,
		cross = 'crossmine.vn.3'
	},
	[3533337] = {
		id = 3533337,
		service = 'crossmine',
		servers = {'game.vn.17', 'game.vn.18', 'game.vn.19', 'game.vn.20', 'game.vn.21', 'game.vn.22'},
		date = 20230815,
		cross = 'crossmine.vn.3'
	},
	[3533338] = {
		id = 3533338,
		service = 'crossmine',
		servers = {'game.vn.17', 'game.vn.18', 'game.vn.19', 'game.vn.20', 'game.vn.21', 'game.vn.22'},
		date = 20230822,
		cross = 'crossmine.vn.3'
	},
	[3533339] = {
		id = 3533339,
		service = 'crossmine',
		servers = {'game.vn.17', 'game.vn.18', 'game.vn.19', 'game.vn.20', 'game.vn.21', 'game.vn.22'},
		date = 20230829,
		cross = 'crossmine.vn.3'
	},
	[3533340] = {
		id = 3533340,
		service = 'crossmine',
		servers = {'game.vn.17', 'game.vn.18', 'game.vn.19', 'game.vn.20', 'game.vn.21', 'game.vn.22'},
		date = 20230905,
		cross = 'crossmine.vn.3'
	},
	[3533341] = {
		id = 3533341,
		service = 'crossmine',
		servers = {'game.vn.17', 'game.vn.18', 'game.vn.19', 'game.vn.20', 'game.vn.21', 'game.vn.22'},
		date = 20230919,
		cross = 'crossmine.vn.3'
	},
	[3533342] = {
		id = 3533342,
		service = 'crossmine',
		servers = {'game.vn.17', 'game.vn.18', 'game.vn.19', 'game.vn.20', 'game.vn.21', 'game.vn.22'},
		date = 20230926,
		cross = 'crossmine.vn.3'
	},
	[3533343] = {
		id = 3533343,
		service = 'crossmine',
		servers = {'game.vn.17', 'game.vn.18', 'game.vn.19', 'game.vn.20', 'game.vn.21', 'game.vn.22'},
		date = 20231003,
		cross = 'crossmine.vn.3'
	},
	[3733331] = {
		id = 3733331,
		service = 'onlinefight',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8', 'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16', 'game.vn.17', 'game.vn.18', 'game.vn.19', 'game.vn.20', 'game.vn.21', 'game.vn.22'},
		date = 20231124,
		cross = 'onlinefight.vn.1',
		endDate = 20231220
	},
	[3733332] = {
		id = 3733332,
		service = 'onlinefight',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8', 'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16', 'game.vn.17', 'game.vn.18', 'game.vn.19', 'game.vn.20', 'game.vn.21', 'game.vn.22'},
		date = 20230724,
		cross = 'onlinefight.vn.1',
		endDate = 20230820
	},
	[3733333] = {
		id = 3733333,
		service = 'onlinefight',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8', 'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16', 'game.vn.17', 'game.vn.18', 'game.vn.19', 'game.vn.20', 'game.vn.21', 'game.vn.22'},
		date = 20230824,
		cross = 'onlinefight.vn.1',
		endDate = 20230920
	},
	[3733334] = {
		id = 3733334,
		service = 'onlinefight',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8', 'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16', 'game.vn.17', 'game.vn.18', 'game.vn.19', 'game.vn.20', 'game.vn.21', 'game.vn.22'},
		date = 20230924,
		cross = 'onlinefight.vn.1',
		endDate = 20231020
	},
	[3733335] = {
		id = 3733335,
		service = 'onlinefight',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8', 'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16', 'game.vn.17', 'game.vn.18', 'game.vn.19', 'game.vn.20', 'game.vn.21', 'game.vn.22'},
		date = 20231024,
		cross = 'onlinefight.vn.1',
		endDate = 20231120
	},
	[3733336] = {
		id = 3733336,
		service = 'onlinefight',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8', 'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16', 'game.vn.17', 'game.vn.18', 'game.vn.19', 'game.vn.20', 'game.vn.21', 'game.vn.22'},
		date = 20231124,
		cross = 'onlinefight.vn.1',
		endDate = 20231220
	},
	[6666101] = {
		id = 6666101,
		service = 'crossgym',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8', 'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16', 'game.vn.17', 'game.vn.18', 'game.vn.19', 'game.vn.20', 'game.vn.21', 'game.vn.22'},
		date = 20230710,
		cross = 'crossgym.vn.1',
		endDate = 20230716
	},
	[6666102] = {
		id = 6666102,
		service = 'crossgym',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8', 'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16', 'game.vn.17', 'game.vn.18', 'game.vn.19', 'game.vn.20', 'game.vn.21', 'game.vn.22'},
		date = 20230717,
		cross = 'crossgym.vn.1',
		endDate = 20230723
	},
	[6666103] = {
		id = 6666103,
		service = 'crossgym',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8', 'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16', 'game.vn.17', 'game.vn.18', 'game.vn.19', 'game.vn.20', 'game.vn.21', 'game.vn.22'},
		date = 20230724,
		cross = 'crossgym.vn.1',
		endDate = 20230730
	},
	[6666104] = {
		id = 6666104,
		service = 'crossgym',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8', 'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16', 'game.vn.17', 'game.vn.18', 'game.vn.19', 'game.vn.20', 'game.vn.21', 'game.vn.22'},
		date = 20230731,
		cross = 'crossgym.vn.1',
		endDate = 20230806
	},
	[6666105] = {
		id = 6666105,
		service = 'crossgym',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8', 'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16', 'game.vn.17', 'game.vn.18', 'game.vn.19', 'game.vn.20', 'game.vn.21', 'game.vn.22'},
		date = 20230807,
		cross = 'crossgym.vn.1',
		endDate = 20230813
	},
	[6666106] = {
		id = 6666106,
		service = 'crossgym',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8', 'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16', 'game.vn.17', 'game.vn.18', 'game.vn.19', 'game.vn.20', 'game.vn.21', 'game.vn.22'},
		date = 20230814,
		cross = 'crossgym.vn.1',
		endDate = 20230820
	},
	[6666107] = {
		id = 6666107,
		service = 'crossgym',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8', 'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16', 'game.vn.17', 'game.vn.18', 'game.vn.19', 'game.vn.20', 'game.vn.21', 'game.vn.22'},
		date = 20230821,
		cross = 'crossgym.vn.1',
		endDate = 20230827
	},
	[6666108] = {
		id = 6666108,
		service = 'crossgym',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8', 'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16', 'game.vn.17', 'game.vn.18', 'game.vn.19', 'game.vn.20', 'game.vn.21', 'game.vn.22'},
		date = 20230828,
		cross = 'crossgym.vn.1',
		endDate = 20230903
	},
	[6666109] = {
		id = 6666109,
		service = 'crossgym',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8', 'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16', 'game.vn.17', 'game.vn.18', 'game.vn.19', 'game.vn.20', 'game.vn.21', 'game.vn.22'},
		date = 20230904,
		cross = 'crossgym.vn.1',
		endDate = 20230910
	},
	[6666110] = {
		id = 6666110,
		service = 'crossgym',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8', 'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16', 'game.vn.17', 'game.vn.18', 'game.vn.19', 'game.vn.20', 'game.vn.21', 'game.vn.22'},
		date = 20230911,
		cross = 'crossgym.vn.1',
		endDate = 20230917
	},
	[6666111] = {
		id = 6666111,
		service = 'crossgym',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8', 'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16', 'game.vn.17', 'game.vn.18', 'game.vn.19', 'game.vn.20', 'game.vn.21', 'game.vn.22'},
		date = 20230918,
		cross = 'crossgym.vn.1',
		endDate = 20230924
	},
	[6666112] = {
		id = 6666112,
		service = 'crossgym',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8', 'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16', 'game.vn.17', 'game.vn.18', 'game.vn.19', 'game.vn.20', 'game.vn.21', 'game.vn.22'},
		date = 20230925,
		cross = 'crossgym.vn.1',
		endDate = 20231001
	},
	[6666114] = {
		id = 6666114,
		service = 'crossgym',
		servers = {'game.vn.1', 'game.vn.2', 'game.vn.3', 'game.vn.4', 'game.vn.5', 'game.vn.6', 'game.vn.7', 'game.vn.8', 'game.vn.9', 'game.vn.10', 'game.vn.11', 'game.vn.12', 'game.vn.13', 'game.vn.14', 'game.vn.15', 'game.vn.16', 'game.vn.17', 'game.vn.18', 'game.vn.19', 'game.vn.20', 'game.vn.21', 'game.vn.22'},
		date = 20231002,
		cross = 'crossgym.vn.1',
		endDate = 20231008
	},

	__size = 583,
	__default = {
		__index = {
			service = '',
			servers = {},
			date = 20170120,
			cross = '',
			version = 0,
			endDate = 0
		}
	}
}
return csv.cross.service