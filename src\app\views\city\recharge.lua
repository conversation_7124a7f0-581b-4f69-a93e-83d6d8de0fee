local STATE_TYPE = {
	null = 0,
	buy = 1,
	max = 2,	--已达上限
}

local STATE_TYPE_DIRECT = {
	canbuy = 1,
	refresh = 2,
	sellout = 3,
}

local ActivityView = require "app.views.city.activity.view"
local RechargeView = class("RechargeView", cc.load("mvc").ViewBase)

local function createPrivilegeLabel(desc, fontSize, width)
	local richText = rich.createWithWidth("#C0xede4e0#" .. desc, fontSize, nil, width)
		:align(cc.p(0, 0), 0, 0)
	local height = richText:size().height
	local item = ccui.Layout:create()
		:size(width, height)
		:add(richText)
	return item
end

local FoxData = {
	[1] = {id=1,PackId = "3dto_pack50",Name="50 " .. gLanguageCsv.cointext,Gia = "49,000 VNĐ",fox = "50",Icon="foxcoin/1.png"},
	[2] = {id=2,PackId = "3dto_pack100",Name="100 " .. gLanguageCsv.cointext,Gia = "99,000 VNĐ",fox = "100",Icon="foxcoin/2.png"},
	[3] = {id=3,PackId = "3dto_pack300",Name="300 " .. gLanguageCsv.cointext,Gia = "299,000 VNĐ",fox = "300",Icon="foxcoin/3.png"},
	[4] = {id=4,PackId = "3dto_pack500",Name="500 " .. gLanguageCsv.cointext,Gia = "499,000 VNĐ",fox = "500",Icon="foxcoin/4.png"},
	[5] = {id=5,PackId = "3dto_pack1000",Name="1000 " .. gLanguageCsv.cointext,Gia = "999,000 VNĐ",fox = "1000",Icon="foxcoin/5.png"},
	[6] = {id=6,PackId = "3dto_pack2000",Name="2000 " .. gLanguageCsv.cointext,Gia = "1,999,000 VNĐ",fox = "2000",Icon="foxcoin/6.png"},
}

local function isVipDistinguishedOpen(vipSum)
	-- 语言为其他语言时，若有礼包，则显示按钮
	local roleVip = gGameModel.monthly_record:read("vip")
	return matchLanguage({"cn"}) and vipSum >= gCommonConfigCsv.rechargeVip and dataEasy.isUnlock(gUnlockCsv.vipDistinguished)
			or (not matchLanguage({"cn"}) and (csvSize(gVipCsv[roleVip].monthGift) >= 1))
end

RechargeView.RESOURCE_FILENAME = "recharge.json"
RechargeView.RESOURCE_BINDING = {
	
	["topPanel.btn"] = {
		varname = "topPanelBtn",
		binds = {
			event = "touch",
			methods = {ended = bindHelper.self("onChangeClick")}
		},
	},
	["buyConfirmPanel.closePanel"] = {
		varname = "closePanel",
		binds = {
			event = "touch",
			methods = {ended = bindHelper.self("onClosePanelClick")}
		},
	},
	["topPanel.btnVip"] = { 	-- 尊贵Vip弹窗
		varname = "btnVip",
		binds = {
			{
				event = "touch",
				methods = {ended = bindHelper.self("onVipClick")}
			},
			{
				event = "visible",
				idler = bindHelper.self("vipSum"),
				method = function(vipSum)
					return isVipDistinguishedOpen(vipSum)
				end,
			},
			{
				event = "extend",
				class = "red_hint",
				props = {
					specialTag = "onHonourableVip",
					onNode = function(node)
						node:xy(170, 170)
					end,
				}
			},
		},
	},
	["topPanel.btn.label"] = {
		binds = {
			{
				event = "effect",
				data = {glow = {color = ui.COLORS.GLOW.WHITE}},
			},{
				event = "text",
				idler = bindHelper.self("selectRecharge"),
				method = function(val)
					if val then
						return gLanguageCsv.spacePrivilege
					end
					return gLanguageCsv.spaceRecharge
				end,
			},
		}
	},
	["topPanel.barBg"] = "barBg",
	["topPanel.bar"] = {
		varname = "topBar",
		binds = {
			event = "extend",
 			class = "loadingbar",
			props = {
				data = bindHelper.self("expProgress"),
				maskImg = "3q/mask_bar_red.png",
			},
		},
	},
	["topPanel.barNum"] = {
		varname = "barNum",
		binds = {
			event = "text",
			idler = bindHelper.self("expProgressNum")
		}
	},
	["topPanel.normalPanel"] = "topNormalPanel",
	["topPanel.image"] = "topImage",
	["topPanel.maxPanel"] = "topMaxPanel",
	-- ["topPanel.maxPanel.label"] = {
	-- 	binds = {
	-- 		{
	-- 			event = "effect",
	-- 			data = {glow = {color = ui.COLORS.GLOW.WHITE}},
	-- 		},
	-- 	}
	-- },
	["topPanel.FoxInfo"] = "FoxInfo",
	["topPanel"] = "topPanel",
	["foxcoinPanel"] = "foxcoinPanel",
	["foxcoinPanel.itemcoin"] = "itemcoin",
	["foxcoinPanel.subl"] = "subl", 
	["foxcoinPanel.list"] = {
		varname = "rechargeList",
		binds = {
			event = "extend",
			class = "tableview",
			props = {
				data = bindHelper.self("FoxCoinDatas"),
				item = bindHelper.self("subl"),
				cell = bindHelper.self("itemcoin"),
				columnSize =3,
				asyncPreload = 6,
				backupCached = false,
				itemAction = {isAction = true, actionTime = 0.7},
				onCell = function(list, node, csvId, v)
					node:get("icon"):texture(v.Icon)
					node:get("price"):text(v.Gia)
					text.addEffect(node:get("price"), {color = cc.c4b(106, 65, 21, 255)})
					node:get("gain"):text(v.fox)
					node:get("extraInfo"):text(v.Name)
					node:get("diamondIcon"):texture("foxcoin/foxcoin.png")
					local packId = v.PackId

					bind.touch(list, node, {clicksafe = true, methods = {ended = functools.partial(list.clickCell, packId, v)}})
				end,
			},
			handlers = {
				clickCell = bindHelper.self("onFoxCoinClickItem"),
			},
		},
	},

	["vaiPanel"] = "vaiPanel",
	-- ["vaiPanel.itemvai"] = "itemvai",
	-- ["vaiPanel.subli"] = "subli", 
	-- ["vaiPanel.list"] = {
	-- 	varname = "rechargeList",
	-- 	binds = {
	-- 		event = "extend",
	-- 		class = "tableview",
	-- 		props = {
	-- 			data = bindHelper.self("FoxCoinDatas"),
	-- 			item = bindHelper.self("subli"),
	-- 			cell = bindHelper.self("itemvai"),
	-- 			columnSize =3,
	-- 			xMargin = 30,
	-- 			asyncPreload = 6,
	-- 			backupCached = false,
	-- 			itemAction = {isAction = true, actionTime = 0.7},
	-- 			onCell = function(list, node, csvId, v)
	-- 				node:get("icon"):texture(v.Icon)
	-- 				node:get("price"):text(v.Gia)
	-- 				text.addEffect(node:get("price"), {color = cc.c4b(106, 65, 21, 255)})
	-- 				node:get("gain"):text(v.fox)
	-- 				node:get("extraInfo"):text(v.Name)
	-- 				node:get("diamondIcon"):texture("foxcoin/foxcoin.png")
	-- 				local packId = v.PackId

	-- 				bind.touch(list, node, {clicksafe = true, methods = {ended = functools.partial(list.clickCell, packId, v)}})
	-- 			end,
	-- 		},
	-- 		handlers = {
	-- 			clickCell = bindHelper.self("onFoxCoinClickItem"),
	-- 		},
	-- 	},
	-- },
	-- ["vaiPanel.list"] = {
	-- 	varname = "list",
	-- 	binds = {
	-- 		event = "extend",
	-- 		class = "tableview",
	-- 		props = {
	-- 			data = bindHelper.self("datas"),
	-- 			item = bindHelper.self("listitem"),
	-- 			cell = bindHelper.self("item"),
	-- 			columnSize = bindHelper.self("rightColumnSize"),
	-- 			asyncPreload = 9,
	-- 			itemAction = {isAction = true},
	-- 			dataOrderCmp = function(a, b)
	-- 				if a.state ~= b.state then
	-- 					return a.state < b.state
	-- 				end
	-- 				if a.sort ~= b.sort then
	-- 					return a.sort < b.sort
	-- 				end
	-- 				return a.csvId < b.csvId
	-- 			end,
	-- 			onCell = function(list, node, k, v)
	-- 				local cfg = v.cfg
	-- 				local childs = node:multiget("title", "times", "bottomPanel", "mask", "list", "item", "icon")
	-- 				childs.title:text(cfg.name)
	-- 				childs.title:x(node:width()/2)
	-- 				childs.title:y(640)
	-- 				adapt.setTextAdaptWithSize(childs.title, {size = cc.size(330, 70), vertical = "top", horizontal = "center", margin = -8, maxLine = 2})
	-- 				childs.icon:texture(cfg.icon)
	-- 				if cfg.refresh then
	-- 					childs.times:text(string.format(gLanguageCsv.directBuyGiftDailyBuy, v.leftTimes, cfg.limit))
	-- 					text.addEffect(childs.times, {color=ui.COLORS.DISABLED.YELLOW})
	-- 				else
	-- 					local hint = gLanguageCsv.directBuyGiftOnetimeBuy
	-- 					if v.status == 1 then
	-- 						hint = gLanguageCsv.directBuyGiftWeek
	-- 					elseif v.status == 2 then
	-- 						hint = gLanguageCsv.directBuyGiftMonth
	-- 					end
	-- 					childs.times:text(string.format(hint, v.leftTimes, cfg.limit))
	-- 					text.addEffect(childs.times, {color=ui.COLORS.QUALITY[4]})
	-- 				end
	-- 				childs.list:removeAllChildren()
	-- 				childs.list:setScrollBarEnabled(false)
	-- 				childs.list:setGravity(ccui.ListViewGravity.bottom)
	-- 				local idx = 0
	-- 				local len = csvSize(cfg.item)
	-- 				local dx = len == 1 and childs.item:size().width/2 or 0
	-- 				for _, itemData in ipairs(dataEasy.getItemData(cfg.item)) do
	-- 					local id = itemData.key
	-- 					local num = itemData.num
	-- 					local item = childs.item:clone():show()
	-- 					local size = item:size()
	-- 					bind.extend(list, item, {
	-- 						class = "icon_key",
	-- 						props = {
	-- 							data = {
	-- 								key = id,
	-- 								num = num,
	-- 							},
	-- 							onNode = function(node)
	-- 								node:xy(size.width/2 + dx, size.height/2)
	-- 									:scale(0.8)
	-- 							end,
	-- 						},
	-- 					})
	-- 					childs.list:pushBackCustomItem(item)
	-- 				end

	-- 				childs.list:adaptTouchEnabled()
	-- 					:setItemAlignCenter()
	-- 				local btn = childs.bottomPanel:get("btn")
	-- 				local panel = childs.bottomPanel:get("panel")
	-- 				local price = childs.bottomPanel:get("panel"):get("price")
	-- 				local rmbIcon = childs.bottomPanel:get("panel"):get("rmb")
	-- 				btn:setTouchEnabled(false)
	-- 				rmbIcon:visible(false)
	-- 				cache.setShader(btn, false, "normal")
	-- 				if v.rmb then
	-- 					local number = 40
	-- 					price:text(v.rmb)
	-- 					if type(v.rmb) ~= "string" then
	-- 						rmbIcon:visible(true)
	-- 						price:x(rmbIcon:width()/2 + price:width()/2 + rmbIcon:x())
	-- 						number = 70
	-- 					end
	-- 					childs.bottomPanel:get("panel"):width(price:width() + rmbIcon:width())
	-- 					childs.bottomPanel:get("panel"):x(btn:x() - number)
	-- 				elseif v.price then
	-- 					price:text(string.format(gLanguageCsv.symbolMoney, v.price))
	-- 				end
	-- 				local isInfo = (v.rmb and type(v.rmb) == "string") and true or false
	-- 				if v.state == STATE_TYPE.canbuy then
	-- 					childs.mask:hide()
	-- 					btn:setTouchEnabled(true)
	-- 					bind.touch(list, btn, {clicksafe = true, methods = {ended = functools.partial(list.clickCell, k, v)}})
	-- 					text.addEffect(price, {color = ui.COLORS.NORMAL.WHITE, glow={color=ui.COLORS.GLOW.WHITE}})
	-- 				else
	-- 					childs.mask:show()
	-- 					isInfo = false
	-- 					cache.setShader(btn, false, "hsl_gray")
	-- 					text.deleteAllEffect(price)
	-- 					text.addEffect(price, {color = ui.COLORS.DISABLED.WHITE})
	-- 					if v.state == STATE_TYPE.sellout then
	-- 						childs.mask:get("label"):text(gLanguageCsv.sellout)
	-- 					else
	-- 						childs.mask:get("label"):text(gLanguageCsv.nextDayRefresh5)
	-- 					end
	-- 				end
	-- 				if matchLanguage({"en"}) then
	-- 					price:anchorPoint(0.5, 0.5)
	-- 					panel:x(btn:size().width/2)
	-- 					childs.mask:get("img"):size(childs.mask:get("label"):size().width + 45, childs.mask:get("img"):size().height)
	-- 				end
	-- 				local props = {
	-- 					class = "red_hint",
	-- 					props = {
	-- 						state = isInfo,
	-- 						onNode = function(panel)
	-- 							panel:xy(355, 110)
	-- 						end,
	-- 					}
	-- 				}
	-- 				bind.extend(list, childs.bottomPanel, props)
	-- 				list:setRenderHint(0)
	-- 			end,
	-- 		},
	-- 		handlers = {
	-- 			clickCell = bindHelper.self("onBuyClick"),
	-- 		},
	-- 	},
	-- },

	["rechargePanel"] = "rechargePanel",
	["rechargePanel.item"] = "rechargeItem",
	["rechargePanel.sublist"] = "subList", 
	["rechargePanel.list"] = {
		varname = "rechargeList",
		binds = {
			event = "extend",
			class = "tableview",
			props = {
				data = bindHelper.self("rechargeDatas"),
				item = bindHelper.self("subList"),
				cell = bindHelper.self("rechargeItem"),
				columnSize =3,
				asyncPreload = 6,
				backupCached = false,
				itemAction = {isAction = true, actionTime = 0.7},
				dataOrderCmp = function(a, b)
					if a.sortValue ~= b.sortValue then
						return a.sortValue < b.sortValue
					end
					return a.csvId > b.csvId
				end,
				onCell = function(list, node, csvId, v)
					local cfg = v.cfg
					node:get("icon"):texture(cfg.icon)
					node:get("price"):text(string.format(gLanguageCsv.symbolMoney, cfg.rmbDisplay))
					text.addEffect(node:get("price"), {color = cc.c4b(106, 65, 21, 255)})
					node:get("gain"):text(cfg.rmb)
					node:get("extraInfo"):text(string.format(gLanguageCsv.rechargeFirstExtra, cfg.firstPresent))
					node:get("extraInfo"):visible(v.rechargeBuyTimes == 0 and cfg.firstPresent ~= 0)
					node:get("doublePanel"):visible(v.rechargeBuyTimes == 0 and cfg.firstPresent ~= 0)
					node:get("doublePanel.bg"):setOpacity(0)
					node:get("doublePanel.label"):text(gLanguageCsv.doubleRecharge)
					widget.addAnimation(node:get("doublePanel.bg"), "effect3q/icon_napdau.skel", "icon_napdau_loop", 10):xy(node:get("doublePanel.bg"):width() / 2, node:get("doublePanel.bg"):height() / 2)
					bind.touch(list, node, {clicksafe = true, methods = {ended = functools.partial(list.clickCell, cfg.id, v)}})
				end,
			},
			handlers = {
				clickCell = bindHelper.self("onRechargeClick"),
			},
		},
	},
	["privilegePanel"] = "privilegePanel",
	["privilegePanel.bg"] = "privilegePanelBg",
	["privilegePanel.panel"] = "privilegeItem",
	["privilegePanel.pageView"] = {
		varname = "pageView",
		binds = {
			event = "extend",
			class = "pageview",
			props = {
				data = bindHelper.self("privilegeDatas"),
				item = bindHelper.self("privilegeItem"),
				onItem = function(list, node, k, v)
					idlereasy.if_(v.show, function ()
						local richTextList = node:get("list")
						local size = richTextList:size()
						richTextList:setScrollBarEnabled(false)
						richTextList:setItemsMargin(15)
						richTextList:removeAllChildren()
						for i = 1, math.huge do
							local desc = v.vipDescCfg["desc" .. i]
							if desc == nil or desc == "" then break end
							local item = createPrivilegeLabel(desc, 40, size.width)
							richTextList:pushBackCustomItem(item)
						end
						richTextList:adaptTouchEnabled()

						node:get("name"):text(string.format("V%d %s", k, gLanguageCsv.privilege))
						uiEasy.createItemsToList(list, node:get("propList"), v.vipCfg.gift, {scale = 1, margin = 10, padding = 10})

						node:get("oldPrice"):text(v.vipCfg.oldPrice)
						node:get("line"):size(100 + node:get("oldPrice"):size().width, node:get("line"):size().height)
						node:get("price"):text(v.vipCfg.newPrice)
						bind.touch(list, node:get("btn"), {methods = {ended = functools.partial(list.clickCell, k, v)}})

						idlereasy.any({v.hasBuy, v.vipLevelEnough, v.roleLevelEnough}, function(_, hasBuy, vipLevelEnough, roleLevelEnough)
							local btn = node:get("btn")
							local label = btn:get("label")
							cache.setShader(btn, false, "hsl_gray")
							text.deleteAllEffect(label)
							text.addEffect(label, {color = ui.COLORS.DISABLED.WHITE})
							if hasBuy then
								label:text(gLanguageCsv.hasBuy)
								btn:setTouchEnabled(false)
							else
								label:text(gLanguageCsv.spaceBuy)
								btn:setTouchEnabled(true)
								if vipLevelEnough and not roleLevelEnough then
									label:text(string.format(gLanguageCsv.lvnCanBuy, v.vipCfg.giftLevelLimit))
								elseif vipLevelEnough then
									cache.setShader(btn, false, "normal")
									text.addEffect(label, {color = cc.c4b(106, 65, 21, 255)})
								end
							end
						end):anonyOnly(list, k)
					end)
				end,
				onAfterBuild = function(list)
					list.afterBuild()
				end,
			},
			handlers = {
				clickCell = bindHelper.self("onPrivilegeGiftBuy"),
				afterBuild = bindHelper.self("onPrivilegeAfterBuild"),
			},
		},
	},
	["privilegePanel.leftBtn"] = {
		binds = {
			event = "touch",
			methods = {ended = bindHelper.self("onPrivilegeLeftBtnClick")}
		}
	},
	["privilegePanel.rightBtn"] = {
		binds = {
			event = "touch",
			methods = {ended = bindHelper.self("onPrivilegeRightBtnClick")}
		}
	},
	["btnPanel.nap"] = {
		binds = {
			event = "touch",
			methods = {ended = bindHelper.self("onNapBtnClick")}
		}
	},
	["btnPanel.foxCoin"] = {
		binds = {
			event = "touch",
			methods = {ended = bindHelper.self("onfoxCoinClick")}
		}
	},
	["btnPanel.thethang"] = {
		binds = {
			event = "touch",
			methods = {ended = bindHelper.self("onTheThangBtnClick")}
		}
	},
	["btnPanel.vip"] = {
		binds = {
			event = "touch",
			methods = {ended = bindHelper.self("onVIPBtnClick")}
		}
	},
	["btnPanel.vai"] = {
		binds = {
			event = "touch",
			methods = {ended = bindHelper.self("onVaiBtnClick")}
		}
	},
	["btnPanel.foxCoin.img"] = "imgfoxCoin",
	["btnPanel.nap.img"] = "imgNap",
	["btnPanel.thethang.img"] = "imgTheThang",
	["btnPanel.vip.img"] = "imgVIP",
	["btnPanel.vai.img"] = "imgVai",
	["privilegePanel.mask"] = "mask",
	["monthPanel"] = "monthPanel",
	["monthPanel.panel1"] = "panel1",
	["monthPanel.panel2"] = "panel2",
	["monthPanel.panel1.btn"] = {
		binds = {
			event = "touch",
			clicksafe = true,
			methods = {ended = bindHelper.defer(function(view)
				return view:onBtnClick(1)
			end)}
		},
	},
	["monthPanel.panel2.btn"] = {
		binds = {
			event = "touch",
			clicksafe = true,
			methods = {ended = bindHelper.defer(function(view)
				return view:onBtnClick(2)
			end)}
		},
	},
	["monthPanel.panel1.btnGo"] = {
		varname = "btnGo1",
		binds = {
			event = "touch",
			methods = {ended = bindHelper.self("onBtnGoClick")}
		}
	},
	["monthPanel.panel2.btnGo"] = {
		varname = "btnGo2",
		binds = {
			event = "touch",
			methods = {ended = bindHelper.self("onBtnGoClick")}
		}
	},
	["btnPanel.nap.txt"] = {
		binds = {
			event = "effect",
			data = {outline = {color = cc.c4b(111, 88, 83, 255), size = 2}}
		}
	},
	["btnPanel.thethang.txt"] = {
		binds = {
			event = "effect",
			data = {outline = {color = cc.c4b(111, 88, 83, 255), size = 2}}
		}
	},
	["btnPanel.vip.txt"] = {
		binds = {
			event = "effect",
			data = {outline = {color = cc.c4b(111, 88, 83, 255), size = 2}}
		}
	},
	["btnPanel.foxCoin.txt"] = {
		binds = {
			event = "effect",
			data = {outline = {color = cc.c4b(111, 88, 83, 255), size = 2}}
		}
	},
	["btnPanel.vai.txt"] = {
		binds = {
			event = "effect",
			data = {outline = {color = cc.c4b(111, 88, 83, 255), size = 2}}
		}
	},
	["ruleBtn"] = {
		varname = "ruleBtn",
		binds = {
			event = "touch",
			methods = {ended = bindHelper.self("onRuleBtnClick")},
		},
	},

	["vaiPanel.rightPanel"] = "rightPanel",
	["vaiPanel.rightPanel.item"] = "rightItem",
	["vaiPanel.rightPanel.list"] = {
		binds = {
			event = "extend",
			class = "listview",
			props = {
				data = bindHelper.self("rightData"),
				item = bindHelper.self("rightItem"),
				showTab = bindHelper.self("showTab"),
				onItem = function(list, node, k, v)
					local normal = node:get("normal")
					local selected = node:get("selected")
					local panel
					if v.select then
						normal:hide()
						panel = selected:show()
					else
						selected:hide()
						panel = normal:show()
					end
					panel:get("name"):text(v.desc)
					if matchLanguage({"kr"}) then
						adapt.setTextScaleWithWidth(panel:get("name"), nil, 250)
					elseif matchLanguage({"en"}) then
						adapt.setTextAdaptWithSize(panel:get("name"), {size = cc.size(255, 70), vertical = "center", horizontal = "center", margin = -5, maxLine = 2})
					end
					bind.extend(list, node, {
						class = "red_hint",
						props = {
							specialTag = "activityDirectBuyGiftExternal",
							state = list.showTab:read() ~= k,
							listenData = {
								id = v.id,
								huodongID = csv.yunying.yyhuodong[v.id].huodongID,
							},
							onNode = function(panel)
								panel:xy(317, 105)
							end,
						},
					})

					bind.touch(list, normal, {methods = {ended = functools.partial(list.clickCellItem, k, v)}})
				end,
			},
			handlers = {
				clickCellItem = bindHelper.self("onLeftItemClick"),
			},
		},
	},
	["vaiPanel.item"] = "item",
	["vaiPanel.listitem"] = "listitem",
	["vaiPanel.list"] = {
		varname = "list",
		binds = {
			event = "extend",
			class = "tableview",
			props = {
				data = bindHelper.self("datas"),
				item = bindHelper.self("listitem"),
				cell = bindHelper.self("item"),
				columnSize = bindHelper.self("rightColumnSize"),
				xMargin = 130,
				asyncPreload = 9,
				itemAction = {isAction = true},
				dataOrderCmp = function(a, b)
					if a.state ~= b.state then
						return a.state < b.state
					end
					if a.sort ~= b.sort then
						return a.sort < b.sort
					end
					return a.csvId < b.csvId
				end,
				onCell = function(list, node, k, v)
					local cfg = v.cfg
					local childs = node:multiget("title", "times", "bottomPanel", "mask", "list", "item", "icon", "infoPanel")
					childs.title:text(cfg.name)
					childs.title:x(node:width()/2)
					--childs.title:y(640)
					adapt.setTextAdaptWithSize(childs.title, {size = cc.size(330, 70), vertical = "top", horizontal = "center", margin = -8, maxLine = 2})
					childs.icon:texture(cfg.icon)
					if cfg.refresh then
						childs.times:text(string.format(gLanguageCsv.directBuyGiftDailyBuy, v.leftTimes, cfg.limit))
						--text.addEffect(childs.times, {color=ui.COLORS.DISABLED.YELLOW})
					else
						local hint = gLanguageCsv.directBuyGiftOnetimeBuy
						if v.status == 1 then
							hint = gLanguageCsv.directBuyGiftWeek
						elseif v.status == 2 then
							hint = gLanguageCsv.directBuyGiftMonth
						end
						childs.times:text(string.format(hint, v.leftTimes, cfg.limit))
						--text.addEffect(childs.times, {color=ui.COLORS.QUALITY[4]})
					end
					childs.list:removeAllChildren()
					childs.list:setScrollBarEnabled(false)
					childs.list:setGravity(ccui.ListViewGravity.bottom)
					local idx = 0
					local len = csvSize(cfg.item)
					local dx = len == 1 and childs.item:size().width/2 or 0
					for _, itemData in ipairs(dataEasy.getItemData(cfg.item)) do
						local id = itemData.key
						local num = itemData.num
						local item = childs.item:clone():show()
						local size = item:size()
						bind.extend(list, item, {
							class = "icon_key",
							props = {
								data = {
									key = id,
									num = num,
								},
								onNode = function(node)
									node:xy(size.width/2 + dx, size.height/2)
										:scale(0.8)
								end,
							},
						})
						childs.list:pushBackCustomItem(item)
					end

					childs.list:adaptTouchEnabled()
						:setItemAlignCenter()
					local btn = childs.bottomPanel:get("btn")
					local panel = childs.bottomPanel:get("panel")
					local price = childs.bottomPanel:get("panel"):get("price")
					local rmbIcon = childs.bottomPanel:get("panel"):get("rmb")
					local infoPanel = childs.infoPanel
					btn:setTouchEnabled(false)
					infoPanel:setTouchEnabled(false)
					rmbIcon:visible(false)
					cache.setShader(btn, false, "normal")
					if v.rmb then
						local number = 40
						price:text(v.rmb)
						if type(v.rmb) ~= "string" then
							rmbIcon:visible(true)
							price:x(rmbIcon:width()/2 + price:width()/2 + rmbIcon:x())
							number = 70
						end
						childs.bottomPanel:get("panel"):width(price:width() + rmbIcon:width())
						childs.bottomPanel:get("panel"):x(btn:x() - number)
					elseif v.price then
						price:text(string.format(gLanguageCsv.symbolMoney, v.price))
					end
					local isInfo = (v.rmb and type(v.rmb) == "string") and true or false
					if v.state == STATE_TYPE_DIRECT.canbuy then
						childs.mask:hide()
						btn:setTouchEnabled(true)
						infoPanel:setTouchEnabled(true)
						bind.touch(list, btn, {clicksafe = true, methods = {ended = functools.partial(list.clickCell, k, v)}})
						bind.touch(list, infoPanel, {clicksafe = true, methods = {ended = functools.partial(list.clickCell1, k, v)}})
						text.addEffect(price, {color = ui.COLORS.BROWN})
					else
						childs.mask:show()
						isInfo = false
						cache.setShader(btn, false, "hsl_gray")
						text.deleteAllEffect(price)
						text.addEffect(price, {color = ui.COLORS.DISABLED.WHITE})
						if v.state == STATE_TYPE_DIRECT.sellout then
							childs.mask:get("label"):text(gLanguageCsv.sellout)
						else
							childs.mask:get("label"):text(gLanguageCsv.nextDayRefresh5)
						end
					end
					if matchLanguage({"en"}) then
						price:anchorPoint(0.5, 0.5)
						panel:x(btn:size().width/2)
						childs.mask:get("img"):size(childs.mask:get("label"):size().width + 45, childs.mask:get("img"):size().height)
					end
					local props = {
						class = "red_hint",
						props = {
							state = isInfo,
							onNode = function(panel)
								panel:xy(355, 110)
							end,
						}
					}
					bind.extend(list, childs.bottomPanel, props)
					list:setRenderHint(0)
				end,
			},
			handlers = {
				clickCell = bindHelper.self("onBuyClick"),
				clickCell1 = bindHelper.self("onInfoClick"),
			},
		},
	},
	["vaiPanel.icon404"] = "icon404",
	["vaiPanel.bg404"] = "bg404",
	--["rightBg"] = "rightBg",
	["vaiPanel.none"] = "none",
	["vaiPanel.time"] = "time",
	["buyConfirmPanel"] = "buyConfirmPanel",
}

-- params: {showPrivilege, privilegeIndex(int)}
function RechargeView:onCreate(params)
	self:enableSchedule()
	adapt.centerWithScreen("left", "right", nil, {
	--	{self.rechargeList, "width"},
	--	{self.rechargeList, "pos", "left"},
		--{self.privilegePanelBg, "width"},
	})

	params = params or {}
	gGameModel.currday_dispatch:getIdlerOrigin("vipGift"):set(true)
	self:initModel()
	gGameUI.topuiManager:createView("recharge", self, {onClose = self:createHandler("onClose")})
		:init()

	self.pageView:setTouchEnabled(false)
	self.privilegePanel:get("panel.list"):setScrollBarEnabled(false)
	self.privilegePanel:get("panel.propList"):setScrollBarEnabled(false)
	--self:setBarSize()

	-- 充值数据
	self.selectRecharge = idler.new(not (params.showPrivilege or params.privilegeIndex))
	local data = {}
	for k,v in orderCsvPairs(csv.recharges) do
		if matchLanguage(v.languages) and v.type == 1 then
			data[k] = {csvId = k, cfg = v, rechargeBuyTimes = 0}
		end
	end
	self.rechargeDatas = idlers.newWithMap(data)
	idlereasy.when(self.recharges, function(_, recharges)
		for csvId, data in self.rechargeDatas:pairs() do
			local v = data:proxy()
			v.rechargeBuyTimes = self:getRechargeBuyTimes(csvId)
			-- v.sortValue = v.cfg.sortValue + (v.rechargeBuyTimes == 0 and 100 or 0)
			v.sortValue = v.cfg.sortValue
		end
		if self.rechargeList.sortItems then
			self.rechargeList:sortItems()
		end
	end)


	----------------------FoxCoin----------------------

	self.FoxCoinDatas = idlers.newWithMap(FoxData)

	----------------------FoxCoin----------------------

	-- 进度条
	self.expProgress = idler.new(0)
	self.expProgressNum = idler.new("")

	-- 初始特权页面数据, 无指定 privilegeIndex，则显示为进入时的vip对应特权
	if params.showPrivilege or params.privilegeIndex then
		local vipGift = self.vipGift:read()
		local vipLevel = self.vipLevel:read()
		local maxNoBuyGift = math.min(vipLevel + 1, game.VIP_LIMIT)
		for i = vipLevel, 1, -1 do
			if vipGift[i] ~= 0 then
				maxNoBuyGift = i
				break
			end
		end
		self.showPrivilegeIndex = maxNoBuyGift
	else
		self.showPrivilegeIndex = self.vipLevel:read()
	end
	self.showPrivilegeIndex = cc.clampf(self.showPrivilegeIndex, 1, game.VIP_LIMIT)
	self.privilegeIndex = idler.new(self.showPrivilegeIndex)
	local data = {}
	--game.VIP_LIMIT = 18
	for i = 1, game.VIP_LIMIT do
		data[i] = {
			show = idler.new(i == self.showPrivilegeIndex),
			vipDescCfg = csv.vip_desc[i+1],
			vipCfg = gVipCsv[i],
			hasBuy = idler.new(false),
			vipLevelEnough = idler.new(false),
			roleLevelEnough = idler.new(false)
		}
	end
	self.privilegeDatas = data

	-- idler 监听触发
	idlereasy.any({self.vipLevel, self.vipSum}, function(_, vipLevel, vipSum)
		local curVip = math.min(vipLevel, game.VIP_LIMIT)
		local nextVip = curVip
		if curVip == game.VIP_LIMIT then
			self.topNormalPanel:hide()
			self.topMaxPanel:show()
			self.topMaxPanel:get("vipIcon"):texture(ui.VIP_ICON[curVip])
		else
			nextVip = curVip + 1
			self.topMaxPanel:hide()
			self.topNormalPanel:show()
			local childs = self.topNormalPanel:multiget("vipIcon1", "label2", "diamondIcon", "num", "label3", "vipIcon2", "label1")
			childs.num:text(gVipCsv[nextVip].upSum - vipSum)
			childs.vipIcon2:texture(ui.VIP_ICON[nextVip])
			if curVip < 1 then
				itertools.invoke({childs.vipIcon1}, "show")
				childs.vipIcon1:texture(ui.VIP_ICON[curVip])
				adapt.oneLinePos(childs.label1, {childs.vipIcon1, childs.label2, childs.diamondIcon, childs.num, childs.label3, childs.vipIcon2}, cc.p(10, 0))
			else
				itertools.invoke({childs.vipIcon1}, "show")
				childs.vipIcon1:texture(ui.VIP_ICON[curVip])
				adapt.oneLinePos(childs.label1, {childs.vipIcon1, childs.label2, childs.diamondIcon, childs.num, childs.label3, childs.vipIcon2}, cc.p(10, 0))
			end
		end

		-- 第一次达到尊贵Vip要求，自动弹出尊贵Vip弹窗
		if isVipDistinguishedOpen(vipSum) and not userDefault.getForeverLocalKey("rechargeVip", false)
			and self:isShowVipGift() then
			self:onVipClick()
			userDefault.setForeverLocalKey("rechargeVip", true)
		end

		--self:setBarSize()  --防止首次vip至尊时，服务器数据未及时同步
		self.expProgress:set(math.min(100 * vipSum / gVipCsv[nextVip].upSum, 100))
		self.expProgressNum:set(string.format("%d/%d", vipSum, gVipCsv[nextVip].upSum))
	end)
	idlereasy.when(self.vipLevel, function(_, vipLevel)
		for k, v in ipairs(self.privilegeDatas) do
			v.vipLevelEnough:set(vipLevel >= k)
		end
	end)
	idlereasy.when(self.roleLevel, function(_, roleLevel)
		for k, v in ipairs(self.privilegeDatas) do
			v.roleLevelEnough:set(roleLevel >= v.vipCfg.giftLevelLimit)
		end
	end)

	idlereasy.when(self.vipGift, function(_, vipGift)
		for i, v in pairs(vipGift) do
			local data = self.privilegeDatas[i]
			if data then data.hasBuy:set((v == 0) and true or false) end
		end
	end)
	idlereasy.when(self.selectRecharge, function(_, selectRecharge)
		if selectRecharge == true then
			gGameUI.topuiManager:updateTitle(gLanguageCsv.recharge, "RECHARGE")
			self.rechargePanel:show()
			self.privilegePanel:hide()
			self.monthPanel:hide()
		else
			gGameUI.topuiManager:updateTitle(gLanguageCsv.privilege, "PRIVILEGE")
			self.rechargePanel:hide()
			self.privilegePanel:show()
			self.monthPanel:hide()
		end
	end)
	idlereasy.when(self.privilegeIndex, function(_, privilegeIndex)
		privilegeIndex = cc.clampf(privilegeIndex, 1, game.VIP_LIMIT)
		self.privilegePanel:get("leftBtn"):visible(privilegeIndex > 1)
		self.privilegePanel:get("rightBtn"):visible(privilegeIndex < game.VIP_LIMIT)
		self:showPrivilegePage(privilegeIndex)
		
		local newTexture = "3q/privilege/banner4.png"
		if privilegeIndex == 3 or privilegeIndex == 4 then
			newTexture = "3q/privilege/banner2.png"
		elseif privilegeIndex == 5 then
			newTexture = "3q/privilege/vip5_skinlongca.png"
		elseif privilegeIndex == 8 then
			newTexture = "3q/privilege/banner7.png"
		elseif privilegeIndex == 10 or privilegeIndex == 11 then
			newTexture = "3q/privilege/banner1.png"
		elseif privilegeIndex == 12 then
			newTexture = "3q/privilege/vip12_skinlubohacquang.png"
		elseif privilegeIndex == 13 then
			newTexture = "3q/privilege/vip13_phuongthienhoakich.png"
		end

		local fadeOutTime = 0.15
		self.topImage:removeAllChildren()
		self.topImage:stopAllActions()
		self.topImage:runAction(cc.FadeOut:create(fadeOutTime))
		performWithDelay(self, function()
			self.pageView:setCurrentPageIndex(privilegeIndex - 1)
			self.topImage:texture(newTexture)
			local fadeInTime = 0.15
			self.topImage:runAction(cc.FadeIn:create(fadeInTime))
		end, fadeOutTime)
	end)

	self:initPrivilegeListener()

	widget.addAnimationByKey(self.btnVip, "effect3q/guizu.skel", "efc1", "effect_loop", 6)
		:alignCenter(self.btnVip:size())

	self.btnVip:setEnabled(self:isShowVipGift())

	--monthcard
	userDefault.setCurrDayKey("notShowMonthCardRedhint",true)
	local activityIds = RechargeView.getActivityIds()
	self.data = {}
	for idx, activityData in ipairs(activityIds) do
		local rechargeId = activityData.rechargeId
		local activityId = activityData.activityId
		local data = {rechargeId = rechargeId, activityId = activityId, state = idler.new(STATE_TYPE.null), day = idler.new(0)}
		self.data[idx] = data
		local rechargeCfg = csv.recharges[rechargeId]
		local yyCfg = csv.yunying.yyhuodong[activityId]
		local panel = self["panel" .. idx]
		local list = panel:get("list")
		local award = yyCfg.paramMap.award
		local maxMonthCard = yyCfg.paramMap.most
		local items = {{key = "rmb", num = award["rmb"]}}
		for key, num in csvMapPairs(award) do
			if key ~= "rmb" then
				table.insert(items, {key = key, num = num})
			end
		end

		data.privilegeId = yyCfg.paramMap.privilege
		data.list = list

		uiEasy.createItemsToList(self, list, items,{scale = 0.8, padding = 10})

		panel:get("item4.num"):text(string.format(gLanguageCsv.day, rechargeCfg.param.days))
		idlereasy.any({data.state, data.day},function(_, state, day)
			local btn = panel:get("btn")
			local btnLabel = panel:get("btn.label")
			local dayLabel = panel:get("label")
			local dayNum = panel:get("num")
			cache.setShader(btn, false, "normal")
			btn:setTouchEnabled(true)
 			text.addEffect(btnLabel, {color = ui.COLORS.NORMAL.BROWN_ALT, glow={color=ui.COLORS.GLOW.WHITE}})
			if state == STATE_TYPE.buy then
				btnLabel:text(string.format(gLanguageCsv.symbolMoney, rechargeCfg.rmbDisplay))
				btnLabel:setFontSize(60)
				btnLabel:alignCenter(btn:size())
				if day <= 0 then
					dayLabel:text(gLanguageCsv.monthCardNotHave)
					dayNum:text("")
				else
					dayLabel:text(gLanguageCsv.reveiveLeftDay)
					dayNum:text(string.format(gLanguageCsv.day, data.day:read()))
				end
				text.addEffect(panel:get("textHasNum"), {color = cc.c4b(92, 153, 112, 255)})
			elseif state == STATE_TYPE.max then
				dayLabel:text(gLanguageCsv.reveiveLeftDay)
				dayNum:text(string.format(gLanguageCsv.day, data.day:read() ))
				text.addEffect(panel:get("textHasNum"), {color = cc.c4b(92, 153, 112, 255)})
				btnLabel:text(gLanguageCsv.alreadyMax)
				btnLabel:setFontSize(50)
				btnLabel:alignCenter(btn:size())
				btn:setTouchEnabled(false)
				cache.setShader(btn, false, "hsl_gray")
				text.deleteAllEffect(btnLabel)
				text.addEffect(btnLabel, {color = ui.COLORS.NORMAL.BROWN_ALT})
				text.addEffect(panel:get("textHasNum"), {color = cc.c4b(230, 92, 92, 255)})
			end
			local day = data.day:read()
			local hasNums = math.ceil(day / 30)
			if day <= 0 then
				hasNums = 0
			end
			panel:get("textHasNum"):text(hasNums .."/"..maxMonthCard)
			adapt.oneLinePos(dayLabel, dayNum , cc.p(5, 0), "left")
			local tipText
			if hasNums == 0 then
				panel:get("textHas"):hide()
				panel:get("textHasNum"):hide()
				tipText = gLanguageCsv.monthCardAward1
			else
				panel:get("textHas"):show()
				panel:get("textHasNum"):show()
				tipText = gLanguageCsv.monthCardAwardNoToday
			end
			-- panel:removeChildByName("richText1")
			-- local richText1 = rich.createWithWidth(string.format(tipText, rechargeCfg.rmb), 34, nil, 660)
			-- 	:addTo(panel, 2, "richText1")
			-- 	:anchorPoint(0, 0.5)
			-- 	:xy(cc.p(55, 550))

			-- panel:removeChildByName("richText2")
			-- local richText2 = rich.createWithWidth(string.format(gLanguageCsv.monthCardAward2, award.rmb),  34, nil, 700)
			-- 	:addTo(panel, 2, "richText2")
			-- 	:anchorPoint(0, 0.5)
			-- 	:xy(cc.p(45, richText1:y() - richText1:height()))
			-- if not matchLanguage({"cn", "tw"}) then
			-- 	richText2:xy(cc.p(45, richText1:y() - richText1:height() - 5))
			-- end

		end)
	end
	idlereasy.when(self.yyhuodongs, function(_, yyhuodongs)
		for _, data in ipairs(self.data) do
			if yyhuodongs[data.activityId] then
				local lastday = yyhuodongs[data.activityId].lastday
				local enddate = yyhuodongs[data.activityId].enddate
				local today = tonumber(time.getTodayStrInClock())
				data.day:set(math.max(0, 1 + math.floor((time.getNumTimestamp(enddate) - time.getNumTimestamp(today)) / 24 / 3600)))
				local rechargeCfg = csv.recharges[data.rechargeId]

				local yyCfg = csv.yunying.yyhuodong[data.activityId]
				local maxMonthCard = yyCfg.paramMap.most
				if data.day:read() > rechargeCfg.param.days * (maxMonthCard - 1)  then
					data.state:set(STATE_TYPE.max)
				else
					data.state:set(STATE_TYPE.buy)
				end
			else
				data.state:set(STATE_TYPE.buy)
			end
		end
	end)

	widget.addAnimation(self.btnGo1, "effect3q/effect_thethang_thuong.skel", "effect_thuong_loop", 5):scale(2):xy(self.btnGo1:width() / 2 - 9, self.btnGo1:height() / 2 - 5)
	widget.addAnimation(self.btnGo2, "effect3q/effect_thethang_chiton.skel", "effect_chiton_loop", 5):scale(2):xy(self.btnGo2:width() / 2 - 24, self.btnGo2:height() / 2)

	local baseNode = self:getResourceNode()
	local scale=self:scale()
	transition.executeSequence(baseNode)
		:easeBegin("INOUT")
			:scaleTo(0, scale*1.2)
			:scaleTo(0.5, scale)
		:easeEnd()
		:done()

		if not (params.showPrivilege or params.privilegeIndex) then
			self:onfoxCoinClick()
		end	
		local panel = self.FoxInfo:get("x")
		local size = panel:size()
		widget.addAnimation(panel, "spine_fox.skel", "idle_loop")
			:xy(size.width/2, size.height/2-200)
			:scale(1.6)

	if params and params.vipClick then
		self:onVIPBtnClick()
	end

	-- DirectBuyGift
	local dataTab = {}

	for _,id in ipairs(self.yyOpen:read()) do
		local cfg = csv.yunying.yyhuodong[id]
		if cfg.type == game.YYHUODONG_TYPE_ENUM_TABLE.directBuyGift and (cfg.independent == 1  or cfg.independent == 2) and not cfg.clientParam.double11 then
			table.insert(dataTab, {desc = cfg.desc, id = id, sortWeight = cfg.sortWeight})
		end
	end

	table.sort(dataTab, function(a, b)
		if a.sortWeight ~= b.sortWeight then
			return a.sortWeight < b.sortWeight
		end
		return a.id < b.id
	end )
	local idx 
	self.time:hide()
	if dataTab and dataTab[1] then
		idx = dataTab[1].id
		self.icon404:visible(false)
		self.bg404:visible(false)
		self.none:hide()
		self.rightData:update(dataTab)
		self.activityId:set(idx)
		self.rightColumnSize = 3
		self.showTab:addListener(function(val, oldval, idler)
			self.rightData:atproxy(oldval).select = false
			self.rightData:atproxy(val).select = true
			local cfg = csv.yunying.yyhuodong[self.activityId:read()]
			if cfg.clientParam.isShowCountDown ~= false then
				self.time:show()
				local uiIcon = self.time:get("icon")
				local uiTimeLabel = self.time:get("title")
				local uiTime = self.time:get("time")
				ActivityView.setCountdown(self, self.activityId:read(), uiTimeLabel, uiTime, {labelChangeCb = function()
					adapt.oneLinePos(uiTime, {uiTimeLabel, uiIcon}, cc.p(5, 0), "right")
				end, tag = 1})
			else
				self.time:hide()
			end
		end)

		local timeVisibel = csv.yunying.yyhuodong[idx].clientParam
		self.time:visible(timeVisibel.isShowCountDown ~= false)

		gGameModel.currday_dispatch:getIdlerOrigin("activityDirectBuyGift"):set(true)
		-- 客户端模拟购买次数变动了通知
		self.clientBuyTimes = idler.new(true)
		self.clientBuyTimes:notify()
		idlereasy.any({self.yyhuodongs, self.clientBuyTimes, self.activityId}, function(_, yyhuodongs, clientBuyTimes, activityId)
			local huodongID = csv.yunying.yyhuodong[activityId].huodongID
			local yydata = yyhuodongs[activityId] or {}
			local stamps = yydata.stamps or {}
			local datas = {}
			for k, v in csvPairs(csv.yunying.directbuygift) do
				if v.huodongID == huodongID and self.level >= v.levelLimit then
					local state = STATE_TYPE_DIRECT.canbuy
					local buyTimes = stamps[k] or 0
					buyTimes = dataEasy.getPayClientBuyTimes("directBuyData", activityId, k, buyTimes)
					local leftTimes = math.max(0, v.limit - buyTimes)
					local status = v.status
					if leftTimes == 0 then
						if v.refresh then
							state = STATE_TYPE_DIRECT.refresh
						else
							state = STATE_TYPE_DIRECT.sellout
						end
					end
					--# 判断是免费还是钻石和钱
					local rmb, price
					if v.rmbCost == 0 then
						rmb = gLanguageCsv.freeToReceive
					elseif v.rmbCost >= 1 then
						rmb = v.rmbCost
					else
						price = csv.recharges[v.rechargeID].rmbDisplay
					end
					table.insert(datas, {csvId = k, cfg = v, state = state, buyTimes = buyTimes, leftTimes = leftTimes, price = price, rmb = rmb, status = status, sort = v.sort})
				end
			end

			if not self.isTabChange then
				dataEasy.tryCallFunc(self.list, "updatePreloadCenterIndex")
			else
				self.isTabChange = false
			end
			self.datas:update(datas)
		end)
	else
		--self.rightBg:visible(false)
		self.none:show()
	end
end

function RechargeView:initModel()
	self.recharges = gGameModel.role:getIdler("recharges")
	self.vipLevel = gGameModel.role:getIdler("vip_level")
	self.vipSum = gGameModel.role:getIdler("vip_sum")
	self.vipGift = gGameModel.role:getIdler("vip_gift")
	self.rmb = gGameModel.role:getIdler("rmb")
	self.roleLevel = gGameModel.role:getIdler("level")
	self.yyhuodongs = gGameModel.role:getIdler("yyhuodongs")
	self.yyOpen = gGameModel.role:getIdler('yy_open')
	self.level = gGameModel.role:read("level")
	self.activityId = idler.new()
	self.rightData = idlers.new()
	self.datas = idlers.new()
	self.showTab = idler.new(1)
	self.rightItem:visible(false)
	self.buyConfirmPanel:hide()
end

function RechargeView:initPrivilegeListener()
	uiEasy.addTouchOneByOne(self.mask, {ended = function(pos, dx, dy)
		if not self.selectRecharge:read() then
			if math.abs(dx) > 100 and math.abs(dx) > math.abs(dy) then
				local dir = dx > 0 and -1 or 1
				self.privilegeIndex:modify(function(val)
					val = cc.clampf(val + dir, 1, game.VIP_LIMIT)
					return true, val
				end)
			end
		end
	end})
end

function RechargeView:getRechargeBuyTimes(rechargeID)
	-- # 0 表示 没重置过，也没活动ID
	-- # >0 表示 新的重置活动ID
	-- # <0 表示 重置过的活动ID
	local recharge = self.recharges:read()[rechargeID]
	if recharge then
		local reset = recharge.reset or 0
		if reset > 0 then
			return 0
		end
		return recharge.cnt
	end
	return 0
end

function RechargeView:onPrivilegeAfterBuild(list)
	list:setCurrentPageIndex(self.showPrivilegeIndex - 1)
end

function RechargeView:onChangeClick()
	self.selectRecharge:modify(function(val)
		return true, not val
	end)
end

function RechargeView:onRechargeClick(list, rechargeId, v)
	gGameApp:payCustom(self)
		:params({rechargeId = rechargeId})
		:wait(5) -- 直充最多等待拦截5s
		:doit()
end

function RechargeView:onPrivilegeGiftBuy(list, k, v)
	local vipLevelEnough = v.vipLevelEnough:read()
	if not vipLevelEnough then
		gGameUI:showTip(gLanguageCsv.vipNotEnough)
	elseif not v.roleLevelEnough:read() then
		gGameUI:showTip(string.format(gLanguageCsv.lvnCanBuy, v.vipCfg.giftLevelLimit))
	else
		local function cb()
			local rmb = self.rmb:read()
			if rmb < v.vipCfg.newPrice then
				gGameUI:showTip(gLanguageCsv.noDiamondGoRecharge)
			else
				gGameApp:requestServer("/game/role/vipgift/buy", function(tb)
					gGameUI:showGainDisplay(tb)
				end, k)
			end
		end
		if matchLanguage({"kr"}) then
			dataEasy.sureUsingDiamonds(cb, v.vipCfg.newPrice, nil, string.format(gLanguageCsv.sureBuyVipGift, k))
		else
			gGameUI:showDialog({content = string.format(gLanguageCsv.sureBuyVipGift, k), cb = cb, btnType = 2, dialogParams = {clickClose = false}, clearFast = true})
		end
	end
end

function RechargeView:onPrivilegeLeftBtnClick()
	self.privilegeIndex:modify(function(val)
		return true, val - 1
	end)
end

function RechargeView:onPrivilegeRightBtnClick()
	self.privilegeIndex:modify(function(val)
		return true, val + 1
	end)
end

function RechargeView:showPrivilegePage(index)
	if index >= 1 and index <= game.VIP_LIMIT then
		local data = self.privilegeDatas[index]
		data.show:set(true)
		return true
	end
end

function RechargeView:isShowVipGift()
	local roleVip = gGameModel.monthly_record:read("vip")
	return  matchLanguage({"cn"}) or csvSize(gVipCsv[roleVip].monthGift) >= 1
end

function RechargeView:onVipClick()
	if matchLanguage({"cn"}) then
		gGameUI:stackUI("city.recharge_vip")
	else
		gGameUI:stackUI("city.vip_distinguished")
	end
end

-- @desc 设置bar的长度，主要是在有尊贵Vip按钮和没有的状态下长度不一样
function RechargeView:setBarSize()
	local distance = 62  -- 间距62像素
	local barBgScale = self.barBg:scale()
	local btnVipScale =  self.btnVip:scale()
	local barBgwidth = (self.topPanelBtn:x() - self.barBg:x() - self.topPanelBtn:width()/2 - distance)/barBgScale
	local vipSum = self.vipSum:read()
	if vipSum >= gCommonConfigCsv.rechargeVip and isVipDistinguishedOpen(vipSum) then
		barBgwidth = (barBgwidth*barBgScale - self.btnVip:width()*btnVipScale - distance)/barBgScale
	end
	local barPos = self.barBg:x() + barBgwidth*barBgScale/2
	self.barBg:width(barBgwidth)
	self.topBar:setContentSize(barBgwidth, self.topBar:height())
	self.topBar:x(barPos)
	self.barNum:x(barPos)
end

function RechargeView:onNapBtnClick()
	self.FoxInfo:hide()
	self.topPanel:show()
	self.foxcoinPanel:hide()
	self.vaiPanel:hide()
	self.rechargePanel:show()
	self.privilegePanel:hide()
	self.monthPanel:hide()
	self.topImage:texture("3q/privilege/banner5.png")
	self.imgNap:texture("3q/privilege/btnactive.png")
	self.imgTheThang:texture("3q/privilege/btnnormal.png")
	self.imgVIP:texture("3q/privilege/btnnormal.png")
	self.imgfoxCoin:texture("3q/privilege/btnnormal.png")
	self.imgVai:texture("3q/privilege/btnnormal.png")
end

function RechargeView:onVIPBtnClick()
	self.FoxInfo:hide()
	self.topPanel:show()
	self.foxcoinPanel:hide()
	self.vaiPanel:hide()
	self.rechargePanel:hide()
	self.privilegePanel:show()
	self.monthPanel:hide()
	self.topImage:texture("3q/privilege/banner4.png")
	self.imgNap:texture("3q/privilege/btnnormal.png")
	self.imgTheThang:texture("3q/privilege/btnnormal.png")
	self.imgVIP:texture("3q/privilege/btnactive.png")
	self.imgfoxCoin:texture("3q/privilege/btnnormal.png")
	self.imgVai:texture("3q/privilege/btnnormal.png")
end

function RechargeView:onTheThangBtnClick()
	self.FoxInfo:hide()
	self.topPanel:show()
	self.foxcoinPanel:hide()
	self.vaiPanel:hide()
	self.rechargePanel:hide()
	self.privilegePanel:hide()
	self.monthPanel:show()
	self.topImage:texture("3q/privilege/banner3.png")
	self.imgNap:texture("3q/privilege/btnnormal.png")
	self.imgTheThang:texture("3q/privilege/btnactive.png")
	self.imgVIP:texture("3q/privilege/btnnormal.png")
	self.imgfoxCoin:texture("3q/privilege/btnnormal.png")
	self.imgVai:texture("3q/privilege/btnnormal.png")
end

function RechargeView:onfoxCoinClick()
	self.FoxInfo:show()
	--self.topPanel:hide()
	self.foxcoinPanel:show()
	self.vaiPanel:hide()
	self.imgfoxCoin:texture("3q/privilege/btnactive.png")
	self.rechargePanel:hide()
	self.privilegePanel:hide()
	self.monthPanel:hide()
	self.topImage:texture("foxcoin/banner3.png")
	self.imgNap:texture("3q/privilege/btnnormal.png")
	self.imgTheThang:texture("3q/privilege/btnnormal.png")
	self.imgVIP:texture("3q/privilege/btnnormal.png")
	self.imgVai:texture("3q/privilege/btnnormal.png")
end

function RechargeView:onVaiBtnClick()
	self.FoxInfo:hide()
	self.topPanel:show()
	self.foxcoinPanel:hide()
	self.vaiPanel:show()
	self.rechargePanel:hide()
	self.privilegePanel:hide()
	self.monthPanel:hide()
	self.topImage:texture("3q/privilege/banner3.png")
	self.imgNap:texture("3q/privilege/btnnormal.png")
	self.imgTheThang:texture("3q/privilege/btnnormal.png")
	self.imgVIP:texture("3q/privilege/btnnormal.png")
	self.imgfoxCoin:texture("3q/privilege/btnnormal.png")
	self.imgVai:texture("3q/privilege/btnactive.png")

	gGameApp:requestServer("/game/yy/active/get", function(tb)
        if not self.clientBuyTimes then
            self.clientBuyTimes = idler.new(true)
        end
        
        gGameModel.currday_dispatch:getIdlerOrigin("activityDirectBuyGift"):set(true)
        
        self.clientBuyTimes:notify()
    end)
end


function RechargeView:onFoxCoinClickItem(list, rechargeId, v)
	if device.platform ~= "ios" then
		gGameUI:stackUI("okami_dev.nap", nil, nil, list, rechargeId, v)
	else
		local tmp = {
			area = gGameApp.serverInfo.name,
			level = tostring(gGameModel.role:read("level")),
			role_name = gGameModel.role:read("name"),
			vip = tostring(gGameModel.role:read("vip_level")),
			username = gGameModel.account:read("name"),
			serverId = tostring(gGameModel.role:read("area")),
			roleId = tostring(gGameModel.role:read("uid")),
			productID = rechargeId,
		}
	
		sdk.callPlatformFunc("pay", json.encode(tmp), function(info)
			print("pay ret = ", info)
			if info == "ok" then
				gGameUI:goBackInStackUI("city.view")
				gGameUI:stackUI("city.recharge", nil, {full = true})
				gGameUI:showTip(gLanguageCsv.rechargeSuccess)
			else
				gGameUI:showTip(gLanguageCsv.transactionCancelled)
			end
		end)
	end
end

function RechargeView:onBtnClick(idx)
	local data = self.data[idx]
	local state = data.state:read()
	local yyCfg = csv.yunying.yyhuodong[data.activityId]
	if state == STATE_TYPE.buy then
		gGameApp:payCustom(self)
			:params({rechargeId = data.rechargeId, yyID = data.activityId})
			:checkCanbuy()
			:serverCb(function()
				local rechargeCfg = csv.recharges[data.rechargeId]
				gGameUI:showGainDisplay({rmb = rechargeCfg.rmb}, {raw = false})
				gGameUI:showTip(gLanguageCsv.monthCardBuySuccess)
			end)
			:doit()
	end
end

function RechargeView.getActivityIds()
	local yyOpen = gGameModel.role:read("yy_open")
	local activityIds = {}
	for _, id in ipairs(yyOpen) do
		local yyCfg = csv.yunying.yyhuodong[id]
		if yyCfg.type == game.YYHUODONG_TYPE_ENUM_TABLE.monthlyCard then
			table.insert(activityIds, {rechargeId = yyCfg.paramMap.rechargeID, activityId = id})
		end
	end
	table.sort(activityIds, function(a, b)
		return a.rechargeId < b.rechargeId
	end)
	return activityIds
end

-- @desc 根据 month_card_privilege 里的 key 和 月卡激活状态获得当前加成
function RechargeView.getPrivilegeAddition(key)
	local activityIds = ActivityMonthCardView.getActivityIds()
	local ans = nil
	local yyhuodongs = gGameModel.role:read("yyhuodongs")
	for _, data in ipairs(activityIds) do
		local yyData = yyhuodongs[data.activityId]
		if yyData then
			local yyCfg = csv.yunying.yyhuodong[data.activityId]
			local hour, min = time.getHourAndMin(yyCfg.beginTime, true)
			local today = tonumber(time.getTodayStrInClock(hour, min))
			local enddate = yyData.enddate
			if today <= enddate then
				local cfg = csv.month_card_privilege[yyCfg.paramMap.privilege]
				if key == "pwNoCD" then
					if cfg[key] then
						return true
					end
				else
					ans = (ans or 0) + (tonumber(cfg[key]) or 0)
				end
			end
		end
	end
	return ans
end

function RechargeView:onBtnGoClick()
	gGameUI:stackUI("city.activity.month_card1")
end

function RechargeView:onRuleBtnClick()
	gGameUI:stackUI("common.rule", nil, nil, self:createHandler("getRuleContext"), {width = 1326})
end

function RechargeView:getRuleContext(view)
	local c = adaptContext
	local context = {
		c.clone(view.title, function(item)
			item:get("text"):text(gLanguageCsv.privilege)
		end),
		c.noteText(220038, 220044),
	}
	return context
end

function RechargeView:onLeftItemClick(list, idx, v)
	if self.activityId:read() ~= v.id then
		dataEasy.tryCallFunc(self.list, "setItemAction", {isAction = true})
		self.isTabChange = true
		self.activityId:set(v.id)
		self.showTab:set(idx)
	end
end

function RechargeView:showBuyConfirmPanel(k, v)
    local cfg = v.cfg
    local activityId = self.activityId:read()
    
    self.buyConfirmPanel:show()
    
    self.pendingPurchase = {
        k = k,
        v = v,
        activityId = activityId
    }
    
    self.buyConfirmPanel:get("title"):text(cfg.name)
    
    self.buyConfirmPanel:get("icon"):texture(cfg.icon)
    
    local timesText = ""
    if cfg.refresh then
        timesText = string.format(gLanguageCsv.directBuyGiftDailyBuy, v.leftTimes, cfg.limit)
    else
        local hint = gLanguageCsv.directBuyGiftOnetimeBuy
        if v.status == 1 then
            hint = gLanguageCsv.directBuyGiftWeek
        elseif v.status == 2 then
            hint = gLanguageCsv.directBuyGiftMonth
        end
        timesText = string.format(hint, v.leftTimes, cfg.limit)
    end
    self.buyConfirmPanel:get("times"):text(timesText)
    
    local itemList = self.buyConfirmPanel:get("list")
    itemList:removeAllChildren()
    itemList:setScrollBarEnabled(false)
    
    for _, itemData in ipairs(dataEasy.getItemData(cfg.item)) do
        local id = itemData.key
        local num = itemData.num
        local item = self.buyConfirmPanel:get("item"):clone():show()
        local size = item:size()
        
        bind.extend(self, item, {
            class = "icon_key",
            props = {
                data = {
                    key = id,
                    num = num,
                },
                onNode = function(node)
                    node:xy(size.width/2, size.height/2):scale(0.8)
                end,
            },
        })
        
        itemList:pushBackCustomItem(item)
    end
    
    itemList:adaptTouchEnabled()
    itemList:setItemAlignCenter()
    
    local price = self.buyConfirmPanel:get("bottomPanel.panel.price")
    if v.rmb then
        price:text(v.rmb)
    elseif v.price then
        price:text(string.format(gLanguageCsv.symbolMoney, v.price))
    end
    
    bind.touch(self, self.buyConfirmPanel:get("cancelBtn"), {
        methods = {ended = function()
            self.buyConfirmPanel:hide()
        end}
    })
    
    bind.touch(self, self.buyConfirmPanel:get("bottomPanel.btn"), {
        methods = {ended = function() 
            self:executePurchase()
        end}
    })
end

function RechargeView:executePurchase()
    local purchase = self.pendingPurchase
    if not purchase then return end
    
    local k = purchase.k
    local v = purchase.v
    local activityId = purchase.activityId
    
    self.buyConfirmPanel:hide()
    
    if not self.clientBuyTimes then
        self.clientBuyTimes = idler.new(true)
    end
    
    if not v.rmb then
        gGameApp:payDirect(self, {rechargeId = v.cfg.rechargeID, yyID = activityId, csvID = v.csvId, name = v.cfg.name, buyTimes = v.buyTimes}, self.clientBuyTimes)
            :serverCb(function()
                local cfg = csv.yunying.directbuygift[v.csvId]
                gGameUI:showGainDisplay(cfg.item, {raw = false})
            end)
            :doit()
    else
        gGameApp:requestServer("/game/yy/award/get", function(tb)
            self.clientBuyTimes:notify()
            local cfg = csv.yunying.directbuygift[v.csvId]
            gGameUI:showGainDisplay(cfg.item, {raw = false})
        end, activityId, v.csvId)
    end
    
    self.pendingPurchase = nil
end

function RechargeView:onBuyClick(list, k, v)
    local activityId = self.activityId:read()
    
    if not self.clientBuyTimes then
        self.clientBuyTimes = idler.new(true)
	end
    
    if not v.rmb then
        gGameApp:payDirect(self, {rechargeId = v.cfg.rechargeID, yyID = activityId, csvID = v.csvId, name = v.cfg.name, buyTimes = v.buyTimes}, self.clientBuyTimes)
            :serverCb(function()
                local cfg = csv.yunying.directbuygift[v.csvId]
                gGameUI:showGainDisplay(cfg.item, {raw = false})
            end)
            :doit()
    else
        gGameApp:requestServer("/game/yy/award/get", function(tb)
            self.clientBuyTimes:notify()
            local cfg = csv.yunying.directbuygift[v.csvId]
            gGameUI:showGainDisplay(cfg.item, {raw = false})
        end, activityId, v.csvId)
    end
end

function RechargeView:onInfoClick(list, k, v)
    self:showBuyConfirmPanel(k, v)
end

function RechargeView:onClosePanelClick()
    self.buyConfirmPanel:hide()
end

return RechargeView

