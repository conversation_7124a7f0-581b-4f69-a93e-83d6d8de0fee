-- @Date:   2023-03-10
-- @Desc:   特惠物匣

local TAB_TYPE = {
	ZIXUNA = 1,
	TUPO = 2,
}
-- 计算有多少个可选奖励总数
local function optionNums(awards)
	local count = 0
	for k,v in ipairs(awards) do
		if not v.isFisAwards then
			count = count + 1
		end
	end
	return count
end

local function bindIcon(list, node, data, touch)
	bind.extend(list, node:get("icon"), {
		class = "icon_key",
		props = {
			data = data,
			onNode = function(panel)
				panel:setTouchEnabled(touch)
			end
		},
	})
end

local function setIcon(list, value, node)
	if value.isFisAwards then
		node:get("add"):hide()
		node:get("select"):hide()
		bindIcon(list, node, dataEasy.getItemData(value.showAwards)[1], true)
	elseif value.choose and value.choose > 0 then
		node:get("add"):hide()
		node:get("select"):visible(value.canbuy)
		local award = value.showAwards[value.choose]
		bindIcon(list, node, dataEasy.getItemData(award)[1])
		bind.touch(list, node, {clicksafe = false, methods = {ended = functools.partial(list.clickCell, value, value.choose)}})
	else
		node:get("select"):hide()
		node:get("add"):show()
		node:get("icon"):hide()
		bind.touch(list, node, {clicksafe = false, methods = {ended = functools.partial(list.clickCell, value, 0)}})
	end
end


local ActivityPreferentialView = class("ActivityPreferentialView",Dialog)
ActivityPreferentialView.RESOURCE_FILENAME = "activity_preferential_goods.json"
ActivityPreferentialView.RESOURCE_BINDING = {
	["bg"] = "bg",
	["titleImage"] = "titleImage",
	["close"] = {
		binds = {
			event = "touch",
			methods = {ended = bindHelper.self("onClose")}
		},
	},
	["leftItem"] = "leftItem",
	-- ["leftItem.selected.txt"] = {
	-- 	binds = {
	-- 		event = "effect",
	-- 		data = {outline = {color = cc.c4b(36, 126, 190, 255), size = 4}},
	-- 	},
	-- },
	["leftList"] = {
		varname = "tabList",
		binds = {
			event = "extend",
			class = "listview",
			props = {
				data = bindHelper.self("tabDatas"),
				item = bindHelper.self("leftItem"),
				margin = 20,
				itemAction = {isAction = true},
				onItem = function(list, node, k, v)
					local panel = node:get("normal")
					node:get("selected"):hide()
					node:get("normal"):hide()
					if v.selected then
						panel = node:get("selected")
					end
					panel:show()
					panel:get("txt"):text(v.name)
						:scale(1)
						:setFontSize(40)
					if matchLanguage({"en", "kr"}) then
						adapt.setTextAdaptWithSize(panel:get("txt"), {size = cc.size(240, 80), horizontal = "center", vertical = "center"})
					else
						adapt.setTextScaleWithWidth(panel:get("txt"), nil, 220)
					end
					node:onClick(functools.partial(list.clickCell, k))
				end,
			},
			handlers = {
				clickCell = bindHelper.self("onTabClick"),
		   },
		}
	},
	["item"] = "item",
	["goodsList"] = {
		varname = "list",
		binds = {
			event = "extend",
			class = "listview",
			props = {
				data = bindHelper.self("itemsData"),
				item = bindHelper.self("item"),
				itemAction = {isAction = false},
				margin = 20,
				onItem = function(list, node, k, v)
					setIcon(list, v, node)
				end,
				onAfterBuild = function (list)
					list:setItemAlignCenter()
				end,
			},
			handlers = {
				clickCell = bindHelper.self("onselectClick"),
		   },
		}
	},
	["timeLabel"] = "timeLabel",
	["pricePanel"] = "pricePanel",
	["pricePanel.btnBuy"] = {
		varname = "btnBuy",
		binds = {
			event = "touch",
			methods = {ended = bindHelper.self("clickBuy")}
		},
	},
	-- ["pricePanel.btnBuy.txt"] = {
	-- 	binds = {
	-- 		event = "effect",
	-- 		data = {glow = {color = cc.c4b(255, 255, 255, 255)}},
	-- 	},
	-- },
	["goodsBg"] = "goodsBg",
}

function ActivityPreferentialView:onCreate(activityId)
	gGameModel.forever_dispatch:getIdlerOrigin("preferentialGoodsClick"):set(true)
	self.activityId = activityId
	self.huodongID = csv.yunying.yyhuodong[self.activityId].huodongID
	self:initModel()
	self:initUI()
	self:initData()
	self:initTabData()
	self:setTimeLabel()
	Dialog.onCreate(self, {blackType = 1})
end

function ActivityPreferentialView:initModel()
	self.yyhuodongs = gGameModel.role:getIdler("yyhuodongs")
	self.tabDatas = idlers.newWithMap({})
	self.itemsData = idlers.newWithMap({})
	self.showTab = idler.new(1)
	self.clientBuyTimes = idler.new(true)
	self.timeout = false
end

function ActivityPreferentialView:initTabData()
	local tabDatas = {}
	for key, data in orderCsvPairs(csv.yunying.customize_gift) do
		if data.huodongID == self.huodongID then
			--如果第一个自选有配置奖励 则为自选
			table.insert(tabDatas, {
				id = key,
				name = data.name,
				type = itertools.size(data.optionalAwards1) > 0 and TAB_TYPE.ZIXUNA or TAB_TYPE.TUPO
			})
		end
	end
	table.sort(tabDatas, function(a, b)
		return a.id < b.id
	end)
	self.tabDatas:update(tabDatas)
	self.showTab:addListener(function(val, oldval)
		for key, v in self.tabDatas:pairs() do
			if key == val then 
				self.tabDatas:atproxy(key).selected = true
				-- self.bg:get("img1"):visible(self.tabDatas:atproxy(key).type == TAB_TYPE.TUPO)
				-- self.bg:get("img2"):visible(self.tabDatas:atproxy(key).type == TAB_TYPE.ZIXUNA)
			elseif key == oldval then
				self.tabDatas:atproxy(key).selected = false
			end
		end
		self:updatePricePanel(self.itemData[val])
		self.itemsData:update(self.itemData[val].awards)
	end)
end

function ActivityPreferentialView:onTabClick(list, k)
	self.showTab:set(k)
end

function ActivityPreferentialView:onselectClick(list, val, choose)
	gGameUI:stackUI("city.activity.preferential_goods_select", nil, nil, {activityId = self.activityId, slotNums = optionNums(self.itemData[self.showTab:read()].awards), data = self.itemData[self.showTab:read()], val = val, choose})
end

function ActivityPreferentialView:updatePricePanel(data)
	self.pricePanel:get("mask"):hide()
	self.pricePanel:get("originalPricePanel.num"):text(string.format(gLanguageCsv.goodsMoney,data.rmb))
	uiEasy.setBtnShader(self.btnBuy, self.btnBuy:get("txt"), data.isCanBuy and 1 or 2)
	self.pricePanel:get("btnBuy.txt"):text(string.format(gLanguageCsv.symbolMoney,data.rmbDisplay))
	self.pricePanel:get("limitTxt"):text(string.format(gLanguageCsv.foreverLimit, data.buyTimes - data.hasBoughtNum, data.buyTimes))
	if itertools.size(data.awards) >= 5 then
		-- self.list:size(1100)
		self.goodsBg:width((self.item:width() + 35) * itertools.size(data.awards))
		self.list:x(self.pricePanel:x() - self.list:width() / 2 + 100)
	else
		-- self.list:size(900)
		self.goodsBg:width((self.item:width() + 30) * itertools.size(data.awards) + 50)
		self.list:x(self.pricePanel:x() - self.list:width() / 2)
	end
	self.goodsBg:x(self.list:x() + self.list:width() / 2 + 10)
end

function ActivityPreferentialView:initData()
	idlereasy.when(self.yyhuodongs, function (_, yyhuodong)
		self.itemData = self:initCsv(yyhuodong[self.activityId])
		self.itemsData:update(self.itemData[self.showTab:read()].awards)
		self:updatePricePanel(self.itemData[self.showTab:read()])
	end)
end

function ActivityPreferentialView:initUI()
	local textColor = {
		["activity/preferential_novice"] = cc.c4b(255, 238, 209, 255),
		["activity/preferential_double"] = cc.c4b(255, 238, 209, 255),
	}
	local outlineColor = {
		["activity/preferential_novice"] = cc.c4b(214, 30, 98, 255),
		["activity/preferential_double"] = cc.c4b(214, 30, 98, 255),
	}
	local cfg = csv.yunying.yyhuodong[self.activityId]
	if cfg and cfg.clientParam and cfg.clientParam.res then
		local res = cfg.clientParam.res
		self.bg:texture(string.format("%s/%s",res,"img_thwx.png"))
		self.bg:get("img1"):texture(string.format("%s/%s",res,"img_tpbx.png"))
		self.bg:get("img2"):texture(string.format("%s/%s",res,"img_zxbx.png"))
		self.leftItem:get("selected.bg"):texture(string.format("%s/%s",res,"btn_xz.png"))
		self.titleImage:texture(string.format("%s/%s",res,"txt_thwx.png"))
		-- if outlineColor[res] then
		-- 	text.addEffect(self.leftItem:get("selected.txt"), {outline = {color =  outlineColor[res]}})
		-- 	text.addEffect(self.timeLabel, {color =  outlineColor[res]})
		-- end	
		-- if textColor[res] then
		-- 	text.addEffect(self.leftItem:get("normal.txt"), {color = textColor[res]})
		-- end		
	end
end

function ActivityPreferentialView:initCsv(serverData)
	local itemsData = {}
	local awardsData = {}
	local optionAwards = {}
	local function getServerChoose(csvId, num)
		local idx = serverData and serverData.choose and serverData.choose[csvId] and serverData.choose[csvId][num]
		if not csv.yunying.customize_gift[csvId]["optionalAwards" .. num][idx] then
			return 0
		end
		return idx
	end

	local function getBuyTimes(csvId)
			return  serverData and serverData.stamps and serverData.stamps[csvId]
		and (serverData.stamps[csvId] > 0) and serverData.stamps[csvId]
	end

	-- 插入awards参数，isFisAwards为真，则是固定奖励，否为，可选奖励。
	-- 若为固定奖励，showAwards为特定奖励，否则为数组,包含所有可选奖励
	-- optionSlotNum 为第N个可选奖励Num,choose 为是否选中
	local function insertAwards(data, canbuy, showAwards, isFisAwards, optionSlotNum, choose)
		if itertools.size(showAwards) > 0 then
			if isFisAwards then
				for _, v in ipairs(dataEasy.getItemData(showAwards)) do
					local t = {}
					t[v.key] = v.num
					table.insert(data, {
						showAwards = t,
						isFisAwards = isFisAwards,
						optionSlotNum = optionSlotNum,
						choose = choose,
						canbuy = canbuy,
					})
				end
			else
				table.insert(data, {
					showAwards = table.deepcopy(showAwards, true),
					isFisAwards = isFisAwards,
					optionSlotNum = optionSlotNum,
					choose = choose,
					canbuy = canbuy,
				})
			end
			if not choose or choose == 0 then
				return false
			else
				return true
			end
		end
		return true
	end

	-- 从配表中获取原始购买数据，然后和服务器选中数组比对，如果数组值不为空，就标记选中
	for key, data in orderCsvPairs(csv.yunying.customize_gift) do
		if data.huodongID == self.huodongID then
			--如果第一个自选有配置奖励 则为自选
			local leftTime = data.buyTimes - (getBuyTimes(key) or 0)
			local awardsData = {}
			local hasAllChoose = true
			insertAwards(awardsData, leftTime > 0, data.awards, true)
			if itertools.size(data.optionalAwards1) > 0 then
				for i = 1, math.huge do
					local optionAward = data["optionalAwards" .. i]
					if optionAward and itertools.size(optionAward) > 0 then
						if not insertAwards(awardsData, leftTime > 0, optionAward, false, i, getServerChoose(key, i)) then
							hasAllChoose = false
						end
					else
						break
					end
				end 
			end
			table.insert(itemsData,{
				awards = awardsData,
				buyTimes = data.buyTimes, --限购次数
				name = data.name,
				rechargeID = data.rechargeID,
				rmbDisplay = csv.recharges[data.rechargeID].rmbDisplay,
				rmb = data.originalCost,
				hasBoughtNum = getBuyTimes(key) or 0,
				isCanBuy = leftTime > 0 ,
				csvId = key,
				hasAllChoose = hasAllChoose,
			})
		end
	end
	return itemsData
end

function ActivityPreferentialView:setTimeLabel()
	local endTime = gGameModel.role:read("yy_endtime")[self.activityId]
	bind.extend(self, self.timeLabel, {
		class = 'cutdown_label',
		props = {
			endTime = endTime,
			endFunc = function()
				self.timeout = true
				self.timeLabel:text(gLanguageCsv.activityOver)
			end,
			callFunc = function(t)
				self.timeLabel:text(gLanguageCsv.exclusiveRestrictionClose .. t.str)
			end,
		}
	})
end

function ActivityPreferentialView:clickBuy()
	local data = self.itemData[self.showTab:read()]
	if self.timeout then
		gGameUI:showTip(gLanguageCsv.activityOver)
		return
	end

	if not data.hasAllChoose then
		gGameUI:showTip(gLanguageCsv.selectGiftTip)
		return
	end

	gGameApp:payDirect(self, {rechargeId = data.rechargeID, yyID = self.activityId, csvID = data.csvId, buyTimes = data.hasBoughtNum, name = data.name}, self.clientBuyTimes)
		:doit()

end

return ActivityPreferentialView